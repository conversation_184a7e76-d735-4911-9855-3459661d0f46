import service from '@/utils/request'
// import qs from 'qs'
// eslint-disable-next-line
const { httpPost, httpGet } = service
const addProductComment = params => {
    return httpPost({
        url: '/materialMall/userCenter/productComment/create',
        params,
    })
}
const listCommentByProductId = params => {
    return httpPost({
        url: '/materialMall/w/productComment/listCommentByProductId',
        params,
    })
}
//登陆时查看评价
const listCommentByProductIdLogin = params => {
    return httpPost({
        url: '/materialMall/productComment/listCommentByProductId',
        params,
    })
}
// 删除评价
const deleteCommentById = params => {
    return httpGet({
        url: '/materialMall/userCenter/productComment/deleteComment',
        params,
    })
}
// 获取评价
const getCommentByOrderItemId = params => {
    return httpGet({
        url: '/materialMall/userCenter/productComment/getCommentByOrderItemId',
        params,
    })
}
//根据订单id获取订单项
const getOrderItemByOrderIds = params => {
    return httpPost({
        url: '/materialMall/userCenter/productComment/getOrderItemByOrderIds',
        params,
    })
}
// 根据订单id获取评价
const getCommentByOrderId = params => {
    return httpGet({
        url: '/materialMall/userCenter/productComment/getCommentByOrderId',
        params,
    })
}
// 修改评价
const updateComment = params => {
    return httpPost({
        url: '/materialMall/userCenter/productComment/update',
        params,
    })
}
export {
    addProductComment,
    listCommentByProductId,
    deleteCommentById,
    getCommentByOrderItemId,
    getOrderItemByOrderIds,
    getCommentByOrderId,
    updateComment,
    listCommentByProductIdLogin
}