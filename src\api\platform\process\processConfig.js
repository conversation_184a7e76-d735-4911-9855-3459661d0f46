import service from '@/utils/request'

const { httpPost, httpGet } = service

const getList = params => {
    return httpPost({
        url: '/materialMall/platform/processConfig/page',
        params
    })
}

const getProcessConfigDtlById = params => {
    return httpGet({
        url: '/materialMall/platform/processConfig/getProcessConfigDtlById',
        params
    })
}

const update = params => {
    return httpPost({
        url: '/materialMall/platform/processConfig/update',
        params
    })
}

const create = params => {
    return httpPost({
        url: '/materialMall/platform/processConfig/create',
        params
    })
}

const del = params => {
    return httpGet({
        url: '/materialMall/platform/processConfig/delete',
        params
    })
}

export {
    getList,
    getProcessConfigDtlById,
    update,
    create,
    del,
}