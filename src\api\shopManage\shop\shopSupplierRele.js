import service from '@/utils/request'
// eslint-disable-next-line no-unused-vars
const { httpPost, httpGet } = service
const selectListByEnterPriseName = params => {
    return httpGet({
        url: '/materialMall/platform/enterpriseInfo/selectListByEnterPriseName',
        params
    })
}
const getList = params => {
    return httpPost({
        url: '/materialMall/shopSupplierRele/listByShopId',
        params
    })
}
//获取供应商列表，通过条件
const getSuppliersList = params => {
    return httpPost({
        url: '/materialMall/shopSupplierRele/suppliersByCondition',
        params
    })
}

//获取自营店名称
const getEnterpriceList = params => {
    return httpPost({
        url: '/materialMall/platform/enterpriseInfo/getEnterpriseList',
        params
    })
}
const create  = params => {
    return httpPost({
        url: '/materialMall/shopSupplierRele/create',
        params
    })
}
const del  = params => {
    return httpGet({
        url: '/materialMall/shopSupplierRele/delete',
        params
    })
}
const update  = params => {
    return httpPost({
        url: '/materialMall/shopSupplierRele/update',
        params
    })
}
const batchDelete = params => {
    return httpPost({
        url: '/materialMall/shopSupplierRele/deleteBatch',
        params
    })
}
const updateByBatch = params => {
    return httpPost({
        url: '/materialMall/shopSupplierRele/updateByBatch',
        params
    })
}

/*供应商类型设置*/
const setSupplierType = params => {
    return httpPost({
        url: '/materialMall/shopSupplierRele/setSupplierType',
        params
    })
}
/*供应商群组设置*/
const setSupplierGroup = params => {
    return httpPost({
        url: '/materialMall/shopSupplierRele/setSupplierGroup',
        params
    })
}
const getSupplierFileList = id => {
    return httpPost({
        url: '/materialMall/shopSupplierRele/getFileList/' + id,
    })
}
export {
    selectListByEnterPriseName,
    getList,
    create,
    del,
    update,
    batchDelete,
    updateByBatch,
    getEnterpriceList,
    getSuppliersList,
    setSupplierType,
    setSupplierGroup,
    getSupplierFileList
}