<template>
  <div>
    <el-dropdown ref="dropdown" placement="bottom-start" trigger="click" :hide-on-click="false">
      <span class="el-dropdown-link pointer">
        {{ userAddress.detailAddress }} <i class="el-icon-arrow-down el-icon--right" />
      </span>
      <el-dropdown-menu class="dropdown-menu" slot="dropdown">
        <div class="p10">
            <div class="mb10 dfb">收货地址 <span @click="confirmAddress">确定</span></div>
            <div class="mb20">
                <el-input class="receiver" v-model="receiverName"  placeholder="请输入收货人姓名"/>
<!--                <span class="consignee">{{ receiverName }}</span>-->
                <el-select v-model="userAddress.detailAddress" placeholder="请选择收货地址" @change="addrChange">
                    <el-option
                        v-for="item in data.list"
                        :key="item.addressId"
                        :label="item.detailAddress"
                        :value="item.detailAddress"
                        @click.stop="() => {}"
                    />
                </el-select>
                <el-input class="district" v-model="district" placeholder="请输入区名"/>
            </div>
            <div class="mb10">选择新地址</div>
            <div class="mb10" style="color: red">销售价格：￥{{ userAddress.zonePrice }}</div>
            <div class="tabs dfa">
                <div v-for="province in data.address" :class="['tab', 'pointer', isActiveTab(province)]" @click="setActive(province)" :key="province.zondId">
                    {{ province.zone }} <i class="el-icon-arrow-down"/>
                </div>
            </div>
            <div class="city-pane">
                <span class="pointer mb10" v-for="city in currProvince" :key="city.zondId" @click="selectCity(city)">
                    {{ city.zone }}
                </span>
            </div>
        </div>
      </el-dropdown-menu>
    </el-dropdown>
  </div>
</template>
<script>
export default {
    name: 'addressPicker',
    props: {
        data: {
            type: Object,
            default: () => ({ address: [], list: [] }),
        },
    },
    data () {
        return {
            activeTab: this.data.address[0].zone,
            userAddress: {
                detailAddress: this.data.list[0].detailAddress,
                receiverName: this.data.list[0].receiverName,
                receiverMobile: this.data.list[0].receiverMobile,
                zonePrice: this.data.address[0].zonePrice,
                zoneId: this.data.address[0].zoneId
            },
            receiverName: this.data.list[0].receiverName,

            district: '',
            tempData: {},
            province: '',
        }
    },
    computed: {
        currProvince () {
            return this.data.address.find(item => item.zone === this.activeTab).children || []
        }
    },
    methods: {
        confirmAddress () {
            let addr = []
            let province = ''
            let city = ''
            //获取省，市
            if (this.userAddress.detailAddress.includes('省')) {
                addr = this.userAddress.detailAddress.split('省')
                province = addr[0]
                city = addr[1]
            }else if (this.userAddress.detailAddress.includes('自治区')) {
                addr = this.userAddress.detailAddress.split('自治区')
                province = addr[0]
                city = addr[1]
            }else {
                addr = this.userAddress.detailAddress.split('市')
                province = addr[0]
                city = this.userAddress.detailAddress
            }
            const zoneList = this.data.address.filter(obj => obj.zone.includes(province))
            if (zoneList.length === 0) {
                this.$emit('error', this.userAddress.detailAddress)
                return
            }else {
                const citys = zoneList[0].children.filter(obj => city.includes(obj.zone))
                if (citys.length === 0) {
                    this.$emit('error', this.userAddress.detailAddress)
                    return
                }else {
                    this.clientPop('info', '你确定配送区域为' + this.userAddress.detailAddress + '吗？', async () => {
                        this.$refs.dropdown.hide()
                        console.log(this.tempData, 'this.tempData3')
                        this.$emit('confirm', { ...this.tempData, district: this.district })
                    }, null, () => {
                        this.$refs.dropdown.hide()
                        this.userAddress = {
                            detailAddress: this.data.list[0].detailAddress,
                            receiverName: this.data.list[0].receiverName,
                            receiverMobile: this.data.list[0].receiverMobile,
                            zonePrice: this.data.address[0].zonePrice,
                            zoneId: this.data.address[0].zoneId
                        }
                        this.$emit('confirm', { ...this.userAddress, district: this.district })
                    })

                }
            }

        },
        // console.log()

        addrChange (value) {
            // this.$refs.dropdown.visible = true
            let splitor = value.includes('省') ? '省' : '自治区'
            this.$refs.dropdown.show()
            let [p, city] = value.split(splitor)
            let [c] = city.split('市')
            this.province = p
            this.district = ''
            let zone = this.setCompleteData(p + splitor, c + '市')
            this.userAddress.detailAddress = value
            this.userAddress.zonePrice = zone.zonePrice
            this.userAddress.zoneId = zone.zoneId
            this.tempData = {  ...this.userAddress }
            console.log(this.tempData, 'this.tempData1')
        },
        setCompleteData (province, city) {
            if(!this.data.address.map(item => item.zone).includes(province)) return { zondId: '', zonePrice: 0 }
            let citys = this.data.address.find(item => item.zone === province).children
            let { zoneId, zonePrice } = citys.find(item => item.zone === city)
            return { zoneId, zonePrice }
        },
        selectCity ({ zone }) {
            this.userAddress.detailAddress = this.activeTab + zone
            let { zoneId, zonePrice } = this.currProvince.find(item => item.zone === zone)
            this.userAddress.zonePrice = zonePrice
            this.userAddress.zoneId = zoneId
            this.tempData = { userAddress: this.userAddress }
            console.log(this.tempData, 'this.tempData2')
            // })

        },
        isActiveTab ({ zone }) {
            return this.activeTab === zone ? 'active-bar' : ''
        },
        setActive ({ zone }) {
            this.activeTab = zone
        },
        handleOutsideClick (event) {
            const dropdown = this.$refs.dropdown
            if(!dropdown || dropdown.contains(event.target)) return
            this.dropVisible = false
        }
    },
}
</script>

<style lang="scss" scoped>
.el-dropdown-link {
    padding: 6px 10px;
    border: 1px solid #dcdfe6;
}
.dropdown-menu {
    width: 620px !important;
    .receiver, .district {
        width: 140px;
    }
    .receiver {
        margin-right: 5px;
    }
    .consignee {
        margin-right: 30px;
    }
    .district {
        margin-left: 5px;
    }
}
.tabs {
    position: relative;
    .tab {
        padding: 6px 20px;
        border: 1px solid #c1c1c1;
        &:not(:first-of-type) {
            margin-left: 6px;
        }
    }
    .active-bar {
        color: #226fc7;
        border-bottom: 1px solid #fff !important;
    }
}
.city-pane {
    min-height: 200px;
    margin-top: -1px;
    padding: 20px 0;
    border-top: 1px solid #c1c1c1;
    // display: flex;
    // flex-wrap: wrap;
    // align-items: top;
    .pointer {
        width: 20%;
        display: inline-block;
        &:hover { color: blue; }
    }
}
.p10 {
    padding: 10px;
    .mb10:not(span) {
        font-weight: bold;
        color: #6b6f79;
        span {
            font-weight: normal;
            color: #226fc7;
            cursor: pointer;
        }
    }
}
</style>
