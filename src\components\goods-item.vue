<template>
  <div class="goods-item">
    <div>
      <img
        :src="
          itemData.productMinImg
            ? imgUrlPrefixAdd + itemData.productMinImg
            : require('@/assets/images/img/queshen5.png')
        "
        alt=""
        @click="
          openWindowTab({
            path: '/mFront/productDetail',
            query: { productId: itemData.productId },
          })
        "
      />
      <div class="name textOverflow1">{{ itemData.productName }}</div>
      <div
        class="type dfc textOverflow2"
        style="display: flex; justify-content: center; align-items: center"
      >
        {{ itemData.skuName }}
      </div>
      <div
        style="
          display: flex;
          align-items: center;
          justify-content: space-between;
          width: 100%;
        "
      >
        <div class="price" style="margin-left: 10px">
          ￥{{ itemData.sellPrice }}
        </div>
        <div style="width: 100px">
          <QuantitySelector
            v-model="itemData.goodsNum"
            @changeSelector="handleChange"
            :min="1"
            :max="100000"
          />
        </div>
        <img
          style="width: 30px; height: 30px; margin-right: 10px"
          src="../assets/images/shopping_cart.png"
          @click="handleView"
          alt=""
        />
      </div>
      <el-dialog :visible.sync="shoppingShowDialog" width="30%">
        <el-row>
          <el-col :span="12">
            <div class="tabs-title">商品信息选择</div>
          </el-col>
          <el-col :span="12">
            <span @click="shoppingShowDialog = false"
              ><i
                style="
                  cursor: pointer;
                  margin-left: 240px;
                  width: 20px;
                  height: 20px;
                  display: inline-block;
                "
                class="el-icon-close"
              ></i
            ></span>
          </el-col>
        </el-row>

        <el-form
          :model="form"
          :rules="rules"
          ref="ruleForm"
          style="margin-right: 30px; margin-top: 40px"
        >
          <el-form-item label="账期：" label-width="100px" prop="accountPeriod">
            <el-select
              v-model="form.accountPeriod"
              placeholder="请选择账期"
              style="width: 400px"
            >
              <el-option
                v-for="item in accountPeriodOption"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="区域：" label-width="100px" prop="region">
            <el-select
              v-model="form.region"
              placeholder="请选择区域"
              style="width: 400px"
              @change="changeRegion"
            >
              <el-option
                v-for="item in deliveryAreaOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button @click="shoppingShowDialog = false">取 消</el-button>
          <el-button type="primary" @click="handleAddToCart">确 定</el-button>
        </span>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import {
    addCart,
    getLogInMaterialInfo,
    getMaterialInfo,
} from '@/api/frontStage/productDetail'
import QuantitySelector from '@/components/quantity-selector'
export default {
    name: 'goodItem',
    components: {
        QuantitySelector,
    },
    props: {
        itemData: Object,
    },
    data () {
        return {
            goodsNum: 1,
            productInfo: {},
            shoppingShowDialog: false,
            showClose: true,
            form: {
                accountPeriod: '',
                region: '',
                zoneId: '',
                zoneAddr: '',
            },
            deliveryAreaOptions: [
                { label: '四川省成都市', value: 'chengdu' },
                { label: '四川省自贡市', value: 'zigong' },
                { label: '四川省攀枝花市', value: 'panzhihua' },
                { label: '四川省泸州市', value: 'luzhou' },
                { label: '四川省德阳市', value: 'deyang' },
                { label: '四川省绵阳市', value: 'mianyang' },
                { label: '四川省广元市', value: 'guangyuan' },
                { label: '四川省遂宁市', value: 'suining' },
                { label: '四川省内江市', value: 'neijiang' },
                { label: '四川省乐山市', value: 'leshan' },
                { label: '四川省南充市', value: 'nanchong' },
                { label: '四川省眉山市', value: 'meishan' },
                { label: '四川省宜宾市', value: 'yibin' },
                { label: '四川省广安市', value: 'guangan' },
                { label: '四川省达州市', value: 'dazhou' },
                { label: '四川省雅安市', value: 'yaan' },
                { label: '四川省巴中市', value: 'bazhong' },
                { label: '四川省资阳市', value: 'ziyang' },
                { label: '四川省阿坝藏族羌族自治州', value: 'aba' },
                { label: '四川省甘孜藏族自治州', value: 'ganzi' },
                { label: '四川省凉山彝族自治州', value: 'liangshan' },
            ],
            accountPeriodOption: [
                {
                    value: '1',
                    label: '1个月账期',
                },
                {
                    value: '2',
                    label: '2个月账期',
                },
                {
                    value: '3',
                    label: '3个月账期',
                },
                {
                    value: '4',
                    label: '4个月账期',
                },
                {
                    value: '5',
                    label: '5个月账期',
                },
                {
                    value: '6',
                    label: '6个月账期',
                },
            ],
            rules: {
                accountPeriod: [
                    { required: true, message: '请选择账期', trigger: 'blur' },
                ],
                region: [{ required: true, message: '请选择区域', trigger: 'blur' }],
            },
        }
    },
    mounted () {
        this.getProductInfoM()
    },

    methods: {
        handleAddToCart () {
            if (this.productInfo.shopState == 0) {
                return this.$message.error('该店铺已被冻结，无法购买商品')
            }
            this.$refs.ruleForm.validate(valid => {
                if (valid) {
                    addCart({
                        cartNum: this.goodsNum,
                        productId: this.productInfo.productId,
                        zoneId: this.form.zoneId,
                        zoneAddr: this.form.zoneAddr,
                        paymentPeriod: this.form.accountPeriod,
                    })
                        .then(res => {
                            if (res.code == 200) {
                                this.$message({
                                    message: '加入购物车成功！',
                                    type: 'success',
                                })
                                this.$bus.$emit('refreshCart')
                            }
                        })
                        .catch(() => {
                        })
                } else {
                    console.log('error submit!!')
                    return false
                }
            })

        },
        // 获取商品详情
        getProductInfoM () {
            let params = {
                productId: this.itemData.productId,
            }
            if (!localStorage.getItem('token')) {
                getMaterialInfo(params)
                    .then(res => {
                        this.productInfo = res
                    })
                    .catch(() => {
                        this.$message({
                            message: '商品不存在或商品已下架',
                            type: 'warning',
                        })
                    })
            } else {
                getLogInMaterialInfo(params)
                    .then(res => {
                        this.productInfo = res
                    })
                    .catch(() => {
                        this.$message({
                            message: '商品不存在或商品已下架',
                            type: 'warning',
                        })
                    })
            }
        },
        handleChange (goodsNum) {
            this.goodsNum = goodsNum
        },

        changeRegion (row) {
            // this.form.zoneAddr = row.zoneAddr
            // this.form.zoneId = row.zoneId
            // console.log('changeRegion', row)
            this.form.zoneId = row
        },
        handleView () {
            this.form.accountPeriod = null
            this.form.region = null
            this.shoppingShowDialog = true
        },
    },
}
</script>

<style scoped lang="scss">
@import '../assets/css/floor.scss';

.tabs-title {
  margin-left: 15px;
}

.tabs-title::before {
  content: '';
  height: 15px;
  width: 6px;
  border-radius: 2px;
  background-color: #2e61d7;
  // display: block;
  position: absolute;
  left: -2px;
  margin-top: 1px;
  margin-left: 10px;
  margin-right: 12px;
}

.fixed-width-dialog {
}
:deep(.el-button) {
  padding: 0px 20px !important;
}
:deep(.el-button--text) {
  color: #3b3c3d !important;
}
// :deep( .el-select){
//   display: block  !important;
//   position: relative;
// }
</style>