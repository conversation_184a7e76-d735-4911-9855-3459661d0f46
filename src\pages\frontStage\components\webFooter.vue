<template>
  <footer>
    <div class="content-box center dfb">
      <div class="contact-info">
        <!--                <div class="title mb20 dfa"><img src="../../../assets/images/tel.png" alt="" /><span>服务热线</span></div>
                        <div class="tel">{{deviceIndexPageServiceNumber}}</div>-->
        <div class="active-time">工作日： 9:00-17:30</div>
        <div class="record"><a href="https://beian.miit.gov.cn/">蜀ICP备2024110446号-1</a></div>
<!--        <div class="record"><a href="https://beian.mps.gov.cn/"><img :src="require('@/assets/images/img/gaztp.png')"-->
<!--                                                                     slot="suffix" width="19px" /> 川公网安备51019002006060</a>-->
<!--        </div>-->
        <div class="record">技术支持单位：四川智能建造科技股份有限公司</div>
      </div>
      <div class="navigation dfb">
        <div>
          <a @click="viewWebInfo({ path: '/mFront/simpleDetail', query: { key: 'becomeAM' } })">商家服务</a>
          <a @click="viewWebInfo({ path: '/mFront/simpleDetail', query: { key: 'becomeAM' } })">我要开店</a>
        </div>
        <div>
          <a @click="viewWebInfo({ path: '/mFront/webInfoList', query: { key: 'userAgreement' } })">平台规则</a>
          <a @click="viewWebInfo({ path: '/mFront/webInfoList', query: { key: 'userAgreement' } })">用户协议</a>
          <a @click="viewWebInfo({ path: '/mFront/webInfoList', query: { key: 'thePSR' } })">服务规则</a>
          <a @click="viewWebInfo({ path: '/mFront/webInfoList', query: { key: 'serviceGurantee' } })">服务保障</a>
          <a @click="viewWebInfo({ path: '/mFront/webInfoList', query: { key: 'integrityManagement' } })">诚信管理</a>
        </div>
        <div>
          <a @click="viewWebInfo({ path: '/mFront/simpleDetail', query: { key: 'companyProfile' } })">关于我们</a>
          <a @click="viewWebInfo({ path: '/mFront/simpleDetail', query: { key: 'companyProfile' } })">公司介绍</a>
          <a @click="viewWebInfo({ path: '/mFront/simpleDetail', query: { key: 'contactUs' } })">联系我们</a>
        </div>
        <div>
          <a @click="viewWebInfo({ path: '/mFront/webInfoList', query: { key: 'commonProblem' } })">常见问题</a>
        </div>
        <div>
          <a @click="viewWebInfo({ path: '/mFront/simpleDetail', query: { key: 'userRegisteration' } })">新手服务</a>
          <a @click="viewWebInfo({ path: '/mFront/simpleDetail', query: { key: 'userRegisteration' } })">用户注册</a>
          <!-- <a @click="viewWebInfo({ path: '/mFront/simpleDetail', query: { key: 'retrievePassword' } })">找回密码</a> -->
        </div>
        <div>
          <a @click="viewWebInfo({ path: '/mFront/simpleDetail', query: { key: 'marketCooperation' } })">市场合作</a>
        </div>
      </div>
      <div class="contact-info" style="margin-left: 30px">
        <div style="margin-top: -20px"><img
          :src="require('@/assets/images/img/weixingongzuohao.png')" slot="suffix" width="150px" height="150px" /></div>
      </div>
    </div>
  </footer>
</template>
<script>

import { getParamByCode } from '@/api/frontStage/webFooter'

export default {
    data () {
        return {
            deviceIndexPageServiceNumber: null
        }
    },
    created () {
        this.getParamByCodeM()
    },
    methods: {
        getParamByCodeM () {
            getParamByCode({ code: 'materialIndexPageServiceNumber', size: 1 }).then(res => {
                this.deviceIndexPageServiceNumber = res[0].keyValue
            })
        },
        viewWebInfo (route) {
            window.open(`${route.path}?key=${route.query.key}`, '_blank')
        }
    }
}
</script>
<style scoped lang="scss">

footer {
  height: 316px;
  background-color: #0e1926;

  .content-box {
    width: 1626px;
    height: inherit;
    padding-top: 60px;
    color: #fff;
    display: flex;
    justify-content: space-between;

    .contact-info {
      height: 200px;

      .title {
        font-size: 24px;

        img {
          width: 24px;
          height: 24px;
          margin-right: 9px;
        }
      }

      .tel {
        height: 58px;
        margin-bottom: 15px;
        font-size: 50px;
        font-family: 'DIN Alternate';
        line-height: 58px;
      }

      .active-time,
      .record {
        height: 30px;
        font-size: 18px;
        line-height: 30px;
        font-weight: lighter;
      }
    }

    .navigation {
      width: 732px;
      height: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;

      div {
        // width: 72px;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: left;
        align-items: flex-start;
      }

      a {
        margin-bottom: 20px;
        font-size: 18px;
        cursor: pointer;
      }

      a:hover {
        color: #216ec6;
      }
    }
  }
}
</style>