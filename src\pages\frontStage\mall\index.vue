<template>
  <div class="root" v-loading="showLoading">
    <pageHeader
      :pageType="pageType"
      :carouselItems="carouselItems"
      :middleItems="middleItems"
      :tabs="tabsArr"
      @onTabChange="onTabChange"
      @onTabItemChange="onTabItemChange"
    ></pageHeader>
    <div class="main" v-if="currentTab == 0">
      <template>
        <div class="titleBox dfb">
          <div class="dfa left">
            <!-- <div v-if="tabItem.className == '平台自营'">合作商家</div> -->
            <div>热门企业</div>
            <span class="ml10">精细挑选 为您选购</span>
          </div>
          <div
            class="right"
            @click="
              openWindowTab({
                path: '/mFront/shopList',
                query: { currentTab: currentTab },
              })
            "
          >
            更多<i class="el-icon-arrow-right"></i>
          </div>
        </div>
        <div class="qyList df">
          <div
            class="mb20 dfa pointer"
            v-for="item in companiesList"
            :key="item.shopId"
            @click="
              openWindowTab({
                path: '/mFront/shopIndex',
                query: { shopId: item.shopId, currentTab: currentTab },
              })
            "
          >
            <el-image
              style="width: 55px; height: 55px; margin-right: 15px"
              :src="
                item.shopImg
                  ? imgUrlPrefixAdd + item.shopImg
                  : require('@/assets/images/img/queshen5.png')
              "
              fit="cover"
              lazy
            />
            <span>{{ item.shopName }}</span>
          </div>
        </div>
      </template>
      <div v-for="(item, i) in floorGoodsList" :key="i">
        <div class="titleBox dfb">
          <div class="dfa left">
            <div>{{ item.floorName }}</div>
            <span class="ml10">{{ item.floorNameText }}</span>
          </div>
          <div
            class="right"
            @click="
              openWindowTab({
                path: '/mFront/productList',
                query: { classId: item.classId, classPath: item.className },
              })
            "
          >
            更多<i class="el-icon-arrow-right"></i>
          </div>
        </div>
        <div class="goodList df">
          <div class="floorImg">
            <el-image
              style="width: 250px; height: 620px"
              :src="
                item.backgroundUrl
                  ? imgUrlPrefixAdd + item.backgroundUrl
                  : require('@/assets/images/default_bgc.png')
              "
              fit="cover"
              lazy
            />
            <!--<div class="floorTitle">{{ item.floorName }}</div>
                        <div class="floorSubTitle">{{ item.floorNameText }}</div>-->
            <el-image
              style="
                width: 190px;
                height: 250px;
                margin-left: -95px;
                margin-top: -50%;
                z-index: 1;
                top: 50%;
                left: 50%;
              "
              :src="
                item.imgUrl
                  ? imgUrlPrefixAdd + item.imgUrl
                  : require('@/assets/images/img/queshen5.png')
              "
              fit="cover"
              lazy
            />
          </div>
          <div class="df">
            <div
              class="goodItem mb20"
              v-show="item.goodsVOS"
              v-for="(item2, i) in item.goodsVOS"
              @click="
                openWindowTab({
                  path: '/mFront/productDetail',
                  query: { productId: item2.productId },
                })
              "
              :key="i"
            >
              <el-image
                style="width: 250px; height: 200px"
                :src="
                  item2.productMinImg
                    ? imgUrlPrefixAdd + item2.productMinImg
                    : require('@/assets/images/img/queshen5.png')
                "
                fit="cover"
                lazy
              />
              <div class="name textOverflow1" style="margin-top: 12px">
                {{ item2.productName }}
              </div>
              <div class="type textOverflow2">{{ item2.skuName || '   ' }}</div>
              <div class="price">￥{{ item2.sellPrice.toFixed(2) }}</div>
              <div
                :title="item2.shopName"
                class="shopName textOverflow1"
                v-if="item2.shopName"
                @click.stop="goToShop(item2.shopId)"
              >
                {{ item2.shopName }}
              </div>
              <!-- <GoodsItem :itemData="item2"></GoodsItem> -->
            </div>
          </div>
        </div>
      </div>
      <el-empty
        v-show="floorGoodsList.length === 0"
        style="height: 500px"
        :image="emptyIcon"
        description="没有商品"
      ></el-empty>
      <!-- <div>
        <el-image
          style="width: 100%; height: 100px"
          :src="require('@/assets/images/dibu.png')"
          fit="cover"
        ></el-image>
      </div> -->

      <div class="image-container" v-if="advertShow">
        <img :src="bottomItem.pictureUrl" alt="示例图片" @click="goToLink(bottomItem.pictureLinkAddress)" class="w-full h-40 object-cover" />

        <button class="close-btn"  @click="closeHandle">
          <i class="fa fa-times el-icon-close"></i>
        </button>
      </div>
    </div>
    <MassPurchase v-show="showMass" />
    <BiddingDisplay v-show="showBidding" />
    <Notifications v-show="showNotifications" />
    <SporadicProcurement
      v-show="showSporadic"
      :ItemData="tabItem"
      :floorGoodsList="floorGoodsList"
    />
    <RevolvingMaterials
      v-show="showRevolving"
      :ItemData="tabItem"
      :floorGoodsList="floorGoodsList"
    />
  </div>
</template>
<script>
import GoodsItem from '@/components/goods-item'
import SporadicProcurement from './sporadic-procurement/index.vue'
import pageHeader from './components/pageHeader.vue'
import BiddingDisplay from './biddingDisplay/index.vue'
import MassPurchase from './massPurchase/index.vue'
import Notifications from './Notifications/index.vue'
import { getCategoryColumns } from '@/api/frontStage/floor'
import { getIndexShopList, getIndexSupplierList } from '@/api/w/indexShop'
import RevolvingMaterials from './revolving-materials/index.vue'
import { getPublishList } from '@/api/platform/content/adImg'

export default {
    name: 'mallIndex',
    components: {
        pageHeader,
        BiddingDisplay,
        MassPurchase,
        Notifications,
        SporadicProcurement,
        RevolvingMaterials,
        // eslint-disable-next-line vue/no-unused-components
        GoodsItem,
    },
    data () {
        return {
            pageType: this.$route.query.type || 'index',
            carouselItems: [],
            middleItems: [],
            bottomItem: {},
            advertShow: true,
            emptyIcon: require('@/assets/images/ico_kong.png'),
            showLoading: false,
            showBidding: false,
            showMass: false,
            showNotifications: false,
            currentTab: 0,
            tabsArr: [],
            floorGoodsList: [],
            completeData: [],
            companiesList: [],
            dynamicLength: 0,
            showSporadic: false,
            tabItem: {},
            fixedTabs: [
                { columnName: '首页', path: '/massPurchase', show: true },
                {
                    columnName: '零星采购专区',
                    path: '/massPurchase',
                    show: true,
                    hasDropdown: true,
                    dropdownItems: [
                        { name: '平台自营', path: '/massPurchase1' },
                        { name: '优先商品', path: '/massPurchase2' },
                    ],
                },
                { columnName: '大宗临购专区', path: '/massPurchase', show: true },
                {
                    columnName: '周转材料专区',
                    path: '/massPurchase',
                    show: true,
                    hasDropdown: true,
                    dropdownItems: [
                        { name: '平台自营', path: '/massPurchase3' },
                        { name: '优先商品', path: '/massPurchase4' },
                    ],
                },
                { columnName: '竞价采购', path: '/biddingDisplay', show: true },
                { columnName: '公告', path: '/mallNotifications', show: true },
            ],
        }
    },
    watch: {
        currentTab (newVal) {
            this.showNotifications = newVal - this.dynamicLength === 2
            this.showBidding = newVal - this.dynamicLength === 1
            this.showMass = newVal - (this.dynamicLength - 1) === 0
            this.showSporadic = newVal - (this.dynamicLength - 2) === 0
            this.showRevolving = newVal - this.dynamicLength === 0
        },
    },
    created () {
        this.getIndexShopListM()
        this.getCategoryColumnsM()
        this.getPublishListM()
    },
    mounted () {
        if(this.pageType === 'noticeType') {
            this.showNotifications = true
            this.showBidding = false
            this.showMass = false
            this.showSporadic = false
            this.showRevolving = false
        }
    },
    methods: {
        goToLink (url) {
            window.open(url, '_blank')
        },
        getPublishListM () {
            getPublishList({ useType: 2 }).then(res => {
                // 轮播图 - 限制为前3条数据
                const images01Urls = res
                    .filter(item => item.usePositioning !== null && item.usePositioning === 1)
                    .slice(0, 3)
                this.carouselItems = images01Urls

                // 中部广告 - 限制为前4条数据
                const images02 = res
                    .filter(item => item.usePositioning !== null && item.usePositioning === 3)
                    .slice(0, 4)
                this.middleItems = images02

                // 底部广告 - 取最新的1条数据
                const images03 = res
                    .filter(item => item.usePositioning !== null && item.usePositioning === 2)
                    .slice(0, 1)
                this.bottomItem = images03[0]
            })
        },
        closeHandle () {
            this.advertShow = false
        },
        goToShop (shopId) {
            this.openWindowTab({ path: '/mFront/shopIndex', query: { shopId } })
        },
        getIndexShopListM () {
            if (this.currentTab == 3) {
                let params = {
                    limit: 5,
                    page: 0,
                }
                getIndexSupplierList(params).then(res => {
                    this.companiesList = res.list
                })
            } else {
                getIndexShopList({ size: 12 }).then(res => (this.companiesList = res))
            }
        },
        getCategoryColumnsM () {
            //调试用、后续删除
            this.tabsArr = this.fixedTabs
            //上方代码调试用、后续删除
            this.showLoading = true
            getCategoryColumns({ size: 8 })
                .then(res => {
                    // 现在是把currentTab == 3作为标志，先注释，等前端做好零星采购专区-平台自营选项再调整，
                    // res.forEach((item, index) => {
                    //     if (item.columnName === '优选商品') {
                    //         const newItem = { ...item, columnName: '平台自选' }
                    //         res.splice(index + 1, 0, newItem)
                    //     }
                    // })
                    this.dynamicLength = res.length
                    this.completeData = res
                    this.floorGoodsList = res[0].floorVOS
                    // this.tabsArr = res.concat(this.fixedTabs)
                })
                .finally(() => {
                    this.showLoading = false
                })
        },
        // 切换tab时
        onTabChange (i) {
            var oldTab = this.currentTab
            this.currentTab = i
            // if (i > this.completeData.length - 1) return
            if (i == 0) {
                this.floorGoodsList = this.completeData[i].floorVOS
            }
            if (this.currentTab == 3) {
                this.getIndexShopListM()
            } else if (oldTab == 3 && this.currentTab != 3) {
                this.getIndexShopListM()
            }
        },
        onTabItemChange (item, index) {
            this.currentTab = index
            this.tabItem = item
            if (item.name == '平台自营') {
                this.floorGoodsList = this.completeData[1].floorVOS
            }
            if (item.name == '优先商品') {
                this.floorGoodsList = this.completeData[2].floorVOS
            }
        },
    },
}
</script>
<style scoped lang="scss">
@import '../../../assets/css/floor.scss';
div {
  line-height: 1;
}

.image-container {
  position: relative;
  width: 100%;
}

.close-btn {
  position: absolute;
  top: 0;
  right: 0;
  margin-top: 20px;
  margin-right: 25px;
  transform: translate(25%, -25%);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  background-color: rgba(0, 0, 0, 0.4);
  border-radius: 9999px;
//   border: 0.1px solid #000;
//   box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 0 0 4px rgba(0, 0, 0, 0.2);
  z-index: 10;
  transition: all 0.3s ease;
  cursor: pointer;
}

.close-btn:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.close-btn i {
  font-size: 1.125rem;
  color: #f5f5f5;
}

.root {
  width: 100%;
  // padding-bottom: 20px;
  background: #f5f5f5;
}

.main {
  width: 1326px;
  margin: 0 auto;
}
.goodItem {
  position: relative;
  .price {
    width: 100%;
    text-align: right;
    padding-right: 12px;
  }
}
.shopName {
  position: absolute;
  left: 12px;
  max-width: 90px;
  bottom: 10px;
  font-size: 14px;
  color: gray;
}
</style>
