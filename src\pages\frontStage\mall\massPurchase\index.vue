<template>
    <div class="main center front">
        <div v-for="(item, i) in floorData" :key="i">
            <div class="titleBox dfb">
                <div class="dfa left">
                    <div>{{ item.floorName }}</div>
                    <span class="ml10">{{ item.floorNameText }}</span>
                </div>
                <div class="right" @click="openWindowTab({ path: '/mFront/productList', query: { classId: item.classId, classPath: item.className }})">
                    更多<i class="el-icon-arrow-right"></i>
                </div>
            </div>
            <div class="goodList df mb20">
                <div class="floorImg">
                    <el-image
                        style="width: 250px;height: 620px;"
                        :src="item.backgroundUrl ? imgUrlPrefixAdd + item.backgroundUrl : require('@/assets/images/default_bgc.png')"
                        fit="cover"
                        alt=""
                    />
                    <!--<div class="floorTitle">{{ item.floorName }}</div>
                    <div class="floorSubTitle">{{ item.floorNameText }}</div>-->
                    <el-image
                        style="width: 190px;height: 250px;margin-left: -95px;margin-top: -50%;z-index: 1;top: 50%;left: 50%;"
                        :src="item.imgUrl ? imgUrlPrefixAdd + item.imgUrl : require('@/assets/images/img/queshen5.png')" alt=""
                        fit="cover"
                    />
                </div>
                <div class="df">
                    <div class="goodItem mb20" v-show="item.goodsVOS" v-for="(item2, i) in item.goodsVOS"
                         @click="openWindowTab({path:'/mFront/productDetail',query:{productId: item2.productId}})"
                         :key="i">
                        <img
                            :src="item2.productMinImg ? imgUrlPrefixAdd + item2.productMinImg : require('@/assets/images/img/queshen5.png')"
                            alt="">
                        <div class="name textOverflow1">{{ item2.productName }}</div>
                        <div class="type dfc textOverflow2">{{ item2.skuName }}</div>
                        <div class="price">￥{{ item2.sellPrice }}</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="userCenter mb20" v-if="topList.length > 0">
            <div class="list-title dfa">
                大宗临供各地区参考价信息栏
                <div class="right pointer" @click="openWindowTab('/mFront/massPriceList')">更多<i class="el-icon-arrow-right"></i></div>
            </div>
            <div class="p20">
                <div class="dfa mb20" v-for="item in topList" :key="item.id">
                    <span class="date">{{ item.gmtRelease | trimData }}</span>
                    <span class="tit pointer" @click="handleView(item)">{{ item.title }}</span>
                </div>
            </div>
        </div>
        <div class="userCenter" v-if="isLoggedIn">
            <div class="list-title dfa">
                历史交易价格
            </div>
            <div class="p20">
                <el-table
                    ref="scrollTable"
                    :data="tableData"
                    :header-cell-style="{ height: '50px', fontSize: '16px', color: '#333', backgroundColor: '#fafafa' }"
                    :cell-style="{ fontSize: '14px', height: '60px' }"
                    style="border: 1px solid #EBEEF5;"
                    height="550"
                    @mouseenter.native="autoScroll(true)"
                    @mouseleave.native="() => autoScroll()"
                >
                    <el-table-column label="时间" align="center" width="130">
                        <template v-slot="scope">
                            {{ scope.row.buyTime.substr(0, 10) || '' }}
                        </template>
                    </el-table-column>
                    <el-table-column label="商品名称" prop="productName" align="center"/>
                    <el-table-column label="规格" prop="skuName" align="center" width="130"/>
                    <el-table-column label="材质" prop="texture" align="center" width="110"/>
                    <el-table-column label="送货区域" prop="receiverAddress" align="center"/>
                    <el-table-column label="供货价格 (元)" prop="supplyPrice" align="center" width="240"/>
                    <el-table-column label="综合单价（元）" prop="productPrice" align="center" width="200"/>
                    <el-table-column label="单位" prop="unit" align="center" width="100"/>
                </el-table>
            </div>
        </div>
    </div>
</template>

<script>
import { getWebInfo, getOrderHistory } from '@/api/frontStage/webInfo'
import { getFixedFloor } from '@/api/frontStage/floor'
import { showLoading, hideLoading } from '@/utils/common'
import { mapState } from 'vuex'
export default {
    name: 'index',
    data () {
        return {
            topList: [],
            floorData: {},
            list: [],
            tableData: [],
            scrollTimer: '',
            scrollPixel: 2,
            topListPage: {
                currPage: 1,
                orderBy: 4,
                state: 1,
                programaKey: 'LcPriceAnnouncement',
            },
            mallType: 0,
        }
    },
    filters: {
        trimData (str) {
            return str.substr(0, 10)
        },
    },
    computed: {
        ...mapState(['userInfo']),
        isLoggedIn () {
            return this.userInfo.token || localStorage.getItem('token')
        },
    },
    methods: {
        leaveTable () {
            this.autoScroll()
        },
        handleView ({ contentId }) {
            this.openWindowTab({ path: '/mFront/newsDetail', query: { id: contentId } })
        },
        getList () {
            if(!this.isLoggedIn) return
            showLoading()
            let params = {
                limit: 3,
                mallType: this.mallType,
                ...this.topListPage
            }
            getWebInfo(params).then(res => {
                this.topList = res.list
            }).finally(() => hideLoading())
        },
        getHistory () {
            if(!this.isLoggedIn) return
            getOrderHistory({ limit: 16, page: 1, productType: 13 }).then(res => {
                this.list = res.list
                this.tableData = res.list
            })
        },
        getFloor () {
            // if(!this.userInfo.token) return
            getFixedFloor({ isFixed: 1, size: 8 }).then(res => this.floorData = res[0].floorVOS)
        },
        autoScroll (stop) {
            if(!this.isLoggedIn) return
            let table = this.$refs.scrollTable
            let wrapper = table.$refs.bodyWrapper

            if(stop) {
                clearInterval(this.scrollTimer)
            }else{
                this.scrollTimer = setInterval(() => {
                    wrapper.scrollTop += this.scrollPixel
                    if(wrapper.clientHeight + wrapper.scrollTop === wrapper.scrollHeight || wrapper.scrollTop === 0) {
                        this.scrollPixel = -this.scrollPixel
                    }
                }, 150)
            }
        },
    },
    created () {
        this.getList()
        this.getFloor()
        this.getHistory()
    },
    mounted () {
        this.autoScroll()
    },
    beforeDestroy () {
        this.autoScroll(true)
    }
}
</script>

<style scoped lang="scss">
@import "../../../../assets/css/floor.scss";
.userCenter {
    background-color: #fff;
    .right {
        font-size: 14px;
        position: absolute;
        right: 20px;
    }
    .date {
        margin-right: 30px;
        color: #666666;
    }
    .tit {
        color: #1b7cea;
    }
}
.main {
    width: 1326px;
    padding: 20px 0;
    .title .left {
        font-size: 22px;
    }
    .box {
        background-color: #fff;
        .list-item {
            height: 60px;
            border-bottom: 1px solid rgba(230, 230, 230, 1);
            &>div {height: 100%;}
            .date {
                width: 126px;
                padding: 20px 0 0 30px;
                font-size: 14px;
                color: rgba(153, 153, 153, 1);
            }
            .content {
                padding-top: 20px;
                p{
                    font-size: 18px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    display: -webkit-box;
                    -webkit-line-clamp: 2;
                    ////能够显示的行数，超出部分用...表示
                    //-webkit-box-orient: vertical;
                }
            }
            h3 {
                font-size: 18px;
                font-weight: 500;
                margin-bottom: 10px;
                cursor: pointer;
                &:hover {color: rgba(33, 110, 198, 1);}
            }
            p {
                width: 1030px;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                line-clamp: 2;
                color: rgba(153, 153, 153, 1);
            }
        }
    }
}
</style>