<template>
    <!-- 商品详情页 -->
    <div class="root">
        <div v-show="showProduct" v-loading="showProductLo">
            <div class="breadcrumb mb10 center">
                <span class="router-usher" @click="$router.push(lastObj.from)">
                    <a>{{ lastObj.title }}</a>
                </span>
                <span
                    class="router-usher"
                    @click="goToListByClass()"
                    :key="i" v-for="(item, i) in productInfo.classPathVOS"
                >
                    ><a>{{ item.className }}</a></span>
            </div>
            <div class="main">
                <div class="baseInfo dfb">
                    <div class="imgBox">
                        <div class="bigImg" :style="productInfo.state == 2?{position: 'relative'}:''">
                            <pic-zoom :url="imgUrlPrefixAdd + imgArr[imgCurrent]" :scale="2"  fit="contain"></pic-zoom>
                            <div class="disableCover" v-if="productInfo.state == 2">
                                <div>已下架</div>
                            </div>
                        </div>
                        <div class="imgList dfb">
                            <i class="el-icon-arrow-left" @click="up"></i>
                            <div class="imgArr" ref="imgBox">
                                <img :style="{ border: imgCurrent==i?'1px solid gray':'' }"
                                     :src="item ? imgUrlPrefixAdd + item : require('@/assets/images/img/queshen3.png')"
                                     ref="img"
                                     v-for="(item, i) in imgArr" @mouseover="(imgCurrent = i)" :key="i" alt=""/>
                            </div>
                            <i class="el-icon-arrow-right" @click="next"></i>
                        </div>
                    </div>
                    <!-- ======== -->
                    <div class="baseInfo_center">
                        <div class="title">
                            {{ productInfo.productName }}
                        </div>
                        <div class="ls_title textOverflow1">
                            {{ productInfo.relevanceName }}
                        </div>
                        <!-- <div class="priceBox mt10 dfa">
                            <div v-if="showDevFunc && productInfo.productType === 1">参考价</div>
                            <div v-else>价格</div>
                            <span>￥{{ fixed2(productInfo.sellPrice) }}/
                               <span> {{ productInfo.unit }}</span>
                            </span>
                        </div> -->
                        <!-- 增加商品不含税价展示 -->
                        <div class="row dfa mt20">
                            <div class="row_left">不含税价</div>
                            <span>￥{{ price }}/{{ productInfo.unit }}</span>
                        </div>
                        <div class="row dfa mt20">
                            <div class="row_left">含税价</div>
                            <span>￥{{ taxInPrice }}/{{ productInfo.unit }}</span>
                            <span v-if="productInfo.priceType == 0">(一口价)</span>
                            <span v-else-if="productInfo.priceType == 1">(参考价)</span>
                        </div>
                        <div class="row dfa mt20">
                            <div class="row_left">商品编码</div>
                            <div class="row_right">{{ productInfo.serialNum }}</div>
                        </div>
                        <div class="row dfa mt20">
                            <div class="row_left">规格型号</div>
                            <div class="row_right">{{ productInfo.skuName }}</div>
                        </div>
                        <div class="row dfa mt20">
                            <div class="row_left">剩余库存</div>
                            <div class="row_right">{{ productInfo.stock }}</div>
                        </div>
                        <div class="row dfa mt20">
                            <div class="row_left">销量</div>
                            <div class="row_right">{{ productInfo.soldNum }}</div>
                        </div>
                        <div class="row dfa mt20" v-if="showDevFunc">
                            <div class="row_left">配送至</div>
                            <addressPicker
                                :data="{ address: productInfo.zones, list: productInfo.addresseList || [] }"
                                @confirm="confirm"
                                @error="onAddrError"
                            />
                        </div>
                        <div class="row dfa mt20" v-if="productInfo.secondUnit == null || productInfo.secondUnit == ''">
                            <div class="row_left">数量</div>
                            <div class="row_right">
                                <el-input-number v-model="num"  @change="handleMinNum" :min="0.01" :max="productInfo.stock"
                                                 label="描述文字"></el-input-number>
                                <span>
                                    {{productInfo.unit}}
                                </span>
                            </div>
                        </div>
                        <div class="row dfa mt20" v-if="productInfo.secondUnit != null && productInfo.secondUnit != ''">
                            <div class="row_left">数量</div>
                            <div class="row_right">
                                <el-input-number v-model="num"  @change="handleMinNum" :min="0.01" :max="fixed4(productInfo.stock)"
                                                 label="描述文字"></el-input-number>
                                <span v-if="productInfo.secondUnit!='' && productInfo.secondUnitNum>0">
                                    {{productInfo.unit}}
                                    共计
                                {{fixed4(num * productInfo.secondUnitNum)}}
                                    {{productInfo.secondUnit}}
                                </span>
                            </div>
                        </div>

<!--                        <div class="row dfa mt20" v-if="productInfo.secondUnit != null && productInfo.secondUnit != ''">-->
<!--                            <div class="row_left">数量</div>-->
<!--                            <div class="row_right">-->
<!--                                <el-input-number v-model="num"  @change="handleMinNum" :min="0.01" :max="fixed4(productInfo.stock * productInfo.secondUnitNum)"-->
<!--                                                 label="描述文字"></el-input-number>-->
<!--                                <span v-if="productInfo.secondUnit!='' && productInfo.secondUnitNum>0">-->
<!--                                    {{productInfo.secondUnit}}-->
<!--                                    共计-->
<!--                                {{fixed4(num / productInfo.secondUnitNum)}}-->
<!--                                    {{productInfo.unit}}-->
<!--                                </span>-->
<!--                            </div>-->
<!--                        </div>-->
                        <!-- <div v-if="showDevFunc && productInfo.productType === 1" class="row dfa mt20">
                            <div class="row_left">材质</div>
                            <div class="row_right">{{ productInfo.productTexture }}</div>
                        </div> -->
                        <!-- 增加商品材质展示，不分商品类型 -->
                        <div class="row dfa mt20">
                            <div class="row_left">材质</div>
                            <div class="row_right">{{ productInfo.productTexture }}</div>
                        </div>
                        <!-- <div class="row dfa mt20">
                            <div class="row_left">区域</div>
                            <div class="row_right">{{ productInfo.detailedAddress }}</div>
                        </div> -->
                        <div class="row dfa mt20">
                            <div class="row_left">品牌</div>
                            <div class="row_right">{{ productInfo.brandName }}</div>
                        </div>
                        <!-- 配送区域：添加商品时需要添加的字段 -->
                        <div class="row dfa mt20">
                            <div class="row_left">配送区域</div>
                            <div class="row_right">
                                <el-select v-model="area" @change="handleQyChange">
                                    <el-option
                                        v-for="item in regionOption"
                                        :key="item.regionPriceId"
                                        :label="item.area"
                                        :value="item.regionPriceId">
                                    </el-option>
                                </el-select>
                            </div>
                        </div>
                        <!-- 账期字段：大宗、周转是一口价展示 -->
                        <div class="row dfa mt20" v-if="productInfo.priceType == 0">
                            <div class="row_left">账期</div>
                            <div class="row_right">
                                <el-select v-model="accountPeriod" @change="handleZqChange">
                                    <el-option
                                        v-for="item in accountPeriodOptions"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value">
                                    </el-option>
                                </el-select>
                            </div>
                        </div>
                        <!-- 先款后货：同账期 -->
                        <div class="row dfa mt20" v-if="productInfo.priceType == 0">
                            <div class="row_left">先款后货</div>
                            <div class="row_right">{{ xiankuanhouhuo }}</div>
                        </div>
                        <div class="row dfa mt20">
                            <div class="row_left">税率</div>
                            <div class="row_right">{{ productInfo.taxRate }}%</div>
                        </div>
                        <div class="row dfa mt20">
                            <div class="row_left">年化率</div>
                            <div class="row_right">{{ productInfo.annualizedRate }}%</div>
                        </div>
                        <div class="row dfa mt20">
                            <div class="row_left">加成率</div>
                            <div class="row_right">{{ productInfo.markUpNum }}%</div>
                        </div>
                        <div class="row dfa mt20" v-if="productInfo.leaveFactory != null">
                            <div class="row_left">出厂年份</div>
                            <div class="row_right">{{ productInfo.leaveFactory }}</div>
                        </div>
                        <div class="row dfa mt20">
                            <div class="row_left">服务支持</div>
                            <div class="row_right">
                                <span v-if="productInfo.isInternalSettlement == 1">可路桥结算
                                </span>
                                <!--                                <span v-if="productInfo.productTransportType == 0">-->
                                <!--                                    商家包邮-->
                                <!--                                </span>-->
                            </div>
                        </div>
                        <div class="row dfa mt20">
                            <div class="row_left">查询号码</div>
                            <div class="row_right dfa">
                                {{ productInfo.contactNumber }}
                                <div class="div1" @click="getTel">点击查询</div>
                            </div>
                        </div>
                        <div class="btns dfb mt30">
                            <div style="background: rgba(212, 48, 48, 1);" @click="handlePurchase">购买</div>
                            <div @click="handleAddToCart">加入购物车</div>
                            <div v-if="isCollectState==0" @click="addCollect() ">关注</div>
                            <div v-else-if="isCollectState==1" @click="addCollect()">取消关注</div>
                        </div>
                    </div>
                    <div class="baseInfoRight">
                        <SimpleShopDetail v-if="productInfo.shopId" :shopId="productInfo.shopId"></SimpleShopDetail>
                        <!--                        <div class="title">店铺信息</div>-->
                        <!--                        <img src="@/assets/images/img/1668497461298.jpg" alt="" />-->
                        <!--                        <div class="store textOverflow1">徐州凯雷机械店徐州凯雷机械店徐州凯雷机械店徐州凯雷机械店</div>-->
                        <!--                        <div class="cell dfa">-->
                        <!--                            <div class="cell_left">店铺地址</div>-->
                        <!--                            <div class="cell_right textOverflow1">山西省晋中市寿阳县</div>-->
                        <!--                        </div>-->
                        <!--                        <div class="cell dfa">-->
                        <!--                            <div class="cell_left">营业时间</div>-->
                        <!--                            <div class="cell_right textOverflow1">2022-10-27 19:03:20</div>-->
                        <!--                        </div>-->
                        <!--                        <div class="cell dfa">-->
                        <!--                            <div class="cell_left">综合服务</div>-->
                        <!--                            <div class="cell_right">-->
                        <!--                                <el-rate v-model="value1"></el-rate>-->
                        <!--                            </div>-->
                        <!--                        </div>-->
                    </div>
                </div>
                <!-- =========== -->
                <div class="detailBox">
                    <div class="tabBox dfa">
                        <div @click="tabcurrent1 = 0" :class="[tabcurrent1 == 0 ? 'tabab' : '']">
                            商品详情
                        </div>
                        <!--                        <div @click="tabcurrent1 = 1" :class="[tabcurrent1 == 1 ? 'tabab' : '']">-->
                        <!--                            供应商介绍66-->
                        <!--                        </div>-->
                        <div @click="tabcurrent1 = 2" :class="[tabcurrent1 == 2 ? 'tabab' : '']">
                            商品评价（{{ (pagination.totalNum > 999) ? '999+' : pagination.totalNum }}）
                        </div>
                        <div @click="tabcurrent1 = 3" :class="[tabcurrent1 == 3 ? 'tabab' : '']">
                            成交记录
                        </div>
                    </div>
                    <!--                    <div class="titleBox dfa">-->
                    <!--                        <div></div>-->
                    <!--                        <span>商品详情</span>-->
                    <!--                    </div>-->
                    <div v-show="tabcurrent1 === 0" class="description center p20" v-html="productInfo.productDescribe">
                    </div>
                    <!--                                        <div class="titleBox dfa" style="margin-top: 20px">
                                                                <div></div>
                                                                <span>供应商介绍</span>
                                                            </div>
                                                            <div class="descriptionsBox">
                                                                <el-descriptions class="margin-top" title="" :column="2" border>
                                                                    <el-descriptions-item>
                                                                        <template slot="label"> 企业名称 </template>
                                                                        kooriookami
                                                                    </el-descriptions-item>
                                                                    <el-descriptions-item>
                                                                        <template slot="label"> 主营业务 </template>
                                                                        18100000000
                                                                    </el-descriptions-item>
                                                                    <el-descriptions-item>
                                                                        <template slot="label"> 企业类型 </template>
                                                                        苏州市
                                                                    </el-descriptions-item>
                                                                    <el-descriptions-item>
                                                                        <template slot="label"> 联系人 </template>
                                                                        张三
                                                                    </el-descriptions-item>
                                                                    <el-descriptions-item>
                                                                        <template slot="label"> 注册资本 </template>
                                                                        江苏省苏州市吴中区吴中大道 1188 号
                                                                    </el-descriptions-item>
                                                                    <el-descriptions-item>
                                                                        <template slot="label"> 联系电话 </template>
                                                                        18966655555
                                                                    </el-descriptions-item>
                                                                    <el-descriptions-item>
                                                                        <template slot="label"> 注册地址 </template>
                                                                        江苏省苏州市吴中区吴中大道 1188 号
                                                                    </el-descriptions-item>
                                                                </el-descriptions>
                                                            </div>-->
                    <div class="review" v-show="tabcurrent1 === 2">
                        <div class="reviewTitle dfa">
                            <div class="dfa">全部评价</div>
                        </div>
                        <div class="reviewList">
                            <div class="reviewItem" v-for="(item, i) in reviewList" :key="i">
                                <div class="reviewTop mb20 df">
                                    <img
                                        :src="item.avatar?imgUrlPrefixAdd+item.avatar:require('@/assets/images/img/queshen1.png')"
                                        alt="" class="avatar">
                                    <div class="user">
                                        <div>{{ item.username }}</div>
                                        <div v-if="isLogin">***&nbsp;￥{{ productInfo.sellPrice }}/台</div>
                                        <div v-else>{{ productInfo.productName }}&nbsp;￥{{
                                                productInfo.sellPrice
                                            }}/台
                                        </div>
                                    </div>
                                </div>
                                <p>{{ item.text }}</p>
                                <div class="imgs df" v-if="item.images.length">
                                    <img :src="url?imgUrlPrefixAdd+url:require('@/assets/images/img/queshen5.png')"
                                         alt="" v-for="url in item.images" :key="url"/>
                                </div>
                            </div>
                        </div>
                        <!--  商品评价结束  -->
                        <pagination :currentPage.sync="pagination.currPage" :destination="pagination.destination"
                                    :pageSize="pagination.pageSize"
                                    :total="pagination.totalNum" :totalPage="pagination.totalPage"
                                    @currentChange="currentChange"
                                    @sizeChange="sizeChange">
                        </pagination>
                    </div>
                    <div class="review" v-show="tabcurrent1 === 3">
                        <div class="reviewTitle dfa">
                            <div class="dfa">成交记录</div>
                        </div>
                        <div class="reviewList">
                            <div class="historyItem dfb" v-for="(item, i) in historyList" :key="i">
<!--                                <div class="user dfa">-->
<!--                                    <img class="avatar"-->
<!--                                         :src="item.userImg ? imgUrlPrefixAdd+item.userImg:require('@/assets/images/img/queshen5.png')"/>-->
<!--                                    <div>{{ item.nickName }}</div>-->
<!--                                </div>-->
                                <div class="product">
                                    <div>{{ item.productName }}</div>
                                    <div>￥{{ item.productPrice }}/{{ productInfo.unit }}</div>
                                </div>
                                <div class="numbers">{{ item.buyCounts }}{{ productInfo.unit }}</div>
                                <div class="finishTime">
                                    <div>{{ item.flishTime }}</div>
                                    <!--                                <div>{{ item.flishTime.split(' ')[1] }}</div>-->
                                </div>
                            </div>
                        </div>
                        <!--  商品评价结束  -->
                        <pagination style="justify-content: right;" :currentPage.sync="pagination2.currPage"
                                    :destination="pagination2.destination" :pageSize="pagination2.pageSize"
                                    :total="pagination2.totalNum" :totalPage="pagination2.totalPage"
                                    @currentChange="currentChange2" @sizeChange="sizeChange2">
                        </pagination>
                    </div>
                </div>
            </div>
        </div>
        <div class="noProduct dfc" v-show="!showProduct">
            <img src="@/assets/images/ico_kong.png" alt="">
            <div>{{ noProductText }}!</div>
        </div>
    </div>
</template>
<script>
import { toFixed } from '@/utils/common'
import pagination from '@/pages/frontStage/components/pagination'
import addressPicker from '@/components/addressPicker'
import PicZoom from 'vue-piczoom'
import SimpleShopDetail from '../shop/simpleShopDetail'
import { addCart, getLogInMaterialInfo, getMaterialInfo } from '@/api/frontStage/productDetail'
import { getShopPhone } from '@/api/frontStage/shop'
import { addShopCollect, isCollect } from '@/api/frontStage/productCollect'
import { mapState } from 'vuex'
import { listCommentByProductId } from '@/api/frontStage/productComment'
import { getLoginProductDetailDealRecord, getProductDetailDealRecord } from '@/api/w/productDetail'
import { getList } from '@/api/frontStage/shippingAddr'

export default {
    name: 'secondDetail',
    components: { PicZoom, SimpleShopDetail, pagination, addressPicker },
    data () {
        return {
            userAddress: {
                detailAddress: '',
                receiverName: '',
                receiverMobile: '',
                zonePrice: '',
                zoneId: ''
            },
            historyList: [],
            showProductLo: false,
            pagination: {
                currPage: 1, //当前页
                destination: null,
                pageSize: 10, // 显示数量
                totalNum: 0,
                totalPage: 0,
            },
            pagination2: {
                currPage: 1, //当前页
                destination: null,
                pageSize: 10, // 显示数量
                totalNum: 0,
                totalPage: 0,
            },
            showProduct: true,
            noProductText: '',
            productInfo: {},
            value1: 5,
            tabcurrent: 1,
            tabcurrent1: 0,
            imgArr: [],
            num: 1,
            imgCurrent: 0,
            isCollectState: 0,
            reviewList: [],
            isLogin: false,
            lastObj: {
                title: null,
                from: {}
            },
            area: null,
            areaValue: null,
            accountPeriod: null,
            accountPeriodOptions: [],
            regionOption: [],
            xiankuanhouhuo: null,
            taxInPrice: null,
            price: null,
            defaultAddress: {}
        }
    },
    created () {
        this.getProductInfoM()
        this.exestCollect()
        this.listCommentByProductIdM()
        this.getProductDetailDealRecordM()
        this.getDefaultAddress()
    },
    watch: {
        imgCurrent (index) {
            this.handleImgScroll(index)
        },
    },
    computed: {
        // 自动计算保留四位
        totalWeight () {
            return this.fixed4(this.num * this.productInfo.secondUnitNum)
        },
        ...mapState(['userInfo']),
    },
    beforeRouteEnter (to, from, next) {
        next(vm => {
            vm.lastObj.title = from.meta.title
            vm.lastObj.from = from
        })
    },
    methods: {
        handleQyChange (val) {
            let rp = this.productInfo.regionPrice.filter(item => item.regionPriceId === val)[0]
            this.xiankuanhouhuo = rp.payBeforeDelivery
            this.taxInPrice = rp.bonusTaxInPrice != null ? rp.bonusTaxInPrice : rp.taxInPrice
            this.price = rp.bonusPrice != null ? rp.bonusPrice : rp.price
            this.areaValue = rp.area
        },
        handleZqChange (val) {
            this.regionOption = []
            this.regionOption = this.productInfo.regionPrice.filter(item => item.accountPeriod === val)
            this.area = this.regionOption[0].regionPriceId
        },
        //获取采购员默认地址
        getDefaultAddress () {
            getList().then(res => {
                this.defaultAddress = res.list.filter(item => item.isDefaultAddress === 1 )[0]
            })
        },
        fixed2 (num) {
            return toFixed(num, 2)
        },
        isZoneConfirm () {
            let addr = []
            let province = ''
            let city = ''
            //获取省，市
            if (this.userAddress.detailAddress.includes('省')) {
                addr = this.userAddress.detailAddress.split('省')
                province = addr[0]
                city = addr[1]
            }else if (this.userAddress.detailAddress.includes('自治区')) {
                addr = this.userAddress.detailAddress.split('自治区')
                province = addr[0]
                city = addr[1]
            }else {
                addr = this.userAddress.detailAddress.split('市')
                province = addr[0]
                city = this.userAddress.detailAddress
            }
            const zoneList = this.productInfo.zones.filter(obj => obj.zone.includes(province))
            if (zoneList.length === 0) {
                return false
            }else {
                const citys = zoneList[0].children.filter(obj => city.includes(obj.zone))
                if (citys.length === 0) {
                    return false
                }else {
                    this.userAddress.zoneId = citys[0].zoneId
                    return true
                }
            }

        },
        onAddrError (value) {
            this.clientPop('err', value + '不在商品的配送区域，请用户前往个人中心设置设置默认收货地址？', async () => {
                this.$router.replace('/user/shippingAddr')
            })
        },
        confirm (value) {
            console.log(value.userAddress.zoneId, 34343)
            this.userAddress = value.userAddress
            if (value.district != null) {
                this.userAddress.detailAddress = value.userAddress.detailAddress + value.district
            }else {
                this.userAddress.detailAddress = value.userAddress.detailAddress
            }
            this.productInfo.sellPrice = this.userAddress.zonePrice

        },
        handleMinNum (val) {
            if (val === 0) {
                this.num =  1
            } else {
                this.num = this.fixed4(val)
            }
        },
        goToListByClass () {
            let paths = this.productInfo.classPathVOS.map(item => {
                return item.className
            })
            let classId = this.productInfo.classPathVOS[paths.length - 1].classId
            this.openWindowTab({
                path: '/mFront/productList',
                query: { classId: classId, classPath: paths.join(' > ') }
            })
        },
        fixed4 (num) {
            return toFixed(num, 4)
        },
        handleImgScroll (index) {
            let box = this.$refs.imgBox
            let img = this.$refs.img[index]

            let startOverflow = box.offsetLeft - img.offsetLeft + box.scrollLeft
            let endOverflow = 112 + img.offsetLeft - box.offsetWidth - box.scrollLeft - box.offsetLeft

            if (endOverflow > 0) {
                return box.scrollLeft += endOverflow
            }
            if (img.offsetLeft - box.scrollLeft < box.offsetLeft) {
                box.scrollLeft = box.scrollLeft - startOverflow
            }
        },
        getProductDetailDealRecordM () {
            let params = {
                page: this.pagination2.currPage,
                limit: this.pagination2.pageSize,
                productId: this.$route.query.productId
            }
            if (!(localStorage.getItem('token'))) {
                getProductDetailDealRecord(params).then(res => {
                    this.historyList = res.list
                    this.historyList.forEach(t => {
                        t.productPrice = '***'
                    })
                    this.pagination2.currPage = res.currPage
                    this.pagination2.pageSize = res.pageSize
                    this.pagination2.totalNum = res.totalCount
                    this.pagination2.totalPage = res.totalPage
                })
            } else {
                getLoginProductDetailDealRecord(params).then(res => {
                    this.historyList = res.list
                    this.historyList.forEach(t => {
                        t.productPrice = '***'
                    })
                    this.pagination2.currPage = res.currPage
                    this.pagination2.pageSize = res.pageSize
                    this.pagination2.totalNum = res.totalCount
                    this.pagination2.totalPage = res.totalPage
                })

            }

        },
        listCommentByProductIdM () {
            let params = {
                page: this.pagination.currPage,
                limit: this.pagination.pageSize,
                productId: this.$route.query.productId
            }
            listCommentByProductId(params).then(res => {
                this.reviewList = []
                res.list.forEach(t => {
                    this.reviewList.push({
                        avatar: t.userImg,
                        username: t.nickName,
                        text: t.commentContent,
                        images: [],
                    })
                })
                this.pagination.currPage = res.currPage
                this.pagination.pageSize = res.pageSize
                this.pagination.totalNum = res.totalCount
                this.pagination.totalPage = res.totalPage
            })

        },
        currentChange2 (index) {
            this.pagination2.currPage = index
            this.getProductDetailDealRecordM()
        },
        sizeChange2 (size) {
            this.pagination2.pageSize = size
            this.getProductDetailDealRecordM()
        },
        currentChange (index) {
            this.pagination.currPage = index
            this.listCommentByProductIdM()
        },
        sizeChange (size) {
            this.pagination.pageSize = size
            this.listCommentByProductIdM()
        },
        // 购买商品
        handlePurchase () {
            return this.$message.error('请加入购物车购买！')
            // 屏蔽
            // let isSubmitFlag = this.userInfo.isSubmitOrder
            // let isInterior = this.userInfo.isInterior
            // if (isInterior == 1) {
            //     if (isSubmitFlag == null || isSubmitFlag == 0) {
            //         this.$message.error('没有下单权限，请联系管理员！')
            //         return
            //     }
            // }
            // this.openWindowTab({
            //     path: '/user/submitOrder',
            //     query: { productId: this.productInfo.productId, buyNum: this.num }
            // })
        },
        // 加入购物车
        handleAddToCart () {
            if (this.productInfo.shopState == 0) {
                return this.$message.error('该店铺已被冻结，无法购买商品')
            }
            console.log(this.areaValue)
            console.log(this.defaultAddress)
            let areaValue = this.areaValue.split(',')
            for (let i = 0; i < areaValue.length; i++) {
                if (areaValue[i] == this.defaultAddress.province || areaValue[i].includes(this.defaultAddress.city)) {
                    break
                }else{
                    this.$alert('该商品已超出配送范围，请重新选购或联系供应商增加派送范围', '提示', {
                        confirmButtonText: '确定',
                        dangerouslyUseHTMLString: true,
                        type: 'warning',
                        confirmButtonClass: 'alertConfirmBtn',
                    })
                    return
                    //return this.$message.error('该商品已超出配送范围，请重新选购或联系供应商增加派送范围')
                }
            }
            this.showProductLo = true
            addCart({ cartNum: this.num, productId: this.productInfo.productId }).then(res => {
                if (res.code == 200) {
                    this.$message({
                        message: '加入成功！',
                        type: 'success'
                    })
                    this.$bus.$emit('refreshCart')
                }
                this.showProductLo = false
            }).catch(() => {
                this.showProductLo = false
            })

        },
        openWindow () {
            window.open('/front/secondHandDevice', '_blank')
        },
        //判断用户是否登录关注
        exestCollect () {
            if (!(localStorage.getItem('token'))) return
            let params = {
                productId: this.$route.query.productId,
            }
            isCollect(params).then(res => {
                if (typeof res != 'number') {
                    this.isCollectState = res.data
                } else {
                    this.isCollectState = res
                }
            })
        },

        // 获取商品详情
        getProductInfoM () {
            let params = {
                productId: this.$route.query.productId,
            }
            if (!(localStorage.getItem('token'))) {
                getMaterialInfo(params).then(res => {
                    console.log(res)
                    this.isLogin = true
                    this.productInfo = res
                    if (res.productFile.length == 0) {
                        this.imgArr.push(res.productMinImg)
                    } else {
                        this.imgArr = res.productFile.map(t => {
                            return t.url
                        })
                    }
                }).catch(res => {
                    let codes = { '500101': '商品不存在', '500102': '商品已下架', }
                    if (Object.keys(codes).includes(res.code)) {
                        this.noProductText = codes[String(res.code)]
                        return this.showProduct = false
                    }
                })
            } else {
                this.isLogin = false
                getLogInMaterialInfo(params).then(res => {
                    console.log(res)
                    this.productInfo = res
                    if (res.productFile.length == 0) {
                        this.imgArr.push(res.productMinImg)
                    } else {
                        this.imgArr = res.productFile.map(t => {
                            return t.url
                        })
                    }
                    console.log(res.accountPeriod)
                    let ap = res.accountPeriod.split(',').map(Number)
                    console.log(ap)
                    this.accountPeriod = ap[0]
                    ap.forEach(e=>{
                        if (e == 1) {
                            this.accountPeriodOptions.push({ value: 1, label: '1个月账期' })
                        }
                        if (e == 2) {
                            this.accountPeriodOptions.push({ value: 2, label: '2个月账期' })
                        }
                        if (e == 3) {
                            this.accountPeriodOptions.push({ value: 3, label: '3个月账期' })
                        }
                        if (e == 4) {
                            this.accountPeriodOptions.push({ value: 4, label: '4个月账期' })
                        }
                        if (e == 5) {
                            this.accountPeriodOptions.push({ value: 5, label: '5个月账期' })
                        }
                        if (e == 6) {
                            this.accountPeriodOptions.push({ value: 6, label: '6个月账期' })
                        }
                    })
                    this.regionOption = res.regionPrice.filter(item => item.accountPeriod === this.accountPeriod)
                    this.area = this.regionOption[0].regionPriceId
                    let rp = res.regionPrice.filter(item => item.regionPriceId === this.area)[0]
                    this.xiankuanhouhuo = rp.payBeforeDelivery
                    this.taxInPrice = rp.bonusTaxInPrice != null ? rp.bonusTaxInPrice : rp.taxInPrice
                    this.price = rp.bonusPrice != null ? rp.bonusPrice : rp.price
                    this.areaValue = rp.area
                }).catch(res => {
                    let codes = { '500101': '商品不存在', '500102': '商品已下架', }
                    if (Object.keys(codes).includes(res.code)) {
                        this.noProductText = codes[String(res.code)]
                        return this.showProduct = false
                    }
                })

            }

        },
        up () {
            if (this.imgCurrent > 0) {
                this.imgCurrent--
            }
        },
        next () {
            if (this.imgCurrent < this.imgArr.length - 1) {
                this.imgCurrent++
            }
        },
        getTel () {
            if ((localStorage.getItem('token'))) {
                getShopPhone({ shopId: this.productInfo.shopId }).then(res => {
                    this.productInfo.contactNumber = res
                })
            } else {
                this.$router.push('/login')
            }

        },
        // 关注商品
        addCollect () {
            let params = {
                productId: this.productInfo.productId,
                collectType: 1,
                productType: 1,
            }
            if (this.isCollectState == 1) {
                this.clientPop('info', '您确定要取消关注吗？', async () => {
                    addShopCollect(params).then(res => {
                        if (res.code == 200) {
                            this.isCollectState = 0
                            this.$message({
                                message: '取消关注成功',
                                type: 'success'
                            })
                        }
                    })

                })
            } else {
                addShopCollect(params).then(res => {
                    if (res.code == 200) {
                        this.isCollectState = 1
                        this.$message({
                            message: '关注成功',
                            type: 'success'
                        })
                    }
                })
            }

        },
    }
}
</script>
<style>
.mouse-cover-canvas {
    background-color: #fff;
}
</style>
<style scoped lang="scss">

div {
    // line-height: 1;
}

.router-usher {
    cursor: pointer;

    a:hover {
        color: rgba(34, 111, 199, 1);
    }
}

span {
    line-height: 1;
}

.top {
    width: 100%;
    height: 60px;
    opacity: 1;
    background: rgba(34, 111, 199, 1);

    .top_content {
        width: 1326px;
        margin: 0 auto;
        height: 100%;
        color: #fff;

        .tabItem {
            padding: 0 30px;
            font-size: 16px;
            font-weight: 500;
            line-height: 60px;
            cursor: pointer;
        }
    }
}

.breadcrumb {
    width: 1326px;
    margin-top: 20px;
}

.main {
    width: 1326px;
    margin: 0 auto;

    .baseInfo {
        width: 100%;
        align-items: flex-start;

        /deep/ .magnifier-box {
            img {
                width: 540px;
                height: 405px;
                object-fit: cover;
            }
        }

        .imgBox {
            width: 540px;

            .bigImg {
                width: 540px;
                height: 405px;
                // position: relative;

                .disableCover {
                    width: 100%;
                    height: 100%;
                    background: rgba(255, 255, 255, 0.6);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    position: absolute;
                    top: 0;
                    left: 0;

                    div {
                        width: 140px;
                        height: 140px;
                        line-height: 140px;
                        font-size: 28px;
                        text-align: center;
                        border-radius: 50%;
                        color: #fff;
                        background: rgba(128, 128, 128, 1);
                    }
                }
            }

            .imgList {
                max-width: 540px;
                margin-top: 20px;

                i {
                    color: #999;
                    font-size: 22px;
                }

                .imgArr {
                    display: flex;
                    flex: 1;
                    overflow: auto;

                    &::-webkit-scrollbar {
                        display: none;
                    }
                }

                img {
                    width: 96px;
                    height: 72px;
                    margin-left: 16px;
                    object-fit: cover;
                }
            }
        }

        .baseInfo_center {
            width: 480px;

            .title {
                width: 100%;
                font-size: 24px;
                font-weight: 500;
                letter-spacing: 0px;
                color: rgba(0, 0, 0, 1);
            }

            .ls_title {
                width: 100%;
            }

            .priceBox {
                width: 470px;
                height: 66px;
                background: rgba(250, 250, 250, 1);
                padding-left: 10px;

                div {
                    font-size: 16px;
                    font-weight: 400;
                    color: rgba(102, 102, 102, 1);
                    width: 86px;
                }

                span {
                    font-size: 24px;
                    font-weight: 500;
                    color: rgba(212, 48, 48, 1);
                }
            }

            .row {
                padding-left: 10px;

                .row_left {
                    width: 86px;
                    font-size: 16px;
                    font-weight: 400;
                    color: rgba(102, 102, 102, 1);
                }

                .row_right {
                    font-size: 16px;

                    .div1 {
                        width: 60px;
                        height: 22px;
                        background: rgba(34, 111, 199, 1);
                        text-align: center;
                        line-height: 22px;
                        font-size: 12px;
                        color: #fff;
                        border-radius: 2px;
                        margin-left: 10px;
                        cursor: pointer;
                    }
                }
            }

            .btns {
                width: 100%;

                div:first-child {
                    border: none;
                    color: #fff;
                }

                div {
                    width: 140px;
                    height: 50px;
                    text-align: center;
                    line-height: 50px;
                    border: 1px solid rgba(34, 111, 199, 1);
                    font-size: 18px;
                    font-weight: 400;
                    color: rgba(34, 111, 199, 1);
                    cursor: pointer;
                    user-select: none;
                }

                .disabled {
                    color: rgba(204, 204, 204, 1);
                    border: 1px solid rgba(217, 217, 217, 1);
                }
            }
        }

        .baseInfoRight {
            width: 260px;
            .title {
                width: 260px;
                height: 48px;
                opacity: 1;
                background: rgba(34, 111, 199, 1);
                text-align: center;
                line-height: 48px;
                color: #fff;
            }

            img {
                width: 100px;
                height: 100px;
                border-radius: 50%;
                display: block;
                margin: 0 auto;
                margin-top: 37px;
            }

            .store {
                width: 240px;
                font-size: 16px;
                font-weight: 400;
                color: rgba(0, 0, 0, 1);
                text-align: center;
                margin: 10px 10px 30px;
            }

            .cell {
                margin-bottom: 20px;

                .cell_left {
                    padding: 0 20px;
                    font-size: 14px;
                    font-weight: 400;
                    color: rgba(102, 102, 102, 1);
                }

                .cell_right {
                    width: 140px;
                    font-size: 14px;
                    font-weight: 400;
                    color: rgba(0, 0, 0, 1);
                }
            }
        }
    }

    .detailBox {
        width: 1326px;
        min-height: 605px;
        opacity: 1;
        background: rgba(255, 255, 255, 1);
        border: 1px solid rgba(229, 229, 229, 1);
        margin-top: 60px;

        .tabBox {
            width: 1324px;
            height: 60px;
            background: rgba(250, 250, 250, 1);

            div {
                width: 160px;
                height: 60px;
                text-align: center;
                line-height: 60px;
                font-size: 18px;
                cursor: pointer;
            }

            .tabab {
                background: #fff;
                border-top: solid 2px rgba(34, 111, 199, 1);
            }
        }

        .titleBox {
            height: 52px;
            border-bottom: solid 1px rgba(229, 229, 229, 1);
            padding-left: 20px;

            div {
                width: 3px;
                height: 20px;
                background: rgba(34, 111, 199, 1);
                margin-right: 10px;
            }

            span {
                font-size: 20px;
                font-weight: 500;
                color: rgba(34, 111, 199, 1);
            }
        }

        .row {
            padding-left: 27px;
            padding-right: 240px;

            .item {
                span {
                    font-size: 16px;
                    font-weight: 400;
                    color: rgba(102, 102, 102, 1);
                }

                div {
                    font-size: 16px;
                    font-weight: 400;
                    color: rgba(0, 0, 0, 1);
                }
            }
        }

        .han {
            font-size: 16px;
            font-weight: 400;
            color: rgba(0, 0, 0, 1);
            padding-left: 30px;
            margin-top: 30px;
        }

        .descriptionsBox {
            padding: 20px;
        }
        .description p {
            img {
                max-width: 100%;
            }
        }
        .review {
            padding: 10px 20px;

            .reviewList {
                min-height: 400px;
            }

            .reviewTitle {
                height: 42px;
                margin-bottom: 22px;
                border-bottom: 1px solid rgba(229, 229, 229, 1);

                div {
                    font-size: 20px;
                    color: rgba(34, 111, 199, 1);

                    &:before {
                        content: '';
                        display: block;
                        width: 3px;
                        height: 20px;
                        margin-right: 10px;
                        background-color: rgba(34, 111, 199, 1);
                    }
                }
            }

            .avatar {
                width: 44px;
                height: 44px;
                margin-right: 10px;
                border-radius: 50%;
            }

            .reviewItem {
                &:not(:last-of-type) {
                    margin-bottom: 24px;
                    padding-bottom: 20px;
                    border-bottom: 1px solid rgba(229, 229, 229, 1);
                }

                .reviewTop {

                    .user {
                        div:first-child {
                            font-size: 18px;
                            font-weight: 500;
                        }

                        div:last-child {
                            color: rgba(102, 102, 102, 1);
                        }
                    }
                }

                p {
                    font-size: 16px;
                    color: rgba(51, 51, 51, 1);
                }

                .imgs {
                    margin-top: 10px;

                    img {
                        width: 120px;
                        height: 120px;
                        margin-right: 10px;
                        object-fit: cover;
                    }
                }
            }

            .historyItem {
                height: 108px;
                padding-right: 26px;

                &:not(:last-of-type) {
                    border-bottom: 1px solid rgba(229, 229, 229, 1);
                }

                .product, .numbers, .finishTime {
                    text-align: center;
                }
            }
        }
    }
}
</style>
<style>
.descriptionsBox .el-descriptions-item__label {
    color: #333;
    font-size: 18px;
}

.descriptionsBox .el-descriptions-item__content {
    font-size: 18px !important;
    font-weight: 400;
    color: rgba(102, 102, 102, 1);
}
</style>
