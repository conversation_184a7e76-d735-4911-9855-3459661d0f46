<template>
    <div class="productListPage">
        <main>
            <div class="center content df">
                <div class="main_left" @mouseleave="() => {typeCurrent = null; showPop = false}">
                    <div class="title p20">商品分类</div>
                    <transition-group appear name="el-fade-in">
                        <div class="typeItem p20 dfb"
                             :style="{ color: typeCurrent === i ? '#fff' : '', background: typeCurrent === i ? '#226FC7' : '' }"
                             v-for="(item, i) in typeList" :key="item.classId"
                             @mouseenter="showPopMenu(i)">
                            <div @click="selectClass(item)">{{ item.className }}</div>
                            <i class="el-icon-arrow-right"></i>
                        </div>
                    </transition-group>
                    <!-- 弹框 -->
                    <transition appear name="el-fade-in">
                        <div class="pop" v-show="showPop" :style="{ height: typeList.length * 58 + 67 + 'px' }">
                            <div class="sector" v-for="(item, i) in popContent" :key="i">
                                <div class="pop_title pointer" @click="selectClass(item)">
                                    {{ item.className }}
                                </div>
                                <div class="items df">
                                    <span @click="selectClass(subitem)" v-for="(subitem, index) in item.children"
                                          :key="index">{{ subitem.className }}</span>
                                </div>
                            </div>
                        </div>
                    </transition>
                </div>
                <div class="product-box center front" v-loading="showLoading">
                    <filterBox :form="filterArr" :list="checkedList" @checkChange="checkChange"></filterBox>
                    <div class="dfa filterBox_top">
                        <el-radio-group v-model="queryAll.checkedRadio" @change="getMaterialPageListM">
                            <el-radio v-for="item in checkboxList" :label="item.value" :value="item.value"
                                      :key="item.value">
                                {{ item.label }}
                            </el-radio>
                        </el-radio-group>
                        <div :class="['sortPrice', 'sortBtn', 'ml10', 'pointer', sortObj.tag === 0 ? 'selected' : '']"
                             @click="selectSort(0)">
                            <span style="margin-right: 0;">综合排序</span>
                        </div>
                        <div :class="['sortPrice', 'sortBtn', 'pointer', sortObj.tag === 1 ? 'selected' : '']"
                             @click="selectSort(1)">
                            <span>价格</span><img :src="priceArrow" alt="">
                        </div>
                        <div :class="['sortPrice', 'sortBtn', 'pointer', sortObj.tag === 3 ? 'selected' : '']"
                             @click="selectSort(3)">
                            <span>销量</span><img :src="salesVolume" alt="">
                        </div>
                        <div :class="['sortPrice', 'sortBtn', 'pointer', sortObj.tag === 2 ? 'selected' : '']"
                             @click="selectSort(2)">
                            <span>更新时间</span><img :src="timeArrow" alt="">
                        </div>
                        <div :class="['sortBtn','pointer', sortObj.tag === 4 ? 'selected' : '']"
                             @click="selectSort(4)">
                            <span>综合评价</span><img :src="ComprehensiveEvaluation" alt="">
                        </div>
                    </div>
                    <div class="row dfa filterBox_bottom">
                        <div>价格范围：</div>
                        <div class="priceFilter">￥<input type="text" v-model="queryAll.price.min"></div>
                        <div class="bar"></div>
                        <div class="priceFilter">￥<input type="text" v-model="queryAll.price.max"></div>
                        <div class="searchInput df">
                            <div class="dfa">
                                <img src="@/assets/images/search1.png" alt="">
                                <input type="text" placeholder="搜索商品/店铺" v-model="queryAll.keyword"/>
                            </div>
                            <button @click="getMaterialPageListM">搜索</button>
                        </div>
                    </div>
                    <div class="productClass df">
                        当前分类：
                        <div v-for="item in productClassList" :key="item.classId">
                            {{ item.label }} <i class="pointer el-icon el-icon-close"
                                                @click="removeProductClass(item)"></i>
                        </div>
                    </div>
                    <div class="product-list">
                        <transition-group
                            name="el-fade-in"
                            tag="div"
                            v-for="(item, i) in productList"
                            :key="item.productMinImg"
                            appear
                        >
                            <div
                                class="item pointer mb20"
                                :style="{ marginRight: (i+1)%4===0 ? 0 : '20px' }"
                                @click="openWindowTab({ path: '/mFront/productDetail', query: { productId: item.productId } })"
                                :key="item.productMinImg"
                            >
                                <div class="productTag" v-if="item.isBusiness">平台自营</div>
                                <img
                                    :src="item.productMinImg ? imgUrlPrefixAdd + item.productMinImg : require('@/assets/images/img/queshen5.png')"
                                    alt="">
                                <div class="price">￥{{ item.productMinPrice.toFixed(2) }}</div>
                                <div class="title mt10 textOverflow2">{{ item.productName }}</div>
                                <div class="description textOverflow1">{{ item.skuName || '   ' }}</div>
                                <div :title="item.shopName" class="shopName textOverflow1" v-if="item.shopName"
                                     @click.stop="goToShop(item.shopId)">
                                    {{ item.shopName }}
                                </div>
                                <div class="actionBtn dfa">
                                    <div @click.stop="() => {}">
                                        <el-checkbox
                                            class="dfc"
                                            @change="toggleComparisonItem(item)"
                                            @click.stop="() => {}"
                                            :value="item.comparison"
                                        >
                                            对比
                                        </el-checkbox>
                                    </div>
                                    <div class="dfc" @click.stop="toggleCollection(item.productId)">
                                        <template v-if="item.collect">
                                            <el-icon class="el-icon el-icon-star-on"/>
                                            取消关注
                                        </template>
                                        <template v-else>
                                            <el-icon class="el-icon el-icon-star-off"/>
                                            关注
                                        </template>
                                    </div>
                                </div>
                            </div>
                        </transition-group>
                    </div>
                    <Pagination
                        :total="totalCount"
                        :current-page="queryAll.page"
                        :page-size.sync="queryAll.limit"
                        :page-sizes="[20, 40, 60, 80]"
                        @currentChange="handlePageChange"
                        @sizeChange="handleSizeChange"
                    />
                </div>
            </div>
        </main>
        <section class="compare-section dfc" v-show="selectedList.length > 0">
            <div class="dfa">
                <div class="compare-item df">
                    <template v-if="selectedList[0]">
                        <img
                            :src="selectedList[0].productMinImg ? imgUrlPrefixAdd + selectedList[0].productMinImg : require('@/assets/images/img/queshen5.png')"
                            alt="">
                        <div class="right">
                            <div class="textOverflow2" style="color: rgba(51, 51, 51, 1);">
                                {{ selectedList[0].productName }}
                            </div>
                            <div class="price">￥{{ selectedList[0].productMinPrice.toFixed(2) }}</div>
                            <div class="del pointer" @click="delComparisonItem(selectedList[0].productId)"><i
                                class="el-icon el-icon-close"/>删除
                            </div>
                        </div>
                    </template>
                    <template v-else>
                        <div class="num-block">1</div>
                        <div class="right">
                            <div class="textOverflow2" style="color: rgba(179, 179, 179, 1);">
                                您还可以继续添加
                            </div>
                        </div>
                    </template>
                </div>
                <div class="compare-item df">
                    <template v-if="selectedList[1]">
                        <img
                            :src="selectedList[1].productMinImg ? imgUrlPrefixAdd + selectedList[1].productMinImg : require('@/assets/images/img/queshen5.png')"
                            alt="">
                        <div class="right">
                            <div class="textOverflow2" style="color: rgba(51, 51, 51, 1);">
                                {{ selectedList[1].productName }}
                            </div>
                            <div class="price">￥{{ selectedList[1].productMinPrice.toFixed(2) }}</div>
                            <div class="del pointer" @click="delComparisonItem(selectedList[1].productId)"><i
                                class="el-icon el-icon-close"/>删除
                            </div>
                        </div>
                    </template>
                    <template v-else>
                        <div class="num-block">2</div>
                        <div class="right">
                            <div class="textOverflow2" style="color: rgba(179, 179, 179, 1);">
                                您还可以继续添加
                            </div>
                        </div>
                    </template>
                </div>
                <div class="compare-item df">
                    <template v-if="selectedList[2]">
                        <img
                            :src="selectedList[2].productMinImg ? imgUrlPrefixAdd + selectedList[2].productMinImg : require('@/assets/images/img/queshen5.png')"
                            alt="">
                        <div class="right">
                            <div class="textOverflow2" style="color: rgba(51, 51, 51, 1);">
                                {{ selectedList[2].productName }}
                            </div>
                            <div class="price">￥{{ selectedList[2].productMinPrice.toFixed(2) }}</div>
                            <div class="del pointer" @click="delComparisonItem(selectedList[2].productId)"><i
                                class="el-icon el-icon-close"/>删除
                            </div>
                        </div>
                    </template>
                    <template v-else>
                        <div class="num-block">3</div>
                        <div class="right">
                            <div class="textOverflow2" style="color: rgba(179, 179, 179, 1);">
                                您还可以继续添加
                            </div>
                        </div>
                    </template>
                </div>
                <div class="compare-item df">
                    <template v-if="selectedList[3]">
                        <img
                            :src="selectedList[3].productMinImg ? imgUrlPrefixAdd + selectedList[3].productMinImg : require('@/assets/images/img/queshen5.png')"
                            alt="">
                        <div class="right">
                            <div class="textOverflow2" style="color: rgba(51, 51, 51, 1);">
                                {{ selectedList[3].productName }}
                            </div>
                            <div class="price">￥{{ selectedList[3].productMinPrice.toFixed(2) }}</div>
                            <div class="del pointer" @click="delComparisonItem(selectedList[3].productId)"><i
                                class="el-icon el-icon-close"/>删除
                            </div>
                        </div>
                    </template>
                    <template v-else>
                        <div class="num-block">4</div>
                        <div class="right">
                            <div class="textOverflow2" style="color: rgba(179, 179, 179, 1);">
                                您还可以继续添加
                            </div>
                        </div>
                    </template>
                </div>
            </div>
            <div class="compare-btns">
                <button @click="compareItems">对比</button>
                <div class="pointer" @click="clearComparisonList">清空对比栏</div>
            </div>
        </section>
    </div>
</template>
<script>
import filterBox from '../../components/searchBox'
import Pagination from '@/pages/frontStage/components/simplePagination'
import arrow from '@/assets/images/arrow.png'
import arrow_up from '@/assets/images/arrow_up.png'
import arrow2 from '@/assets/images/arrow2.png'
import arrow2_up from '@/assets/images/arrow2_up.png'
import { getBrand } from '@/api/frontStage/brand'
import { getLoginMaterialPageList, getMaterialPageList } from '@/api/frontStage/productList'
import { addShopCollect } from '@/api/frontStage/productCollect'
import { getCategoryTree } from '@/api/frontStage/productCategory'
import { getBrandMU, getCityMU } from '@/utils/common'
import { mapState } from 'vuex'

export default {
    components: {
        filterBox, Pagination
    },
    data () {
        return {
            showLoading: false,
            typeCurrent: null,
            showPop: false,
            typeList: [],
            popContent: [],
            init: {
                // 值为99 代表低脂易耗品和大宗临购商品都克搜索
                productType: 99,
                mallType: 0
            },
            queryAll: {
                page: 1,
                limit: 20,
                label: {},
                price: {
                    min: null, max: null,
                },
                keyword: null,
                checkedRadio: null,
            },
            tagFilterStr: '0-desc',
            productList: [],
            arrow,
            arrow_up,
            arrow2,
            arrow2_up,
            priceArrow: arrow,
            salesVolume: arrow,
            timeArrow: arrow,
            ComprehensiveEvaluation: arrow,
            checkedList: [],
            filterArr: [],
            checkboxList: [
                { label: '全部', value: null },
                { label: '平台自营', value: 1 },
                { label: '路桥内部店', value: 2 },
                { label: '其它', value: 3 },
            ],
            sortObj: {
                tag: 0,
                descend: true,
            },
            flag: true,
            totalCount: 0,
            productClassList: [],
            // comparisonList: [],
            // 已选的比价数据
            selectedList: []
        }
    },
    watch: {
        $route: function () {
            this.queryAll.keyword = this.$route.query.keywords || ''
            this.getMaterialPageListM()
        },
        'sortObj.tag': {
            handler (newVal, oldVal) {
                if (oldVal == 2) {
                    if (this.sortObj.descend) {
                        return this.timeArrow = this.arrow
                    }
                    this.timeArrow = this.arrow_up
                } else if (oldVal == 1) {
                    if (this.sortObj.descend) {
                        return this.priceArrow = this.arrow
                    }
                    this.priceArrow = this.arrow_up
                }else if (oldVal == 3) {
                    if (this.sortObj.descend) {
                        return this.salesVolume = this.arrow
                    }
                    this.salesVolume = this.arrow_up
                }else if (oldVal == 4) {
                    if (this.sortObj.descend) {
                        return this.ComprehensiveEvaluation = this.arrow
                    }
                    this.ComprehensiveEvaluation = this.arrow_up
                }
            }
        },
        tagFilterStr (newVal) {
            switch (newVal) {
            case '1-asc':
                this.priceArrow = this.arrow2_up
                break
            case '1-desc':
                this.priceArrow = this.arrow2
                break
            case '2-asc':
                this.timeArrow = this.arrow2_up
                break
            case '2-desc':
                this.timeArrow = this.arrow2
                break
            case '3-asc':
                this.salesVolume = this.arrow2_up
                break
            case '3-desc':
                this.salesVolume = this.arrow2
                break
            case '4-asc':
                this.ComprehensiveEvaluation = this.arrow2_up
                break
            case '4-desc':
                this.ComprehensiveEvaluation = this.arrow2
                break
            }
        },
    },
    computed: {
        ...mapState(['materialCity', 'userInfo']),
        showComparisonSection () {
            return this.comparisonList.length > 0
        },
        comparisonIdList () {
            return this.comparisonList.map(item => item.productId)
        },
        comparisonList () {
            return this.productList.filter(item => item.comparison)
        }
    },
    created () {
        let { keywords, classId, classPath } = this.$route.query
        this.queryAll.keyword = keywords || ''
        if (classId && classPath) this.productClassList[0] = { classId, label: classPath }
        this.getClassTree() // 获取商品分类树
        this.getFilterList()
        this.getMaterialPageListM() // 查询商品
        this.$bus.$on('queryPageList', this.getMaterialPageListM)
    },
    async mounted () {
        // 懒加载列表
        // window.addEventListener('scroll', debounce(this.handleTouchBottom, 200))
    },
    beforeDestroy () {
        // window.removeEventListener('scroll', debounce(this.handleTouchBottom, 100))
    },
    methods: {
        goToShop (shopId) {
            this.openWindowTab({ path: '/mFront/shopIndex', query: { shopId } })
        },
        handleTouchBottom () {
            let isBottom = window.innerHeight + window.scrollY >= document.documentElement.scrollHeight - 500
            if (isBottom && this.productList.length < this.totalCount) {
                this.queryAll.page++
                this.getScrData()
            }
        },
        handlePageChange (page) {
            this.queryAll.page = page
            this.getMaterialPageListM()
        },
        handleSizeChange (size) {
            this.queryAll.limit = size
            this.getMaterialPageListM()
        },
        async getFilterList (id = '') {
            this.showLoading = true
            if (id) {
                let brands = await this.getBrandM()
                this.filterArr.forEach((item, i, arr) => {
                    if (item.name === '品牌') arr.splice(i, 1, brands)
                })
            } else {
                let brands = await this.getBrandM()
                let cities = null
                brands ? this.filterArr.push(brands) : null
                cities ? this.filterArr.push(cities) : null
            }
            this.showLoading = false
        },
        async getClassTree () {
            // 获取商品分类树 需要重置分类类型为0
            this.typeList = await getCategoryTree({
                // 值为99 代表低脂易耗品和大宗临购商品都克搜索
                productType: 0,
                mallType: 0
            },)
        },
        showPopMenu (i) {
            this.typeCurrent !== i ? this.showPop = true : this.showPop = !this.showPop
            this.typeCurrent = i
            this.popContent = this.typeList[i].children
        },
        selectClass ({ classId, classPath }) {
            this.queryAll.keyword = ''
            this.productClassList[0] = { classId, label: classPath.replaceAll('/', ' > ') }
            this.showPop = false
            this.getFilterList(classId)
            this.getMaterialPageListM()
        },
        removeProductClass ({ classId }) {
            this.productClassList.forEach((item, i, arr) => {
                if (item.classId === classId) arr.splice(i, 1)
            })
            this.$router.push({ path: this.$route.path })
            this.getMaterialPageListM()
        },
        // 滚动获取
        async getScrData () {
            let params = this.initParams()
            let res
            if ((localStorage.getItem('token'))) {
                res = await getLoginMaterialPageList(params)
            } else {
                res = await getMaterialPageList(params)
            }
            this.totalCount = res.totalCount
            this.productList = this.productList.concat(res.list || [])
        },
        // 获取 城市
        async getByCityM () {
            return getCityMU(this.materialCity)
        },
        // 获取品牌
        async getBrandM () {
            /*let params = {
                page: 1,
                limit: 100
            }*/
            let res = await getBrand({ classId: this.productClassList[0]?.classId || '' })
            return getBrandMU(res)
        },
        // 获取商品列表
        async getMaterialPageListM () {
            let params = this.initParams()
            this.showLoading = true
            if (this.userInfo.token) {
                let res1 = await getLoginMaterialPageList(params)
                this.productList = this.processList(res1)
                this.totalCount = res1.totalCount
                if (res1.code !== 401) return this.showLoading = false

                let res2 = await getMaterialPageList(params)
                this.productList = this.processList(res2)
                this.totalCount = res2.totalCount
            } else {
                let res = await getMaterialPageList(params)
                this.productList = res.list.map(item => {
                    item.comparison = false
                    return item
                }) || []
                this.totalCount = res.totalCount
            }
            this.showLoading = false
        },
        processList ({ list }) {
            return list.map(item => {
                item.comparison = false
                this.selectedList.forEach(selectedItem => {
                    if (selectedItem.productId !== item.productId) return
                    item.comparison = selectedItem.comparison
                })
                return item
            }) || []
        },
        // 初始化参数
        initParams () {
            let params = {
                productType: this.init.productType,
                page: this.queryAll.page,
                limit: this.queryAll.limit,
                orderBy: this.tagFilterStr,
                isBusiness: this.queryAll.checkedRadio,
                classId: this.productClassList[0]?.classId || ''
            }
            let { keyword, label, price } = this.queryAll
            // 关键字
            keyword ? params.keywords = keyword : ''
            // 品牌
            label.brand ? params.brandName = label.brand.label : ''
            // 城市
            label.city ? params.city = label.city.value : ''
            // 以下价格
            price.max ? params.belowPrice = price.max : ''
            // 以上价格
            price.min ? params.abovePrice = price.min : ''
            return params
        },
        getFilterStr (obj) {
            if (obj.descend) {
                return this.tagFilterStr = `${obj.tag}-desc`
            }
            this.tagFilterStr = `${obj.tag}-asc`
        },
        // 条件列表
        checkChange (obj) {
            this.queryAll.label = obj
            this.getMaterialPageListM()
        },
        // 排序
        selectSort (tag) {
            if (this.sortObj.tag == tag) {
                this.sortObj.descend = !this.sortObj.descend
            }
            this.sortObj.tag = tag
            this.getFilterStr(this.sortObj)
            this.getMaterialPageListM()
        },
        toggleComparisonItem (targetItem) {
            if (!this.userInfo.token) {
                this.remindLogin('要使用商品对比功能请先登录')
                return
            }
            let idList = this.comparisonList.map(product => product.productId)
            if (this.selectedList.length === 4 && !idList.includes(targetItem.productId)) {
                this.$alert('对比商品不能超过4个', '提示', {
                    confirmButtonText: '确定',
                    confirmButtonClass: 'alertConfirmBtn',
                })
                return
            }
            // 反选勾选到的商品
            this.productList.forEach(product => {
                if (product.productId !== targetItem.productId) return
                product.comparison = !product.comparison
            })
            let containProduct = this.selectedList.some(item => item.productId === targetItem.productId)
            if (containProduct) {
                if (targetItem.comparison) return
                let index = this.selectedList.map(item => item.productId).indexOf(targetItem.productId)
                this.selectedList.splice(index, 1)
                return
            }
            this.selectedList.push(targetItem)
        },
        compareItems () {
            let list = this.selectedList.map(item => item.productId)
            if (list.length < 2) return this.$message.error('请选择2个及以上的商品进行对比')
            this.openWindowTab({ path: '/mFront/productComparison', query: { list: JSON.stringify(list) } })
        },
        delComparisonItem (id) {
            this.productList.forEach(item => {
                if (item.productId === id) item.comparison = false
            })
            this.selectedList.forEach((item, index, arr) => {
                if (id === item.productId) {
                    arr.splice(index, 1)
                }
            })
        },
        clearComparisonList () {
            this.productList.forEach(item => {
                item.comparison = false
            })
            this.selectedList = []
        },
        toggleCollection (productId) {
            if (!this.userInfo.token) {
                return this.remindLogin('登陆后可收藏商品')
            }
            addShopCollect({ productId, productType: 1, collectType: 1 }).then(res => {
                if (res.code !== 200) return this.$message.error(res.message)
                this.getMaterialPageListM()
            })
        },
        remindLogin (msg) {
            this.$alert(msg, '登录提示', {
                showCancelButton: true,
                confirmButtonText: '去登陆',
                cancelButtonText: '取消',
                beforeClose: (action, instance, done) => {
                    action === 'confirm' ? this.openWindowTab('/login') : null
                    done()
                }
            })
        },
    }
}
</script>
<style scoped lang="scss">

.main_left {
    width: 234px !important;
    min-height: 763px;
    background: #fff;
    position: relative;

    .title {
        font-size: 20px;
        font-weight: 500;
        color: rgba(0, 0, 0, 1);
        font-weight: 500;
        padding-bottom: 20px;
    }

    .typeItem {
        width: 100%;
        height: 58px;
        font-size: 16px;
        font-weight: 400;
        color: rgba(51, 51, 51, 1);
        cursor: pointer;
        user-select: none;

        div {
            width: 80%;
        }
    }

    .typeItem:hover {
        color: rgba(34, 111, 199, 1);
    }

    .pop {
        width: 1092px;
        min-height: 762px;
        padding: 30px;
        border: 1px solid #E6E6E6;
        box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.1);
        background-color: #fff;
        position: absolute;
        top: 0;
        left: 234px;
        z-index: 10;
        overflow-y: auto;

        .sector:not(:last-child) {
            //margin-bottom: 10px;
        }

        .sector {
            .pop_title {
                margin-bottom: 16px;
                font-size: 16px;
                font-weight: 500;
            }

            .items {
                font-size: 14px;
                flex-wrap: wrap;
                color: #666;

                span {
                    margin: 0 0 16px 0;
                    padding: 0 10px;
                    border-right: 1px solid #E6E6E6;
                    cursor: pointer;
                }

                & span:first-child {
                    padding-left: 0;
                }

                & span:last-child {
                    border-right: none;
                }
            }
        }
    }
}

.productListPage {
    position: relative;

    .tabs-bar {
        width: 100%;
        height: 60px;

        .top_content {
            //width: 1326px;
            //min-width: 1326px;
            height: inherit;

            & > div {
                width: 200px;
                height: inherit;
                font-size: 18px;
                font-weight: 500;
                text-align: center;
                line-height: 60px;
                color: rgba(51, 51, 51, 1);
                position: relative;
                cursor: pointer;

                .icon {
                    width: 18px;
                    height: 18px;
                    position: absolute;
                    left: 23px;
                    top: 20px;
                    background-image: url(../../../../assets/images/productList/分类.png);
                    background-size: 18px 18px;
                }
            }

            & > div:first-child {
                color: #fff;
                background: rgba(33, 110, 198, 1);
            }
        }
    }

    main {
        padding: 20px 0 60px 0;
        background-color: #f5f5f5;
    }

    .content {
        width: 1326px;
        justify-content: space-between;
    }

    .product-box {
        width: 1060px;
        //min-width: 1326px;

        .productClass {
            padding: 10px;
            line-height: 28px;

            div {
                height: 28px;
                padding: 0 10px;
                border: 1px solid lightgray;
                user-select: none;
            }
        }

        .product-list {
            //width: inherit;
            min-height: 800px;
            display: flex;
            flex-wrap: wrap;

            .item {
                width: 250px;
                height: 390px;
                font-size: 14px;
                background-color: #fff;
                position: relative;
                transition: opacity 0.5s;
                overflow: hidden;
                //margin-right: 20px;
                img {
                    width: 250px;
                    height: 200px;
                    object-fit: cover;
                }

                span {
                    margin-bottom: 5px;
                }

                .title, .description {
                    padding: 0 24px;
                }

                .title {
                    line-height: 20px;
                    color: rgba(51, 51, 51, 1);
                }

                .description {
                    max-width: 90%;
                    height: 20px;
                    line-height: 20px;
                    color: rgba(153, 153, 153, 1);
                }

                .price {
                    padding-left: 20px;
                    margin: 8px 0 5px;
                    font-size: 18px;
                    font-weight: 500;
                    line-height: 23px;
                    color: rgba(212, 48, 48, 1);
                }

                .productTag, .shopName, .actionBtn {
                    position: absolute;
                }

                .productTag {
                    width: 78px;
                    height: 26px;
                    font-size: 12px;
                    line-height: 26px;
                    text-align: center;
                    right: 6px;
                    top: 6px;
                    color: rgba(212, 48, 48, 1);
                    border: 1px solid rgba(230, 117, 117, 1);
                    background-color: rgba(250, 232, 232, 1);
                }

                .shopName {
                    max-width: 202px;
                    left: 24px;
                    bottom: 68px;
                    font-size: 14px;
                    color: rgba(33, 110, 198, 1);
                }

                .actionBtn {
                    width: 195px;
                    height: 30px;
                    margin-left: 28px;
                    font-size: 14px;
                    color: rgba(51, 51, 51, 1);
                    border: 1px solid rgba(227, 227, 227, 1);
                    bottom: 20px;

                    div {
                        height: 100%;
                        flex-grow: 1;

                        .el-checkbox {
                            width: 100%;
                            height: 100%;
                        }

                        .el-checkbox__inner {
                            color: rgba(204, 204, 204, 1) !important;
                            border-radius: 0;
                        }

                        .el-icon {
                            font-size: 18px;
                        }

                        .el-icon-star-off::before {
                            color: rgba(204, 204, 204, 1);
                        }

                        .el-icon-star-on::before {
                            color: orange;
                        }
                    }

                    div:first-child {
                        border-right: 1px solid rgba(227, 227, 227, 1);
                    }
                }
            }
        }
    }

    .compare-section {
        width: 100%;
        height: 180px;
        padding: 30px 0;
        background-color: #fff;
        border-top: 5px solid rgba(33, 110, 198, 1);
        position: sticky;
        bottom: 0;
        z-index: 101;

        .compare-item {
            width: 300px;
            height: 121px;
            padding-left: 50px;
            border-right: 1px dashed rgba(128, 128, 128, 1);

            &:hover {
                .del {
                    display: block !important;
                }
            }

            img {
                width: 100px;
                height: 100px;
                margin: 5px 0;
            }

            .num-block {
                width: 90px;
                height: 90px;
                margin: 15px 12px 15px 0;
                font-size: 44px;
                line-height: 90px;
                text-align: center;
                color: rgba(179, 179, 179, 1);
                background-color: rgba(232, 232, 232, 1);
            }

            .right {
                padding-top: 25px;

                .textOverflow2 {
                    min-height: 40px;
                    margin: 0 2px 0 5px;
                    font-size: 14px;
                }

                .price {
                    font-size: 18px;
                    color: rgba(212, 48, 48, 1);
                }

                .del {
                    margin-left: 5px;
                    line-height: 20px;
                    color: rgba(33, 110, 198, 1);
                    display: none;
                }

                .el-icon {
                    font-size: 16px;
                }
            }
        }

        .compare-btns {
            margin-left: 35px;
        }

        button {
            width: 80px;
            height: 40px;
            margin-bottom: 16px;
            font-size: 16px;
            border: 0;
            color: #fff;
            background-color: rgba(33, 110, 198, 1);
        }

        .pointer {
            font-size: 14px;
            color: rgba(33, 110, 198, 1);
        }
    }
}

.messageBox {
    border-radius: 0;
}
</style>