<template>
  <div>
    <main class="all">
      <el-header>安全中心</el-header>
      <el-card class="RetrievePassword">
        <div class="boxTop">找回密码</div>
        <div class="set-password">
          <div class="title">设置新密码</div>
          <el-input show-password v-model="newPassword1" placeholder="请输入新密码" style="margin-bottom: 20px">
          </el-input>
          <el-input show-password v-model="newPassword2" placeholder="请重新输入新密码" style="margin-bottom: 20px">
          </el-input>
          <div class="IsOkTips">
            <div class="Isicon">
              <i v-show="IsTop1" class="el-icon-success"></i>
              <i v-show="!IsTop1" class="el-icon-error"></i>
            </div>
            <div class="Istxt">密码由8-16位数字、字母或符号组成</div>
          </div>
          <div class="IsOkTips">
            <div class="Isicon">
              <i v-show="IsTop2" class="el-icon-success"></i>
              <i v-show="!IsTop2" class="el-icon-error"></i>
            </div>
            <div class="Istxt">至少包含2种以上字符</div>
          </div>
          <button class="okbtn" @click="handleSubmit">确定</button>
          <div class="tips">安全提示：新密码请勿与旧密码过于相似</div>
        </div>
      </el-card>
    </main>
  </div>
</template>

<script>
import { encrypt } from '@/utils/common'
import { updatePasswordByNoLogin } from '@/api/frontStage/login'
export default {
    components: {},
    data () {
        return {
            IsTop1: false,
            IsTop2: false,
            newPassword1: '',
            newPassword2: '',
            loginLoading: false,
        }
    },
    methods: {
        handleSubmit () {
            let len1 = this.newPassword1.trim().length
            let len2 = this.newPassword2.trim().length
            if (len1 === 0) {
                return this.$message.error('请输入新密码！')
            }
            if (len1 < 8 || len1 > 16) {
                return this.$message.error('请输入8到16位的密码！')
            }
            if (!new RegExp(/^(?=.*[0-9])(?=.*[a-zA-Z])(?=.*[^a-zA-Z0-9])(.{8,20})$/).test(this.newPassword1)) {
                return this.$message.error('密码必须同时包含数字，字母和特殊字符！')
            }
            if (len2 === 0) {
                return this.$message.error('请再次输入新密码！')
            }
            if (len1 !== len2 || this.newPassword1 !== this.newPassword2) {
                return this.$message.error('两次输入的密码不一致！')
            }
            if (len2 < 8 || len2 > 16) {
                return this.$message.error('请输入8到16位的密码！')
            }
            if (!new RegExp(/^(?=.*[0-9])(?=.*[a-zA-Z])(?=.*[^a-zA-Z0-9])(.{8,20})$/).test(this.newPassword2)) {
                return this.$message.error('密码必须同时包含数字，字母和特殊字符！')
            }
            let enPass = encrypt(this.newPassword1)
            this.loginLoading = true
            updatePasswordByNoLogin({ userPhone: localStorage.getItem('phone'), newPassword: enPass }).then(res => {
                if (res.code == 200) {
                    this.loginLoading = false
                    this.$message.success('密码修改成功')
                    setInterval(() => {
                        return this.$router.push('/login')
                    }, 2000)
                }
                this.loginLoading = false
            }).catch(() => {
                this.loginLoading = false
            })
        },
    },
    watch: {
        //监控密码是否符合要求
        newPassword1: {
            handler () {
                if (RegExp(/^(?=.*[0-9])(?=.*[a-zA-Z])(?=.*[^a-zA-Z0-9])(.{8,20})$/).test(this.newPassword1)) {
                    this.IsTop1 = true
                }else {
                    this.IsTop1 = false
                }
                if (RegExp(/^(?=.*[\dA-Za-z])(?=.*[\W_])|(?=.*\d)(?=.*[A-Za-z])|(?=.*[\W_])(?=.*[A-Za-z])|(?=.*\d)(?=.*[\W_]).{8,20}$/).test(this.newPassword1)) {
                    this.IsTop2 = true
                }else {
                    this.IsTop2 = false
                }
            },
            immediate: true
        }
    }
}
</script>
<style scoped>
.title{
  font-weight: 560;
  margin-bottom: 20px;
}
.okbtn{
  font-size: 20px;
  font-weight: 540;
  width: 340px;
  height: 45px;
  border-radius: 15px;
  color: white;
  background-color: rgb(0,155,237);
  margin-bottom: 10px;
  margin-top: 40px;
}
.tips{
  color: rgb(149,146,159);
  font-size: 13px;
  font-weight: 560;
}
.IsOkTips{
  display: flex;
  color: rgb(0,217,232);
}
.Isicon{
  line-height: 20px;
  text-align: center;
  width: 20px;
  height: 20px;
}
.Istxt{
  height: 20px;
  width: 250px;
}
.el-icon-success{
  color: rgb(0,217,232);
}
.el-icon-error{
  color: red;
}
.all {
  background-color: rgb(249, 251, 253);
  width: 100%;
  height: 80%;
}
.el-header {
  width: 100%;
  height: 70px;
  background-color: rgb(57, 117, 225);
  color: white;
  font-size: 20px;
  line-height: 60px;
  padding-left: 30px;
  margin-bottom: 50px;
}
.el-card {
  margin: 0 auto;
  width: 570px;
  height: 500px;
  padding-top: 50px;
  padding-left: 95px;
  padding-right: 95px;
}
.boxTop {
  border-style: solid;
  border-color: blue;
  padding-left: 10px;
  border-right: none;
  border-top: none;
  border-bottom: none;
  margin-bottom: 30px;
  font-size: 20px;
  font-weight: 550;
}
</style>
