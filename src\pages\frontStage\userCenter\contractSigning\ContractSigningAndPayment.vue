<template>
  <!-- 新 店铺合同签约缴费 -->
  <div class="ContractSigning">
    <div class="boxTop">
      <div class="title center">企业用户</div>
    </div>
    <div class="steps">
      <el-steps :active="3" align-center>
        <el-step title="注册"></el-step>
        <el-step title="平台初审"></el-step>
        <el-step title="申请开店"></el-step>
        <el-step title="合同签约及缴费"></el-step>
        <el-step title="平台复审"></el-step>
        <el-step title="完成"></el-step>
      </el-steps>
    </div>
    <div class="contractsigningform">
      <div class="txt">请用对公账户打款到四川路桥建设集团物资有限责任公司对公账号，并将缴费凭证截图在此处上传</div>
      <div class="writeform">
          <el-form  :model="addFeeForm" label-width="168px" class="enterpriseForm center"
                    :inline="false" :rules="enterpriseFormRules" ref="enterpriseForm">
            <!--              开户行-->
            <el-row>
              <el-col :span="24">
                <el-form-item label="收款账户公司名称：">
                  <div>{{platformAccountObj.platformFreeyhOrgName}}</div>
                </el-form-item>
              </el-col>
            </el-row>
            <!--              收款账户、收款金额-->
            <el-row>
              <el-col :span="24">
                <el-form-item label="收款账户开户行：">
                  <div>{{platformAccountObj.platformFreeyhAddress}}</div>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="11">
                <el-form-item label="收款账户：">
                  <div>{{platformAccountObj.platformFreeyhAccount}}</div>
                </el-form-item>
              </el-col>
              <el-col :span="11">
                <el-form-item class="iconbtn">
                        <span slot="label">
                          <span class="span-box">
                            <i class="el-icon-warning" @click="open"/>
                            <span>收款金额：</span>
                          </span>
                        </span>
                  <span style="color: red">{{addFeeForm.payAmount}}</span><span>元 (年费+首次履约保证金)</span>
                </el-form-item>
              </el-col>
            </el-row>
            <!--              缴费方式、缴费时长-->
            <el-row>
              <el-col :span="11">
                <el-form-item label="缴费方式：" prop="payType">
                  <div><el-radio v-model="addFeeForm.payType" :label="1">线下</el-radio>
                    <el-radio v-model="addFeeForm.payType" disabled :label="2">线上</el-radio></div>
                </el-form-item>
              </el-col>
              <el-col :span="11">
                <el-form-item label="缴费时长：" prop="paymentDuration">
                  <div><el-input style="width: 180px" placeholder="请输入缴费时长" size="mini" v-model="addFeeForm.paymentDuration" @change="checkInputQty"></el-input>年</div>
                </el-form-item>
              </el-col>
            </el-row>
            <!--              缴费凭证-->
            <el-row>
              <el-col :span="13">
                <el-form-item label="缴费凭证：" prop="enterpriseName">
                  <div><el-upload
                      class="avatar-uploader"
                      action=""
                      :show-file-list="false"
                      :http-request="uploadLicenseBusiness"
                      :before-upload="handleBeforeUpload">
                    <img v-if="fielImage" :src="fielImage" class="avatar" alt=" " height="90px" width="90px">
                    <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                  </el-upload></div>
                  <div class="txt2">请上传首次年费及首次履约保证金缴费凭证</div>
                </el-form-item>
              </el-col>
            </el-row>
            <!--              备注-->
            <el-row>
              <el-col :span="24">
                <el-form-item label="备注：" prop="remarks">
                  <textarea v-model="addFeeForm.remarks" style="width: 90%"></textarea>
                </el-form-item>
              </el-col>
            </el-row>
            <!--            按钮-->
            <el-form-item class="contractsigningBtn">
              <el-col :span="11"><el-button @click="unload()">下载合同</el-button></el-col>
              <el-col :span="11"><el-button @click="okbtn()">提交</el-button></el-col>
            </el-form-item>
          </el-form>
<!--        合同下载表单弹窗-->
        <el-dialog :visible.sync="dialogVisible" width="50%" style="text-align: center">
          <div style="color: rgb(200,0,0);font-size: 12px;margin-bottom: 30px">提示：尊敬的商家，请您提供详细而准确的邮寄地址，包括具体街道、门牌号码以及相关联系方式，该地址将用于后续合同的邮递服务。</div>
          <el-form ref="unloadForm" :model="unloadform" label-width="300px" class="enterpriseForm center" :inline="false" :rules="unloadFormRules">
            <el-row>
              <el-col :span="18">
                <el-form-item label="联系人：" prop="contact">
                  <el-input v-model="unloadform.contact" size="medium"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="18">
                <el-form-item label="联系电话：" prop="contactPhone">
                  <el-input v-model="unloadform.contactPhone" size="medium"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="18">
                <el-form-item label="地址：" prop="address">
                  <el-select
                    ref="selectLabel1"
                    v-model="unloadform.provinces"
                    placeholder="省份"
                    style="width: 30%"
                    @change="(code) => getSubDistrict(code, 1)"
                  >
                    <el-option
                      v-for="item in addressOptions.province"
                      :key="item.value"
                      :label="item.districtName"
                      :value="item.districtCode"
                    />
                  </el-select>
                  <el-select ref="selectLabel2" v-model="unloadform.city" value-key="" placeholder="市级"
                             @change="(code) => getSubDistrict(code, 2)" style="width: 30%">
                    <el-option
                      v-for="item in addressOptions.city"
                      :key="item.value"
                      :label="item.districtName"
                      :value="item.districtCode"
                    />
                  </el-select>
                  <el-select ref="selectLabel3" @visible-change="addressChange" v-model="unloadform.county" value-key="" placeholder="区、县" style="width: 40%">
                    <el-option
                      v-for="item in addressOptions.district"
                      :key="item.value"
                      :label="item.districtName"
                      :value="item.districtCode"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="18">
                <el-form-item label="详细联系地址：" prop="mailAddress">
                  <el-input v-model="unloadform.mailAddress" size="medium"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <div style="text-align: left">合同步骤如下:<br>
            1.下载合同，打印后签字盖章，，一式四份邮寄<br>
            说明:<br>
            a.留一个有效的地址便条[回寄地址，联系人，联系电话]，放入邮件包中，方便回邮合同:<br>
            b.平台邮寄地址xxxxxxxxxxxxxxxxxxxxxxx<br>
            2.平台收到合同后，审核盖章,平台根据地址便条内容，回邮合同 (两份)，请注意查收。<br>
            后续步骤如下:<br>
            (1).使用对公账户打款到四川路桥建设集团股份有限公司装备技术服务分公司的对公账号(县体账号请查看合同)，并在平台上传缴费凭证截图。(2).等待平台复审。</div>
          <span slot="footer" class="dialog-footer">
        <el-button @click="downloadHetong" class="dialogfooterbtn">下载合同</el-button>
<!--            <div class="dialogfootertxt">点击提交需验证必填项，合同编号自动生成<br>
                  WZGSDS-{年}-{0001}<br>
                  {年}为具体年费，{0001}为流水号，<br>流水号自增长</div>-->
      </span>
        </el-dialog>
      </div>
    </div>
  </div>
</template>
<script>
import { getCascaderOptions } from '@/api/platform/common/components'
import { createFeeAndDealFree, reCreateContract } from '@/api/fee/feeApi'
import { uploadFile, previewFile } from '@/api/platform/common/file'
import { getPlatformFreeAccountAndAddress } from '@/api/shopManage/fileRecordDelete/fileRecordDelete'
export default {
    data  () {
        return {
            unloadFormRules: {
                contact: [
                    { required: true, message: '请输入联系人',  trigger: 'blur' }
                ],
                contactPhone: [
                    { required: true, message: '请输入11位手机号', trigger: 'blur' },
                    { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
                ],
                address: [
                    { required: true, validator: this.validateAddress, trigger: 'change' }
                ],
                mailAddress: [
                    { required: true, message: '请填写详细联系地址', trigger: 'blur' },
                    { min: 1, max: 100, message: '超过限制', trigger: 'blur' }
                ],
            },
            enterpriseFormRules: {
                paymentDuration: [
                    { required: true, message: '请输入缴费时长',  trigger: 'blur' },
                    { pattern: /^(?!(0[0-9]{0,}$))[0-9]{1,}[.]{0,}[0-9]{0,}$/, message: '请输入大于0的数字', trigger: 'blur' }
                ],
                payType: [
                    { required: true, message: '请选择缴费方式',  trigger: 'blur' }
                ],
                enterpriseName: [
                    { required: true, validator: this.validateImage,  trigger: 'change' }
                ],
            },
            addFeeForm: {
                state: 0,
                payType: 1,
                recordType: 1,
                paymentDurationType: 4,
                paymentDuration: 0,
                files: [],
                payAmount: 0,
                remarks: null,
                payStatus: 0,
            },
            fielImage: '',
            platformAccountObj: {},
            dialogVisible: false,
            unloadform: {
                contact: '',
                contactPhone: '',
                provinces: '',
                city: '',
                county: '',
                mailAddress: ''
            },
            addressOptions: {
                province: [],
                city: [],
                district: []
            },
        }
    },
    methods: {
        open () {
            const imgSrc = require('@/assets/AmountCollected.png')
            this.$alert(`<img src="${imgSrc}" alt=" " >`,  {
                dangerouslyUseHTMLString: true,
                showClose: false,
                center: true,
                customClass: 'openbox',
                confirmButtonText: '关闭',
                confirmButtonClass: 'openConfirmButton',
            })
        },
        validateImage (rule, value, callback) {
            if (this.fielImage === '') {
                callback(new Error('请上传缴费凭证'))
            } else {
                callback()
            }
        },
        unload () {
            this.$refs['enterpriseForm'].validate(valid => {
                if(!valid) return
                this.dialogVisible = true
                /*this.$nextTick(() => {
                    this.$refs['unloadForm'].resetFields()
                })*/
            })
        },
        // 获取地址选择器数据
        async getAddressPickerOptions () {
            let res = await getCascaderOptions({ distCode: '100000' })
            this.addressOptions.province = res
        },
        // 获取子级地区
        getSubDistrict (code, layer) {
            getCascaderOptions({ distCode: code }).then(res => {
                layer === 1 ? this.addressOptions.city = res : this.addressOptions.district = res
            })
        },
        // 校验地址信息
        validateAddress (rule, value, callback) {
            if ( this.unloadform.provinces == null || this.unloadform.provinces == '' ) {
                return callback(new Error('请选择省份！'))
            }
            if ( this.unloadform.city == null || this.unloadform.city == '' ) {
                return callback(new Error('请选择市级！'))
            }
            // if ( this.enterpriseForm.county == null || this.enterpriseForm.county == '' ) {
            //     return callback(new Error('请选择县、区！'))
            // }
            callback()
        },
        addressChange (val) {
            if(!val) {
                this.unloadform.provinces = this.$refs.selectLabel1.selectedLabel
                this.unloadform.city = this.$refs.selectLabel2.selectedLabel
                this.unloadform.county = this.$refs.selectLabel3.selectedLabel
                let newAddress = this.unloadform.provinces + this.unloadform.city + this.unloadform.county
                this.unloadform.mailAddress = newAddress
            }
        },
        okbtn () {
            console.log('999', this.unloadform)
            if (this.unloadform.contact == '' || this.unloadform.contactPhone == '' || this.unloadform.mailAddress == '') {
                return this.$message.error('请先下载合同')
            }
            this.addFeeForm.submitAud = 1
            this.addFeeForm = { ...this.addFeeForm, ...this.unloadform }
            console.log(this.addFeeForm)
            createFeeAndDealFree(this.addFeeForm).then(res => {
                if (res.code == null) {
                    reCreateContract().then()
                    this.$message.success('操作成功')
                    this.$router.push('/mFront/PlatformReview')
                }
            })
        },
        uploadLicenseBusiness (params) {
            let file = params.file
            const form = new FormData()
            form.append('files', file)
            form.append('bucketName', 'mall-private') //存储桶名称
            form.append('directory', 'material') // 商城类型
            form.append('fileType', 1)
            uploadFile(form).then(res => {
                if(res.code != null && res.code != 200) {
                    this.addFeeForm.files = []
                }else {
                    previewFile({ recordId: res[0].recordId }).then(res => {
                        let url = window.URL.createObjectURL(res)
                        this.fielImage = url
                    })
                    let resO = res[0]
                    this.addFeeForm.files.push({
                        name: resO.objectName,
                        relevanceType: 1,
                        url: resO.nonIpObjectPath,
                        fileFarId: resO.recordId,
                        fileType: 1
                    })
                }
                this.$message({
                    message: '上传成功',
                    type: 'success'
                })
            })
        },
        // 判断上传的图片大小
        handleBeforeUpload (file) {
            if(file.size / 1024 / 1024 > 4) {
                this.$message.error('上传的图片大小不能超过4MB!')
                return false
            }
            if(file.size / 1024 < 100) {
                this.$message.error('请上传100KB以上的文件')
                return false
            }
            return true
        },
        checkInputQty () {
            this.addFeeForm.payAmount = this.addFeeForm.paymentDuration * 3000 + 20000
        },
        downloadHetong () {
            this.$refs['unloadForm'].validate(valid => {
                if(!valid) return
                this.dialogVisible = false
            })
            //reCreateContract().then()
        },
    },
    created () {
        this.getAddressPickerOptions()
        getPlatformFreeAccountAndAddress().then(res => {
            this.platformAccountObj = res
        })
    },
}
</script>
<style scoped lang="scss">
.ContractSigning {
  flex-direction: column;
  //border-style: dashed;
  height: 100%;
}
.boxTop {
  //border-style: dot-dash;
  height: 87px;
  border-bottom: 1px solid #D9D9D9;
  .title {
    width: 200px;
    height: 100%;
    font-size: 26px;
    font-weight: 500;
    line-height: 87px;
    text-align: center;
    border-bottom: 4px solid #216EC6;
    color: #333;
    user-select: none;
  }
}
.steps{
  //border-style: solid;
  margin: 0 auto;
  height: 10%;
  padding:50px 0;
  width: 800px;
}
.txtbox{
  margin: 0 auto;
  height: 150px;
  padding:50px 0;
  width: 1000px;
  .txt{
    font-weight: bold;
    font-size: 35px;
  }
}
.form{
  //border-style: solid;
  margin: 0 auto;
  height: 500px;
  padding:50px 0;
  width: 900px;
}
.contractsigningform{
  width: 100%;
  height: 100%;
  margin-top: 1%;
  //border-style: solid;
  display: flex;
  flex-flow:row wrap;
  flex-direction: column;
  align-items:center;
  .txt{
    margin: 1% 0;
    width: 100%;
    height: 25px;
    text-align: center;
    color: red;
    font-weight: bold;
    font-size: 15px;
  }
  .writeform{
    width: 800px;
    height: 500px;
  }
}
.elinput {
  width: 400px;
  :deep(.el-input__inner) {
    height: 40px;
    line-height: 40px;
    padding: 0 15px;
  }
  :deep(.el-input) {
    height: 60px;
    width: 200px;
  }
}
.namebox{
  //border-style: dashed;
  display: flex;
  justify-content: space-between;
  height: 80px;
  width: 100%;
  .namebox1{
    //border-style: solid;
    width: 200px;
    height: 40px;
  }
  .namebox2{
    height: 80px;
    border-style: dashed;
    background-color: rgb(255,235,229);
    border-color: rgb(255,51,11);
    color: rgb(255,51,113);
    width: 300px;
    text-align: center;
    line-height: 80px;
  }

}
.read{
  width: 300px;
  height: 30px;
  padding-right: 100px;
  margin-left: 50px;
}
/deep/ .el-button {
  width: 150px;
  height: 50px;
//  padding: 0 0;
//  text-align: center;
  font-size: 20px;
  border: 1px solid rgba(33, 110, 198, 1);
  border-radius: 0;
  color: white;
  background-color: rgba(33, 110, 198, 1);
//  margin-left: 200px;
}
.avatar-uploader{
  border-style: solid;
  border-color: rgb(195,218,234);
  width: 100px;
  height: 100px;
  text-align: center;
}
.avatar-uploader-icon{
  font-size: 28px;
  line-height: 100px;
}

textarea {
  width: 300px;
  height: 100px;
  resize: none;
  outline: none
}
.txt2{
  width: 100%;
  height: 25px;
  margin-top: 10px;
  text-align: left;
  color: red;
  font-weight: bold;
  font-size: 12px;
}
.el-icon-warning:hover{
  cursor:pointer;
}
.iconbtn{
  width: 420px;
}
.elinput {
  //border-style: solid;
  width: 200px;
  height: 70px;
}
.el-input >>> .el-input__inner {
  height: 100%
}
.unloadformitem{
  margin-bottom: -20px;
  //border-style: solid;
  width: 450px;
  height: 70px;
  margin-left: 200px;
  padding-top: 20px;
  padding-left: 100px;
}
.unloadformitem1{
  //border-style: solid;
  width: 450px;
  height: 70px;
  margin-left: 200px;
  padding-top: 20px;
  padding-left: -100px;
}
.unloadformitem2{
  margin-bottom: -20px;
  //border-style: solid;
  width: 600px;
  height: 70px;
  margin-left: 165px;
  padding-top: 20px;
  padding-left: -100px;
}
.province{
  //border-style: solid;
  margin-right: 10px;
  margin-left: 10px;
  width: 80px;
  height: 50px;
}
.city{
  margin-left: 10px;
  //margin-right: 10px;
  width: 80px;
  height: 50px;
}
.county{
  margin-left: 10px;
  //margin-right: 20px;
  width: 100px;
  height: 50px;
}
.dialog-footer{
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}
.dialogfooterbtn{
  margin-top: 20px;
}
.dialogfootertxt{
  border-color: rgb(255,51,11);
  color: rgb(255,51,113);
  background-color: rgb(255,235,229);
  font-size: 12px;
  border-style: dashed;
  width: 250px;
  height: 70px;
}
</style>
<style>
.unload{
  width: 60%;
  height: 500px;
}
.openbox{
  width: 55%;
  height: 700px;
}
.openConfirmButton {
  width: 120px;
  height: 40px;
  line-height: 40px;
  background-color: #fff !important;
  border-color: #dcdfe6 !important;
  color: #000 !important;
}

</style>
