<template>
    <main>
        <div v-loading="contractDetailLoading">
            <div class="list-title dfa mb20">计划信息</div>
            <div class="detailBox">
                <div class="row">
                    <div class="col">
                        <div class="name">
                            计划编号：
                        </div>
                        {{ plan.billNo }}
                    </div>
                    <div class="col">
                        <div class="name">
                            计划日期：
                        </div>
                        {{ plan.billDate | dateStr }}
                    </div>
                </div>
                <div class="row">
                    <div class="col">
                        <div class="name">
                            计划金额：
                        </div>
                        {{ plan.planAmount }}
                    </div>
                </div>
                <div class="row">
                    <div class="col">
                        <div class="name">
                            备注：
                        </div>
                        {{ plan.remark }}
                    </div>
                </div>
            </div>
            <div class="list-title dfa mb20">计划明细<el-button v-show="true" :disabled="!ordable" type="primary" style="position: absolute; right: 40px" @click="submitOrderByPlan">生成订单</el-button></div>
            <el-table
                ref="msgTable"
                :data="plan.details"
                :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
                :row-style="{ fontSize: '14px', height: '48px' }"
            >
                <el-table-column label="序号" type="index" width="60"></el-table-column>
                <el-table-column prop="tradeName" label="商品名称" width="200"/>
                <el-table-column prop="materialName" label="物资名称" width="200"/>
                <el-table-column prop="spec" label="规格型号" />
                <el-table-column prop="className" label="类别名称" />
                <el-table-column prop="price" label="单价" />
                <el-table-column prop="quantity" label="数量" />
                <el-table-column prop="amount" label="总金额" />
                <el-table-column prop="consumeAmount" label="已下单金额" />
                <el-table-column prop="consumeNumber" label="已下单数量" />
                <el-table-column label="操作" >
                    <template v-slot="scope">
                        <div class="pointer" v-show="ordable && scope.row.Number != scope.row.consumeNumber" style="color: rgba(33, 110, 198, 1);" @click="submitOrder(scope.row)">去下单</div>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <template
            v-if="planAudits.length>0">
            <div class="list-title dfa mb20">审核记录</div>
            <el-table
                :data="planAudits"
                :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
                :row-style="{ fontSize: '14px', height: '48px' }"
            >
                <el-table-column label="序号" type="index" width="60"></el-table-column>
                <el-table-column prop="gmtModified" label="驳回时间" width="200"/>
                <el-table-column prop="reason" label="驳回理由"/>
            </el-table>

        </template>
        <!-- 是审核人，是推送pcwp的计划，是本部门的经办人推送的计划 -->
        <!-- TODO 这里审核人可能属于多个部门，这种场景下这个审核权限如何判定 -->
        <div class="buttons">
            <div class="button-group">
                <el-button
                    type="primary"
                    class="btn-delete"
                    v-if="isAuditor && !plan.pbillId && plan.orgId==userInfo.orgId && plan.state === '4'"
                    @click="handleCancel(plan.billId)"
                >作废</el-button>
                <el-button
                    type="primary"
                    v-if="isHandler && !plan.pbillId && plan.founderId==userInfo.userId && plan.state === '0'"
                    @click="handleSubmit(plan.billId)"
                >提交</el-button>
                <el-button
                    type="primary"
                    class="btn-delete"
                    v-if="isAuditor && !plan.pbillId && plan.orgId==userInfo.orgId && (plan.state === '0' || plan.state === '-1')"
                    @click="handleRemove(plan.billId)"
                >删除</el-button>
                <el-button
                    type="primary"
                    v-if="autitable"
                    @click="auditPlanM(1, '通过')"
                >通过</el-button>
                <el-button
                    type="primary"
                    class="btn-delete"
                    v-if="autitable"
                    @click="auditPlanM(0, '未通过')"
                >不通过</el-button>
                <el-button
                    type="primary"
                    class="btn-delete"
                    v-if="isHandler && !plan.pbillId && plan.founderId==userInfo.userId && plan.state === '1'"
                    @click="handleRevoke(plan.billId)"
                >撤回</el-button>
                <el-button type="primary" @click="exportExcel">导出excel</el-button>
                <el-button @click="$router.go(-1)">返回</el-button>
            </div>
        </div>
    </main>
</template>

<script>

import { mapState } from 'vuex'
// import { getPCWP2InterfacePlanById } from '@/api/plan/plan'
// import { getPlanDetailById, getAuditInfos, changePlanState } from '@/api/plan/plan'
import { getAuditInfos, deletePlanAndDetails, getPlanDetailById, changePlanState } from '@/api/plan/plan'
import { UserPermission } from '@/utils/permissions'
import { toFixed } from '@/utils/common'
import { ordable } from './index.vue'
import * as XLSX from 'xlsx'
import { saveAs } from 'file-saver'

export default {
    filters: {
        dateStr (dateStr) {
            if(dateStr == null) {
                return ''
            }
            return dateStr.split('T')[0]
        }
    },
    name: 'detail',
    data () {
        return {
            contractDetailLoading: false,
            plan: {},
            // 数据权限
            userPermission: new UserPermission('物资下单权限'),
            disableElement: false,
            // 添加审核状态对象
            auditState: {
                canModify: true,
                canAudit: false,
                canUndoAudit: false
            },
            planAudits: []
        }
    },
    created () {
        if (this.$route.query.orderAll) {
            // 一键下单
            this.submitOrderByPlan()
        } else{
            this.getPlantDetailM()
            this.fetchAuditInfos()
        }
    },

    computed: {
        ...mapState(['userInfo']),
        ordable () {
            return ordable(this.plan, this.userInfo)
        },
        autitable () {
            // 非审核人 不能审核
            if (!this.isAuditor) {
                return false
            }
            // 数据未加载完成 不能审核
            if (!this.plan.billId) {
                return false
            }
            // pcwp计划 不能审核
            if (this.plan.pbillId) {
                return false
            }
            // 非本部门计划 不能审核
            if (this.plan.orgId !== this.userInfo.orgId) {
                return false
            }
            // 状态不是待审核 不能审核
            if (this.plan.state !== '1') {
                return false
            }
            return true
        },
        isHandler () {
            // TODO 还未实现的功能 暂时这样方便测试
            return true
        },
        isAuditor () {
            // TODO 还未实现的功能 暂时这样方便测试
            return true
        },
        // isHandler () {
        //     return this.userInfo.roles.includes('计划-经办人')
        // },
        // isAuditor () {
        //     return this.userInfo.roles.includes('计划-审核人')
        // },
    },
    methods: {
        fixed4 (num) {
            return toFixed(num, 4)
        },
        submitOrderByPlan () {
            this.$router.push({
                path: '/user/submitOrderByPlan',
                query: { billId: this.$route.query.billId, orderAll: this.$route.query.orderAll }
            })
        },
        getPlantDetailM () {
            this.contractDetailLoading = true
            getPlanDetailById({ id: this.$route.query.billId }).then(res => {
                this.contractDetailLoading = false
                // if (res.orgId) {
                //     this.userPermission.isSameOrg(res.orgId) ? '' : this.disableElement = true
                // }
                this.plan = res
                // this.plan.details.forEach(item=>{
                //     item.Amount = this.fixed4(item.Number * item.Price)
                // })
            }).catch(() => {
                this.contractDetailLoading = false
            })
            // getPCWP2InterfacePlanById({ id: this.$route.query.billId }).then(res => {
            //     this.contractDetailLoading = false
            //     if (res.orgId) {
            //         this.userPermission.isSameOrg(res.orgId) ? '' : this.disableElement = true
            //     }
            //     this.plan = res
            //     this.plan.details.forEach(item=>{
            //         item.Amount = this.fixed4(item.Number * item.Price)
            //     })
            // }).catch(() => {
            //     this.contractDetailLoading = false
            // })
        },
        // 去下单
        submitOrder (row) {
            this.$router.push({
                path: '/user/submitOrderByPlan',
                query: { billId: this.$route.query.billId, dtlId: row.dtlId }
            })
        },
        // 添加这个新方法来处理审核按钮操作
        exas (type, routeTo) {
            switch(type) {
            case 'audit':
                // 处理审核操作
                this.$message.success('审核操作')
                break
            case 'undoAudit':
                // 处理撤回审核操作
                this.$message.success('撤回操作')
                break
            case 'submitCommit':
                // 处理保存并提交操作
                this.$message.success('保存并提交操作')
                break
            case 'submit':
                // 处理保存操作
                this.$message.success('保存操作')
                break
            case 'nullify':
                // 处理作废操作
                this.$message.success('作废操作')
                break
            case 'deleteData':
                // 处理删除操作
                this.$message.success('删除操作')
                break
            default:
                break
            }
            // 如果routeTo是一个函数，则调用它
            if(typeof routeTo === 'function') {
                routeTo()
            }
        },
        // 添加审核方法
        auditPlanM (state, title) {
            this.clientPop('info', '您确定进行【' + title + '】操作吗！', async () => {
                if(state === 0) {
                    this.$prompt('未通过原因', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'error',
                        inputType: 'textarea',
                        inputPlaceholder: '请输入不通过原因',
                        inputPattern: /^.+$/,
                        inputErrorMessage: '请输入不通过原因'
                    }).then(({ value: reason }) => {
                        changePlanState(this.plan.billId, '4', { reason }).then(res => {
                            if (res.code != 200) {
                                return
                            }
                            this.$message.success('驳回成功')
                            this.getPlantDetailM()
                            this.fetchAuditInfos()
                        })
                    }).catch(() => {
                        // 取消操作
                    })
                } else {
                    changePlanState(this.plan.billId, '2').then(res => {
                        if (res.code != 200) {
                            return
                        }
                        this.$message.success('审核成功')
                        this.getPlantDetailM()
                    })
                }
            })
        },
        handleCancel (billId) {
            changePlanState(billId, '-1').then(res => {
                if (res.code != 200) {
                    return
                }
                this.$message.success('作废成功')
                this.getPlantDetailM()
            })
        },
        handleSubmit (billId) {
            changePlanState(billId, '1').then(res => {
                if (res.code != 200) {
                    return
                }
                this.$message.success('提交成功')
                this.getPlantDetailM()
            })
        },
        handleRevoke (billId) {
            this.clientPop('info', '您确定要撤回此计划吗？', async () => {
                changePlanState(billId, '0').then(res => {
                    if (res.code != 200) {
                        return
                    }
                    this.$message.success('撤回成功')
                    this.getPlantDetailM()
                })
            })
        },
        handleRemove (billId) {
            this.clientPop('info', '您确定要删除此计划吗？', async () => {
                deletePlanAndDetails(billId).then(res=>{
                    if (res.code != 200) {
                        return
                    }
                    this.$message.success('删除成功')
                    this.getPlantDetailM()
                })
            })
        },
        // 如果没有clientPop方法，添加此方法
        clientPop (type, message, callback) {
            this.$confirm(message, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: type
            }).then(() => {
                callback()
            }).catch(() => {
                // 取消操作
            })
        },
        fetchAuditInfos () {
            getAuditInfos(this.$route.query.billId).then(res => {
                this.planAudits = res
            })
        },
        exportExcel () {
            const plan = this.plan
            const planInfo = [
                ['计划编号：', plan.billNo],
                ['计划日期：', plan.billDate?.split('T')[0]],
                ['计划金额：', plan.planAmount],
                ['备注：', plan.remark],
                []
            ]
            const headers = ['商品名称', '物资名称', '规格型号', '类别名称', '单价', '数量', '总金额', '已下单金额', '已下单数量']

            const data = this.plan.details.map(d => {
                return [d.tradeName, d.materialName, d.spec, d.className, d.price, d.quantity, d.amount, d.consumeAmount, d.consumeNumber]
            })
            const worksheet = XLSX.utils.aoa_to_sheet([...planInfo, headers, ...data])
            // 合并单元格区域配置（planInfo 中第 0~3 行，第 1 列向右合并到第 3 列，即 B→D）
            worksheet['!merges'] = Array.from({ length: 4 }).map(( v, i) => ({
                s: { r: i, c: 1 }, e: { r: i, c: 4 }
            }))
            const workbook = XLSX.utils.book_new()
            XLSX.utils.book_append_sheet(workbook, worksheet, '计划详情')
            const excelBuffer = XLSX.write(workbook, {
                bookType: 'xlsx',
                type: 'array',
                cellStyles: true,
            })
            const blob = new Blob([excelBuffer], { type: 'application/octet-stream' })
            saveAs(blob, '计划详情.xlsx')
        }
    }
}
</script>

<style scoped lang="scss">
main {
    min-height: 894px;
    padding: 0 20px;
    border: 1px solid rgba(229, 229, 229, 1);
}

.list-title {
    padding: 0;
}

.detailBox {
    margin: 70px;
    .row {
        margin-bottom: 22px;
        color: rgba(51, 51, 51, 1);
        &, .col {
            display: flex;
        }
        .col {
            width: 50%;
        }
        .name {
            width: 90px;
            text-align: right;
            span {
                color: rgba(255, 95, 95, 1);
            }
        }
    }
}

// 为按钮部分添加样式
.buttons {
    margin-top: 20px;
    padding-bottom: 20px;
    border-top: 1px solid #eee;
    padding-top: 20px;
    .button-group {
        display: flex;
        justify-content: flex-end;
        padding-right: 40px;
    }
    .btn-delete {
        background-color: #f56c6c;
        border-color: #f56c6c;
    }
    .el-button {
        margin-left: 10px;
        min-width: 80px;
    }
}
</style>
