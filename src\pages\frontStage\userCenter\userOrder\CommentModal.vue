<template>
  <div class="modal-overlay">
    <div class="modal-container" :class="{ 'scale-in': true }">
      <div class="dialog-close" style="float: right" @click="$emit('close')">
        <img src="@/assets/images/close.png" alt=""/>
      </div>
      <div class="dialogTitle">评价订单
      </div>
      <div class="shortInfo dfc">
        <span class="order_sn">订单号：{{ form.orderNum }}</span>
      </div>
      <div class="shortInfo dfc header">
        <div class="notice"><img src="@/assets/images/userCenter/ico_notice.png" alt=""><span>请对订单进行评价</span></div>
        <div v-if="createTime" class="timeC">创建时间：{{ form.createTime }}</div>
      </div>
      <div class="review-scroll-container">
        <!-- 评分-内容填写 -->
        <div class="reviewBox df">
          <div class="detailReview">
            <div class="dfa">
              <span>商品品质</span>
              <el-rate v-model="form.commentLevel" @change="rating" class="el-rate"  :colors="['#FF9900','#FF9900','#FF9900']"></el-rate>
              <span>{{ form.commentLevel }}分</span>
            </div>
            <div class="dfa">
              <span>保供能力</span>
              <el-rate v-model="form.commentSupply" @change="rating" class="el-rate"  :colors="['#FF9900','#FF9900','#FF9900']"></el-rate>
              <span>{{ form.commentSupply }}分</span>
            </div>
          </div>
          <div class="detailReview">
            <div class="dfa">
              <span>诚信履约</span>
              <el-rate v-model="form.commentIntegrity" class="el-rate" @change="rating" :colors="['#FF9900','#FF9900','#FF9900']"></el-rate>
              <span>{{ form.commentIntegrity }}分</span>
            </div>
            <div class="dfa">
              <span>服务水平</span>
              <el-rate v-model="form.commentService" class="el-rate" @change="rating" :colors="['#FF9900','#FF9900','#FF9900']"></el-rate>
              <span>{{ form.commentService }}分</span>
            </div>
          </div>
        </div>
        <div class="textarea df">
          <span class="mt10">评价内容</span>
          <el-input type="textarea" :auto-resize="false" v-model="form.commentContent" placeholder="分享体验心得，给万千想购买的人一个参考~~" maxlength="50" show-word-limit></el-input>
        </div>
        <div class="picReview">
          <div class="dfa">
            <span class="mt10">上传图片</span>
            <el-upload :class="form.commentFile.length === 3 ? 'hide_box_min' : ''"
              v-loading="uploadLoading"
              class="upload-demo"
              action="fakeaction"
              :limit="3"
              :file-list="form.commentFile"
              :before-upload="handleBeforeUpload"
              :auto-upload="true"
              :on-change="minFileChange"
              list-type="picture-card">
              <!-- <div slot="tip" class="el-upload__tip">只能上传图片文件</div> -->
              <i slot="default" class="el-icon-plus"></i>
              <div slot="file" slot-scope="{file}">
                <img class="el-upload-list__item-thumbnail" :src="file.url" alt="">
                <span class="el-upload-list__item-actions">
                  <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
                    <i class="el-icon-zoom-in"></i>
                  </span>
                  <span
                      class="el-upload-list__item-delete"
                      @click="handleDownload2(file)">
                    <i class="el-icon-download"></i>
                  </span>
                  <span
                      class="el-upload-list__item-delete"
                      @click="formDtlFileRemove(file)">
                    <i class="el-icon-delete"></i>
                  </span>
                </span>
              </div>
            </el-upload>
          </div>
        </div>
        <!-- 商品评价 -->
        <div class="reviewList">
          <div class="titleBar mt10">
            <span>评价内容</span>
            <span>上传图片</span>
          </div>
          <div class="itemImg" v-for="(item, index) in form.orderItemComments" :key="item.productId">
            <div class="productImg">
              <img :src="item.productImg ? imgUrlPrefixAdd + item.productImg : require('@/assets/images/img/queshen5.png')" alt="">
            </div>
            <div class="itemHead dfa">
              <span :title="item.productName" class="mt10">{{ item.productName }}</span>
              <span>品牌:{{ item.brandName }}</span>
              <span>规格:{{ item.skuName }}</span>
            </div>
            <div class="textareaP df">
              <el-input type="textarea" :auto-resize="false" v-model="item.commentContent" placeholder="分享体验心得，给万千想购买的人一个参考~~" maxlength="50" show-word-limit></el-input>
            </div>
            <div class="picReviewP">
              <div class="dfa">
                <el-upload :class="item.commentFile.length === 1 ? 'hide_box_min' : ''"
                  v-loading="uploadLoading"
                  class="upload-demo"
                  action="fakeaction"
                  :limit="1"
                  :file-list="item.commentFile"
                  :before-upload="handleBeforeUpload"
                  :auto-upload="true"
                  :on-change="(file, fileList) => childFileChange(file, fileList,index)"
                  list-type="picture-card">
                  <!-- <div slot="tip" class="el-upload__tip">只能上传图片文件</div> -->
                  <i slot="default" class="el-icon-plus"></i>
                  <div slot="file" slot-scope="{file}">
                    <img class="el-upload-list__item-thumbnail" :src="file.url" alt="">
                    <span class="el-upload-list__item-actions">
                      <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
                        <i class="el-icon-zoom-in"></i>
                      </span>
                      <span
                          class="el-upload-list__item-delete"
                          @click="handleDownload2(file)">
                        <i class="el-icon-download"></i>
                      </span>
                      <span
                          class="el-upload-list__item-delete"
                          @click="formDtlFileRemove2(file, index)">
                        <i class="el-icon-delete"></i>
                      </span>
                    </span>
                  </div>
                </el-upload>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="publish dfc">
        <button @click="handlePublish">发表</button>
      </div>
    </div>
    <el-dialog class="showImage" v-dialogDrag :visible.sync="dialogVisible">
        <img width="100%" :src="dialogImageUrl" alt="">
    </el-dialog>
  </div>
</template>
<script>
import { createFileRecordDelete } from '@/api/shopManage/fileRecordDelete/fileRecordDelete'
import { previewFile, uploadFile } from '@/api/platform/common/file'
import { addImgUrl, spliceImgUrl } from '@/utils/common'
import { addProductComment, updateComment, getOrderItemByOrderIds, getCommentByOrderId } from '@/api/frontStage/productComment'
export default {
    props: {
        orderIds: {
            type: Array
        },
        orderNums: {
            type: Array
        },
        orderData: {
            type: Object
        },
        createTime: {
            type: String
        },
    },
    data () {
        return {
            form: {
                commentFile: [],
                commentLevel: 5,
                commentSupply: 5,
                commentIntegrity: 5,
                commentService: 5,
                commentContent: null,
                orderNum: null,
                orderId: null,
                orderIds: [],
                orderItemComments: [],
            },
            uploadLoading: false,
            fileList2: [],
            isUpdate: false,
            dialogVisible: false,
            dialogImageUrl: null,
        }
    },
    methods: {
        handlePublish () {
            if(this.form.commentContent == null || this.form.commentContent == '') {
                this.$message.error('请填写评价内容')
                return
            }
            // 将commentFile中的url值替换为url1值
            if (this.form.commentFile && this.form.commentFile.length > 0) {
                spliceImgUrl(this.form, this.imgUrlPrefixDelete)
                this.form.commentFile.forEach(file => {
                    if (file.url1 != undefined) {
                        file.url = file.url1
                    }
                })
                // 遍历orderItemComments，将其中的commentFile的url值替换为url1值
                if (this.form.orderItemComments && this.form.orderItemComments.length > 0) {
                    this.form.orderItemComments.forEach(item => {
                        spliceImgUrl(item, this.imgUrlPrefixDelete)
                        if (item.commentFile && item.commentFile.length > 0) {
                            item.commentFile.forEach(file => {
                                if (file.url1 != undefined) {
                                    file.url = file.url1
                                }
                            })
                        }
                    })
                }
            }
            if(this.isUpdate) {
                updateComment(this.form).then(res => {
                    if(res.code == 200) {
                        this.$message.success('修改成功')
                        this.form = {
                            commentFile: [],
                            commentLevel: 5,
                            commentSupply: 5,
                            commentIntegrity: 5,
                            commentService: 5,
                            commentContent: null,
                            orderNum: null,
                            orderId: null,
                            orderItemComments: [],
                        }
                        this.form.commentId = null
                        this.isUpdate = false
                        this.$emit('comment_back', true)
                    }
                })
            }else {
                this.form.orderIds = this.form.orderId.split(',')
                addProductComment(this.form).then(res => {
                    if(res.code == 200) {
                        this.$message.success('评价成功')
                        this.form = {
                            commentFile: [],
                            commentLevel: 5,
                            commentSupply: 5,
                            commentIntegrity: 5,
                            commentService: 5,
                            commentContent: null,
                            orderNum: null,
                            orderId: null,
                            orderItemComments: [],
                        }
                        this.$emit('comment_back', true)
                    }
                })
            }
        },
        handlePictureCardPreview (file) {
            this.dialogImageUrl = file.url
            this.dialogVisible = true
        },
        // eslint-disable-next-line no-unused-vars
        formDtlFileRemove (file) {
            this.uploadLoading = true
            createFileRecordDelete({ recordId: file.fileFarId }).then(res => {
                if(res.code == 200) {
                    this.$message({
                        message: res.message,
                        type: 'success'
                    })
                }
                // 找到要删除的文件在commentFile中的索引
                const fileIndex = this.form.commentFile.findIndex(
                    item => item.fileFarId === file.fileFarId // 使用fileFarId作为唯一标识
                )
                // 使用Vue的响应式方法删除文件
                if (fileIndex !== -1) {
                    this.$delete(this.form.commentFile, fileIndex)
                }
            }).finally(() =>{
                this.uploadLoading = false
            })
        },
        formDtlFileRemove2 (file, index) {
            this.uploadLoading = true
            createFileRecordDelete({ recordId: file.fileFarId }).then(res => {
                if(res.code == 200) {
                    this.$message({
                        message: res.message,
                        type: 'success'
                    })
                }
                // 找到要删除的文件在commentFile中的索引
                const fileIndex = this.form.orderItemComments[index].commentFile.findIndex(
                    item => item.fileFarId === file.fileFarId // 使用fileFarId作为唯一标识
                )
                // 使用Vue的响应式方法删除文件
                if (fileIndex !== -1) {
                    this.$delete(this.form.orderItemComments[index].commentFile, fileIndex)
                }
            }).finally(() =>{
                this.uploadLoading = false
            })
        },
        // eslint-disable-next-line no-unused-vars
        handleDownload2 (file) {
            this.uploadLoading = true
            previewFile({ recordId: file.fileFarId }).then(res => {
                const blob = new Blob([res])
                const url = window.URL.createObjectURL(blob)
                let a = document.createElement('a')
                a.href = url
                a.download = file.name
                a.click()
                window.URL.revokeObjectURL(url)
            }).finally(() =>{
                this.uploadLoading = false
            })
        },
        uploadFileInfo (params) {
            const form = new FormData()
            form.append('files', params.raw)
            form.append('bucketName', 'mall')
            form.append('directory', 'material') // 商城类型
            form.append('isChangeObjectName', true)
            return form
        },
        minFileChange (file, fileList) {
            fileList.pop()
            this.uploadMinFile(file, fileList)
        },
        uploadMinFile (params) {
            let form = this.uploadFileInfo(params)
            uploadFile(form).then(res => {
                let file = {}
                file.url1 = res[0].objectPath.replace(this.imgUrlPrefixDelete, this.imgUrlPrefixAdd)
                let fileSuffix = res[0].objectName.substr(res[0].objectName.lastIndexOf('.'))
                file.name = '订单评价图片' + fileSuffix
                file.isMain = 0
                file.relevanceType = 8
                file.fileType = 1
                file.fileFarId =  res[0].recordId
                file.imgType = 0
                file.percentage = params.percentage
                file.size = params.size
                file.state = params.state
                file.uid = params.uid
                file.url = params.url
                file.raw = params.raw
                this.form.commentFile.push(file)
                this.$message({
                    message: '上传成功',
                    type: 'success'
                })
            }).finally(() =>{
                this.uploadLoading = false
            })
        },
        childFileChange (file, fileList, index) {
            fileList.pop()
            this.uploadChildFile(file, index)
        },
        uploadChildFile (params, index) {
            let form = this.uploadFileInfo(params)
            uploadFile(form).then(res => {
                let file = {}
                file.url1 = res[0].objectPath.replace(this.imgUrlPrefixDelete, this.imgUrlPrefixAdd)
                let fileSuffix = res[0].objectName.substr(res[0].objectName.lastIndexOf('.'))
                file.name = '商品评价图片' + fileSuffix
                file.isMain = 0
                file.relevanceType = 8
                file.fileType = 1
                file.fileFarId =  res[0].recordId
                file.imgType = 0
                file.size = params.size
                file.state = params.state
                file.uid = params.uid
                file.url = params.url
                file.raw = params.raw
                this.form.orderItemComments[index].commentFile.push(file)
                this.$message({
                    message: '上传成功',
                    type: 'success'
                })
            }).finally(() =>{
                this.uploadLoading = false
            })
        },
        handleBeforeUpload (file) {
            const sizeOk = file.size / 1024 / 1024 < this.uploadImgSize
            if(!sizeOk) {
                this.$message.error(`上传的图片大小不能超过 ${this.uploadImgSize}MB!`)
            }
            return sizeOk
        },
        rating (score) {
            console.log(score)
        },
        /** 初始化查询是否有数据 */
        initData () {
            if(this.orderData.orderId) {
                this.form.orderId = this.orderData.orderId
                this.form.orderNum = this.orderData.orderNum
                this.form.createTime = this.orderData.createTime
                this.form.commentLevel = 0
                this.form.commentSupply = 0
                this.form.commentIntegrity = 0
                this.form.commentService = 0
                getCommentByOrderId({ orderId: this.form.orderId }).then(res => {
                    if(res != null) {
                        addImgUrl(res, this.imgUrlPrefixAdd)
                        if (res.orderItemComments && res.orderItemComments.length > 0) {
                            res.orderItemComments.forEach(item => {
                                addImgUrl(item, this.imgUrlPrefixAdd)
                            })
                        }
                        this.form.commentId = res.commentId
                        this.form.commentLevel = res.commentLevel
                        this.form.commentSupply = res.commentSupply
                        this.form.commentIntegrity = res.commentIntegrity
                        this.form.commentService = res.commentService
                        this.form.commentContent = res.commentContent
                        this.form.orderItemComments = res.orderItemComments
                        this.form.commentFile = res.commentFile
                        this.isUpdate = true
                    }
                })
            } else {
                this.form.orderId = this.orderIds.join(',')
                this.form.orderNum = this.orderNums.join(',')
                this.form.commentFile = []
                if(this.orderIds.length == 1) {
                    this.form.createTime = this.createTime
                }
                getOrderItemByOrderIds( this.orderIds).then(res => {
                    if(res != null) {
                        this.form.orderItemComments = res.map(item => ({
                            commentContent: null,
                            commentFile: [],
                            orderId: item.orderId,
                            orderItemId: item.orderItemId,
                            productId: item.productId,
                            productName: item.productName,
                        }))
                    }
                })
            }
        },
    },
    created () {},
    mounted () {
        this.initData()
    }
}
</script>

<style scoped lang="scss">
/* 遮罩层 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  transition: opacity 0.3s ease;
}
/* 弹窗容器 */
.modal-container {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  width: var(--modal-width, 400px);
  max-width: 90%;
  // height: 1000px; /* 允许模态框自动适应内容高度 */
  padding: 22px 0 0 !important;
  // overflow: hidden;
  transform: scale(0.95);
  opacity: 0;
  transition: transform 0.3s ease, opacity 0.3s ease;
  width: 900px;
  max-height: 645px;
  padding: 22px 0 0 !important;
  .dialog-close{
    position: relative;
    bottom: 10px;
    right: 10px;
  }
  .dialogTitle {
    font-size: 20px;
    text-align: center;
    color: rgba(51, 51, 51, 1);
  }
  .header{
    display: inline;
  }
  .shortInfo {
    line-height: 30px;
    span:first-child {margin-right: 40px;}
    color: #333333;
    .order_sn{
      font-family: "Arial Negreta", "Arial Normal", "Arial", sans-serif;
      font-weight: 700;
      font-style: normal;
      font-size: 14px;
    }
    .notice {
      margin-left: 32px;
      color: #ffcc00;
      display: inline-block;
      img {
        width: 18px;
        height: 18px;
        margin-right: 2px;
        margin-bottom: -3px;
      }
    }
  }
  .timeC{
    color: #7F7F7F;
    display: inline-block;
    margin-left: 480px;
  }
}
.dfb {
  width: 184px;
  color: rgba(153, 153, 153, 1);
  div {
    // width: 30px;
    div:first-child {
      margin-bottom: 10px;
      text-align: center;
    }
  }
}
/* 弹窗显示动画 */
.scale-in {
  transform: scale(1);
  opacity: 1;
}
.reviewBox {
  margin-left: 135px;
  margin-bottom: 20px;
  // border: 1px solid rgba(230, 230, 230, 1);
  .picture {
    width: 370px;
    height: 100%;
    padding-top: 50px;
    // border-right: 1px solid rgba(230, 230, 230, 1);
    flex-direction: column;
    img {
      width: 160px;
      height: 160px;
      margin-bottom: 10px;
    }
    .name {
      margin-bottom: 16px;
      color: rgba(102, 102, 102, 1);
    }
    .money {
      font-size: 16px;
      color: rgba(51, 51, 51, 1);
    }
  }
  .detailReview {
    padding: 16px 0 0 30px; width: 350px;
    &>div:not(:last-child) {margin-bottom: 10px;}
    .dfa>span:first-of-type {margin-right: 5px;}
    /deep/ .el-rate{
      height: 26px;
      width: 130px;
      font-size: 30px;
      line-height: 24px;
      .el-rate__item .el-rate__icon{
        font-size: 24px;
        margin-right: 0px;
      }
    }
  }
}
.textarea {
  margin-left: 80px;
  span:first-of-type { margin-right: 22px; }
  .el-textarea { width: 560px;}
  /deep/ .el-textarea__inner {
    border-radius: 10px;
    text-align: center;
    width: 560px;
    height: 80px;
    margin-right: 0;
    padding: 10px 20px;
    line-height: 20px;
    border: none;
    background-color: #f2f2f2;
    outline: none;
    resize: none;
    position: relative;
    ::-webkit-input-placeholder {color: rgba(179, 179, 179, 1);}
    span {
      position: absolute;
      right: 10px;
      bottom: 10px;
    }
  }
  .el-input__count{
    background: none;
    right: -90px;
  }
}
.mt10{
  margin-top: 30px !important;
  font-family: "Arial Negreta", "Arial Normal", "Arial", sans-serif;
  font-weight: 700;
  font-style: normal;
  font-size: 14px;
}
.picReview{
  margin-left: 80px;
  margin-top: 20px;
  span:first-of-type { margin-right: 22px; }
}
.publish {
  button {
    width: 130px;
    height: 30px;
    margin-top: 20px;
    margin-bottom: 10px;
    margin-right: 40px;
    font-size: 16px;
    color: #fff;
    background-color: #ff0000;
    border-radius: 5px;
  }
  .el-checkbox__inner {border-radius: 0;}
  .is-checked .el-checkbox__inner {background-color: #216ec6;}
  .el-checkbox__label {color: rgba(51, 51, 51, 1);}
}
.reviewList {
  /* 原有样式保持不变 */
  justify-content: center;
  align-items: center;
  z-index: 1000;
  transition: opacity 0.3s ease;
  .titleBar {
    height: 20px;
    margin-left: 240px;
    span:nth-of-type(1) {margin-right: 380px !important;}
    span:nth-of-type(2) {margin-right: 40px !important;}
    span:not(:last-of-type) {margin-right: 80px;}
  }
  .itemImg{
    max-height: 100px;
    margin-left: 10px;
    margin-bottom: 30px;
    .productImg{
      display: inline;
      img{
        width: 100px;
        height: 100px;
        -o-object-fit: cover;
        object-fit: cover;
        display: inline-block;
      }
    }
    .itemHead{
      display: inline-block;
      width: 120px;
      margin-left: 10px;
      bottom: 35px;
      position: relative;
      span:nth-of-type(1) {
        display: block;
        margin-bottom: 15px !important;
        white-space: nowrap;       /* 强制单行显示 */
        overflow: hidden;          /* 隐藏溢出部分 */
        text-overflow: ellipsis;
        line-height: 16px;
      }
      span:nth-of-type(2) {display: block;margin-bottom: 10px !important;}
      span:nth-of-type(3) {display: block;margin-bottom: 10px !important;}
    }
    .textareaP{
      display: inline-block;
      position: relative;
      bottom: 18px;
      width: 400px;
      span:first-of-type { margin-right: 22px; }
      .el-textarea {
        width: 560px;
      }
      /deep/ .el-textarea__inner {
        border-radius: 10px;
        text-align: center;
        width: 400px;
        height: 80px;
        margin-right: 0;
        padding: 10px 20px;
        line-height: 20px;
        border: none;
        background-color: #f2f2f2;
        outline: none;
        resize: none;
        position: relative;
      }
      /deep/ .el-input__count {
        position: absolute;
        right: 170px;
        bottom: 10px;
        background: none;
      }
    }
    .picReviewP{
      float: inline-end;
      margin-right: 75px;
      width: 100px;
      span:first-of-type { margin-right: 22px; }
      /deep/ .el-upload--picture-card{
        width:100px;
        height:100px;
      }
      /deep/ .el-icon-plus{
        position: relative;
        bottom: 20px;
      }
      /deep/ .el-upload-list__item{
        width:100px;
        height:100px;
      }
    }
  }
}
/* 新增滚动容器样式 - 关键修改点 */
.review-scroll-container {
  max-height: calc(90vh - 220px); /* 计算滚动区域最大高度 */
  overflow-y: auto; /* 垂直滚动条 */
  overflow-x: hidden; /* 隐藏水平滚动条 */
  margin-top: 20px; /* 与头部内容间距 */
  padding: 0 20px; /* 内边距保持布局一致 */
}

</style>