<template>
  <main class="userCenter" v-loading="showLoading">
    <div class="title">评价晒单</div>
    <div class="content p20">
      <div class="tabs mb20 dfb">
        <div class="tab df">
          <div :class="activeTab == 0 ? 'active' : ''" @click="activeTab = 0">待评价({{ commentTotal }})</div>
          <div :class="activeTab == 1 ? 'active' : ''" @click="activeTab = 1">已评价</div>
        </div>
      </div>
      <div class="titleBar mb20 dfa">
        <el-select v-model="selectedVal" value-key="" @change="handleFilter">
          <el-option v-for="item in selectOptions"
                     :key="item.value"
                     :label="item.label"
                     :value="item.value">
          </el-option>
        </el-select>
        <span>订单详情</span>
        <span>计划编号</span>
        <span>收货人</span>
        <span>金额</span>
        <span>状态</span>
        <span>操作</span>
        <span>机构信息</span>
      </div>
      <!-- 列表 -->
      <div class="reviewList">
        <div class="item mb20" v-for="item in list" :key="item.reviewId">
          <div class="itemHead dfa">
            <span>{{ item.shopName }}</span>
          </div>
          <div class="itemContent df">
            <div class="quantity">订单号：{{ item.orderNum }}</div>
            <div class="billNo">{{ item.billNo }}</div>
            <div class="receiver">{{ item.receiver }}</div>
            <div class="price">￥{{ item.price }}</div>
            <div class="status">
              <div class="mb10">
                  <span v-if="item.status == 0">草稿</span>
                  <span v-if="item.status == 1">已提交</span>
                  <span v-if="item.status == 2">待确认</span>
                  <span v-if="item.status == 3">已确认</span>
                  <span v-if="item.status == 4">待签订合</span>
                  <span v-if="item.status == 5">已签合同</span>
                  <span v-if="item.status == 6">待发货</span>
                  <span v-if="item.status == 7">已关闭</span>
                  <span v-if="item.status == 8">发货中</span>
                  <span v-if="item.status == 9">待收货</span>
                  <span v-if="item.status == 10">已完成</span>
              </div>
              <div class="pointer"  style="color: #226fc7;" @click="handleViewDetail(item.orderNum)">订单详情</div>
            </div>
            <div class="actions">
              <div v-if="activeTab != 1" class="mb10" @click="handleReview(item)">评价</div>
              <div v-if="activeTab == 1" class="mb10" @click="deleteProductComment(item)">删除评价</div>
              <div v-if="activeTab == 1" class="mb10" @click="updateProductComment(item)">修改评论</div>
            </div>
            <div class="enterprise">{{ item.enterpriseName }}</div>
          </div>
        </div>
      </div>
      <pagination :currentPage.sync="pagination.currPage" :destination="pagination.destination" :pageSize="pagination.pageSize"
                  :total="pagination.totalNum" :totalPage="pagination.totalPage" @currentChange="currentChange" @sizeChange="sizeChange">
      </pagination>
    </div>
    <el-dialog :visible.sync="dialogVisible">
        <div class="dialog-close" style="float: right" @click="dialogVisible = false"><img src="@/assets/images/close.png"
                                                                      alt=""/></div>
      <div class="dialogTitle mb10">评价订单
      </div>
      <div class="shortInfo mb20 dfc">
        <span>创建时间：{{ form.createTime }}</span>
        <span>订单号：{{ form.orderNum }}</span>
      </div>
      <!-- 评分-内容填写 -->
      <div class="reviewBox df">
        <div class="picture dfa">
          <img @click="$router.push({ path: '/mFront/productDetail', query: { productId: form.productId } })" :src="form.pictureUrl ? imgUrlPrefixAdd + form.pictureUrl : require('@/assets/images/img/queshen5.png')" alt="">
          <div @click="$router.push({ path: '/mFront/productDetail', query: { productId: form.productId } })" class="name">{{ form.title }}</div>
          <div @click="$router.push({ path: '/mFront/productDetail', query: { productId: form.productId } })" class="money">￥{{ form.price }}</div>
        </div>
        <div class="detailReview">
          <div class="notice dfa"><img src="@/assets/images/userCenter/ico_notice.png" alt="">请至少填写一件商品的评价</div>
          <div class="dfa">
            <span>商品符合度</span>
            <el-rate v-model="form.commentLevel" @change="rating"></el-rate>
            <span>{{ form.commentLevel }}分</span>
          </div>
          <div class="textarea df">
            <span class="mt10">评价内容</span>
            <el-input type="textarea" :auto-resize="false" v-model="form.commentContent" placeholder="分享体验心得，给万千想购买的人一个参考~~" maxlength="50" show-word-limit></el-input>
          </div>
        </div>
      </div>
      <div class="publish dfc">
        <button @click="handlePublish">发表</button>
      </div>
    </el-dialog>
  </main>
</template>
<script>
import pagination from '@/pages/frontStage/components/pagination'
import { getUserOrderComment } from '@/api/frontStage/order'
import {
    addProductComment,
    deleteCommentById,
    getCommentByOrderItemId,
    updateComment
} from '@/api/frontStage/productComment'
export default {
    components: { pagination },
    data () {
        return {
            showLoading: false,
            commentTotal: 0,
            pagination: {
                currPage: 1, //当前页
                destination: null,
                pageSize: 3, // 显示数量
                totalNum: null,
                totalPage: null,
            },
            activeTab: 0,
            dialogVisible: false,
            anonymous: true,
            list: [],
            form: {
                productId: null,
                orderItemId: null,
                pictureUrl: null,
                title: null,
                price: null,
                commentLevel: 5,
                commentContent: null,
            },
            state: 10,
            selectedVal: 0,
            selectOptions: [
                { label: '近一个月订单', value: 0 },
                { label: '近三个月订单', value: 1 },
                { label: '近半年订单', value: 2 },
                { label: '全部订单', value: 3 },
            ],
            isComment: 0,
            page: {
                totalCount: 30,
                currPage: 1,
                pageSize: 3,
            },
            destination: 2,
            isUpdate: false,
        }
    },
    watch: {
        activeTab (num) {
            if(num == 0) {
                this.state = 10
                this.isComment = 0
            }
            if(num == 1) {
                this.state = 10
                this.isComment = 1
            }
            this.getUserOrderPageListM()
        },
    },
    created () {
        this.getUserOrderPageListM()
    },
    mounted () { },
    methods: {
        // 修改评论
        updateProductComment (item) {
            this.form.productId = item.productId
            this.form.orderId = item.orderId
            this.form.orderItemId = item.orderItemId
            this.form.pictureUrl = item.pictureUrl
            this.form.createTime = item.createTime
            this.form.productName = item.title
            this.form.title = item.title
            this.form.price = item.price
            this.form.orderNum = item.orderNum
            getCommentByOrderItemId({ orderItemId: item.orderItemId }).then(res => {
                if(res != null) {
                    this.form.commentId = res.commentId
                    this.form.commentLevel = res.commentLevel
                    this.form.commentContent = res.commentContent
                    this.isUpdate = true
                    this.dialogVisible = true
                }
            })
        },
        // 删除评价
        deleteProductComment (item) {
            this.clientPop('info', '您确定要删除该评价吗？', async () => {
                deleteCommentById({ orderItemId: item.orderItemId }).then(res => {
                    if(res.code == 200) {
                        this.commentTotal++
                        this.$message.success('删除成功')
                        this.getUserOrderPageListM()
                    }
                })
            })
        },
        getUserOrderPageListM () {
            let params = {
                page: this.pagination.currPage,
                limit: this.pagination.pageSize,
                state: this.state,
                productType: 13,
            }
            if(this.isComment != null) {
                params.isComment = this.isComment
            }
            if(this.keyword != null) {
                params.keywords = this.keyword
            }
            if(this.selectedVal === 0) {
                let dateObj = this.getLastMonth()
                params.startDate = dateObj.last + ' 00:00:00'
                params.endDate = dateObj.now + ' 23:59:59'
            }
            if(this.selectedVal === 1) {
                let dateObj = this.getLast3Month()
                params.startDate = dateObj.last + ' 00:00:00'
                params.endDate = dateObj.now + ' 23:59:59'
            }
            if(this.selectedVal === 2) {
                let dateObj = this.getLast6Month()
                params.startDate = dateObj.last + ' 00:00:00'
                params.endDate = dateObj.now + ' 23:59:59'
            }
            this.showLoading = true
            getUserOrderComment(params).then(res => {
                this.list = []
                res.list.forEach(t => {
                    this.list.push({
                        shopName: t.shopName,
                        orderId: t.orderId,
                        orderNum: t.orderSn,
                        title: t.untitled,
                        quantity: t.buyCounts,
                        receiver: t.receiverName,
                        price: t.actualAmount,
                        status: t.state,
                        createTime: t.gmtCreate,
                        enterpriseName: t.enterpriseName,
                        billNo: t.billNo,
                    })
                })
                if(this.isComment == 0) {
                    this.commentTotal = res.totalCount
                }
                this.pagination.currPage = res.currPage
                this.pagination.pageSize = res.pageSize
                this.pagination.totalNum =  res.totalCount
                this.pagination.totalPage =  res.totalPage
                this.showLoading = false
            }).catch(() => {
                this.showLoading = false
            })
        },
        getLastMonth () {
            let now = new Date()
            let year = now.getFullYear()
            let month = now.getMonth() + 1//0-11表示1-12月
            let day = now.getDate()
            let dateObj = {}
            dateObj.now = year + '-' + month + '-' + day
            let nowMonthDay = new Date(year, month, 0).getDate()    //当前月的总天数
            if(month - 1 <= 0) { //如果是1月，年数往前推一年<br>
                dateObj.last = (year - 1) + '-' + 12 + '-' + day
            }else{
                let lastMonthDay = new Date(year, (parseInt(month) - 1), 0).getDate()
                if(lastMonthDay < day) {    // 1个月前所在月的总天数小于现在的天日期
                    if(day < nowMonthDay) {        //当前天日期小于当前月总天数
                        dateObj.last = year + '-' + (month - 1) + '-' + (lastMonthDay - (nowMonthDay - day))
                    }else{
                        dateObj.last = year + '-' + (month - 1) + '-' + lastMonthDay
                    }
                }else{
                    dateObj.last = year + '-' + (month - 1) + '-' + day
                }
            }
            return dateObj
        },
        getLast3Month () {
            let now = new Date()
            let year = now.getFullYear()
            let month = now.getMonth() + 1//0-11表示1-12月
            let day = now.getDate()
            let dateObj = {}
            dateObj.now = year + '-' + month + '-' + day
            let nowMonthDay = new Date(year, month, 0).getDate()    //当前月的总天数
            if(month - 3 <= 0) { // 如果是1、2、3月，年数往前推一年
                var last3MonthDay1 = new Date((year - 1), (12 - (3 - parseInt(month))), 0).getDate()    // 3个月前所在月的总天数
                if(last3MonthDay1 < day) {    // 3个月前所在月的总天数小于现在的天日期
                    dateObj.last = (year - 1) + '-' + (12 - (3 - month)) + '-' + last3MonthDay1
                }else{
                    dateObj.last = (year - 1) + '-' + (12 - (3 - month)) + '-' + day
                }
            }else{
                let last3MonthDay2 = new Date(year, (parseInt(month) - 3), 0).getDate()    //3个月前所在月的总天数
                if(last3MonthDay2 < day) {    //3个月前所在月的总天数小于现在的天日期
                    if(day < nowMonthDay) {        //当前天日期小于当前月总天数,2月份比较特殊的月份
                        dateObj.last = year + '-' + (month - 3) + '-' + (last3MonthDay2 - (nowMonthDay - day))
                    }else{
                        dateObj.last = year + '-' + (month - 3) + '-' + last3MonthDay2
                    }
                }else{
                    dateObj.last = year + '-' + (month - 3) + '-' + day
                }
            }
            return dateObj
        },
        getLast6Month () {
            let now = new Date()
            let year = now.getFullYear()
            let month = now.getMonth() + 1//0-11表示1-12月
            let day = now.getDate()
            let dateObj = {}
            dateObj.now = year + '-' + month + '-' + day
            let nowMonthDay = new Date(year, month, 0).getDate()    //当前月的总天数
            if(month - 6 <= 0) { // 年数往前推一年
                let last6MonthDay1 = new Date((year - 1), (12 - (6 - parseInt(month))), 0).getDate()    // 6个月前所在月的总天数
                if(last6MonthDay1 < day) {    // 6个月前所在月的总天数小于现在的天日期
                    dateObj.last = (year - 1) + '-' + (12 - (6 - month)) + '-' + last6MonthDay1
                }else{
                    dateObj.last = (year - 1) + '-' + (12 - (6 - month)) + '-' + day
                }
            }else{
                let last6MonthDay2 = new Date(year, (parseInt(month) - 6), 0).getDate()    //6个月前所在月的总天数
                if(last6MonthDay2 < day) {    //6个月前所在月的总天数小于现在的天日期
                    if(day < nowMonthDay) {        //当前天日期小于当前月总天数,2月份比较特殊的月份
                        dateObj.last = year + '-' + (month - 6) + '-' + (last6MonthDay2 - (nowMonthDay - day))
                    }else{
                        dateObj.last = year + '-' + (month - 6) + '-' + last6MonthDay2
                    }
                }else{
                    dateObj.last = year + '-' + (month - 6) + '-' + day
                }
            }
            return dateObj
        },
        // 评价订单
        handleReview (item) {
            this.form.productId = item.productId
            this.form.orderId = item.orderId
            this.form.orderItemId = item.orderItemId
            this.form.pictureUrl = item.pictureUrl
            this.form.createTime = item.createTime
            this.form.productName = item.title
            this.form.title = item.title
            this.form.price = item.price
            this.form.orderNum = item.orderNum
            this.dialogVisible = true
        },
        // 跳转订单详情页面
        handleViewDetail (id) {
            this.$router.push({ path: '/user/synthesisMaterialDetail', query: { orderSn: id } })
        },
        currentChange (index) {
            this.pagination.currPage = index
            this.getUserOrderPageListM()
        },
        sizeChange (size) {
            this.pagination.pageSize = size
            this.getUserOrderPageListM()
        },
        checkBox (status) {
            console.log(status)
        },
        handlePublish () {
            if(this.form.commentContent == null || this.form.commentContent == '') {
                this.$message.error('请填写评价内容')
                return
            }
            if(this.isUpdate) {
                updateComment(this.form).then(res => {
                    if(res.code == 200) {
                        this.$message.success('修改成功')
                        this.form = {
                            productId: null,
                            orderItemId: null,
                            pictureUrl: null,
                            title: null,
                            price: null,
                            commentLevel: 5,
                            commentContent: null,
                        }
                        this.form.commentId = null
                        this.isUpdate = false
                        this.dialogVisible = false
                        this.getUserOrderPageListM()
                    }
                })
            }else {
                addProductComment(this.form).then(res => {
                    if(res.code == 200) {
                        this.$message.success('评价成功')
                        this.dialogVisible = false
                        this.form = {
                            productId: null,
                            orderItemId: null,
                            pictureUrl: null,
                            title: null,
                            price: null,
                            commentLevel: 5,
                            commentContent: null,
                        }
                        this.getUserOrderPageListM()
                    }
                })
            }
        },
        rating (score) {
            console.log(score)
        }
    },
}
</script>
<style scoped lang="scss">
main {
  height: 874px;
  border: 1px solid rgba(230, 230, 230, 1);
}

.titleBar {
  height: 50px;
  padding: 0 20px;
  border: 1px solid rgba(230, 230, 230, 1);
  background-color: rgba(250, 250, 250, 1);
  /deep/ .el-select {
    //margin-right: 54px;
    &, .el-input {width: 104px;}
    .el-input__inner {
      padding-left: 0;
      padding-right: 20px;
      border: 0;
      color: rgba(0, 0, 0, 1);
    }
    .el-select__caret {
      background-image: url(../../../../assets/images/userCenter/arrow_up.png);
      background-position: 50% 50%;
      background-repeat: no-repeat;
      &::before{content: '';}
    }
  }
  span:nth-of-type(1) {margin-right: 150px !important;}
  span:nth-of-type(2) {margin-right: 180px !important;}
  span:not(:last-of-type) {margin-right: 80px;}
}

.reviewList {
  height: 576px;

  .item {
    border: 1px solid rgba(230, 230, 230, 1);

    .itemHead {
      height: 40px;
      padding: 0 20px;
      border-bottom: 1px solid rgba(230, 230, 230, 1);
      color: rgba(51, 51, 51, 1);
      background-color: rgba(250, 250, 250, 1);

      span {
        margin-right: 24px;
      }
    }

    .itemContent {
      height: 140px;
      padding: 30px 20px;
      img {
        width: 80px;
        height: 80px;
        margin-right: 16px;
      }

      p {
        width: 236px;
        margin-right: 20px;
        color: rgba(51, 51, 51, 1);
      }

      .quantity {
        margin-right: 32px;
        display: inline-block;
      }
      .billNo {margin-right: 32px;}
      .receiver, .price, .enterprise, .status {width: 100px;margin-right: 11px;}
      .actions {
        width: 110px;
        div {cursor: pointer; }
        .mb10 {color: rgba(34, 111, 199, 1); }
      }
    }
  }
}
/deep/ .el-dialog {
  width: 1040px;
  height: 745px;
  .el-dialog__header {display: none;}
  .el-dialog__body {
    height: 100%;
    padding: 22px 0 0 !important;
    .el-rate {margin-right: 10px;}
    .el-rate__item {width: 20px;}
    .dialogTitle {
      font-size: 20px;
      text-align: center;
      color: rgba(51, 51, 51, 1);
    }
    .shortInfo {
      span:first-child {margin-right: 40px;}
      color: rgba(153, 153, 153, 1);
    }
    .rateBox {
      height: 140px;
      margin-bottom: 12px;
      padding: 26px 50px;
      border: 1px solid rgba(230, 230, 230, 1);
      img {
        width: 88px;
        height: 88px;
        margin-right: 20px;
        border-radius: 50%;
      }
      .shop {
        width: 320px;
        border-right: 1px solid rgba(230, 230, 230, 1);
        .shopName {
          width: 320px;
          margin: 3px 0 18px;
          font-size: 18px;
          color: rgba(51, 51, 51, 1);
        }
        .dfb {
          width: 184px;
          color: rgba(153, 153, 153, 1);
          div {
            // width: 30px;
            div:first-child {
              margin-bottom: 10px;
              text-align: center;
            }
          }
        }
      }
      .rate {
        padding: 19px 0 19px 30px;
        flex-wrap: wrap;
        .dfa {
          margin-bottom: 20px;
          &:not(:nth-of-type(2n)) {margin-right: 70px;}
          &>span:first-child {margin-right: 10px;}
        }
      }
    }
    .reviewBox {
      height: 320px;
      margin-bottom: 50px;
      border: 1px solid rgba(230, 230, 230, 1);
      .picture {
        width: 370px;
        height: 100%;
        padding-top: 50px;
        border-right: 1px solid rgba(230, 230, 230, 1);
        flex-direction: column;
        img {
          width: 160px;
          height: 160px;
          margin-bottom: 10px;
        }
        .name {
          margin-bottom: 16px;
          color: rgba(102, 102, 102, 1);
        }
        .money {
          font-size: 16px;
          color: rgba(51, 51, 51, 1);
        }
      }
      .detailReview {
        padding: 16px 0 0 30px;
        &>div:not(:last-child) {margin-bottom: 40px;}
        .notice {
          color: rgba(255, 141, 26, 1);
          img {
            width: 18px;
            height: 18px;
            margin-right: 2px;
          }
        }
        .dfa>span:first-of-type {margin-right: 30px;}
        .textarea {
          span:first-of-type { margin-right: 42px; }
          .el-textarea {width: 480px;}
          .el-textarea__inner {
            width: 480px;
            height: 140px;
            margin-right: 0;
            padding: 10px 20px;
            line-height: 20px;
            border: none;
            background-color: rgba(250, 250, 250, 1);
            outline: none;
            resize: none;
            position: relative;
            ::-webkit-input-placeholder {color: rgba(179, 179, 179, 1);}
            span {
              position: absolute;
              right: 10px;
              bottom: 10px;
            }
          }
        }
      }
    }
    .publish {
      button {
        width: 200px;
        height: 60px;
        margin-right: 40px;
        font-size: 24px;
        color: #fff;
        background-color: rgba(212, 48, 48, 1);
      }
      .el-checkbox__inner {border-radius: 0;}
      .is-checked .el-checkbox__inner {background-color: #216ec6;}
      .el-checkbox__label {color: rgba(51, 51, 51, 1);}
    }
  }
}
</style>