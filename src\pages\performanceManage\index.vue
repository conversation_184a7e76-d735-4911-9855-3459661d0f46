<template>
    <div class="page">
        <div class="full" style="padding-right: 11px;">
            <div class="menu full" v-show="showMenu">
                <div class="title">
                    <img class="logo" src="../../assets/images/procurement_performance_platform.png" alt="">
                </div>
                <el-menu
                    :default-active="defaultActive"
                    class="el-menu-vertical-demo"
                    mode="vertical"
                    background-color="#ffffff00"
                    text-color="#fff"
                    active-text-color="#FFD41C"
                    :unique-opened="true"
                    menu-trigger="click"
                    :router="true"
                    @open="handleSubOpen"
                >
                    <template v-for="item in visibleMenu">
                        <!-- 包含子1菜单 -->
                        <template v-if="item.children">
                            <authComponent :permission="item.role || ''" :key="item.menuId">
                                <el-submenu :key="item.menuId" :index="item.menuId"
                                            @click="changePath(1, item.menuName)"
                                            v-show="item.show">
                                    <template slot="title">
                                        <div class="bar"></div>
                                        <img :src="images.gear2" style="margin-right: 10px" alt=""/>
                                        <span>{{ item.menuName }}</span>
                                    </template>
                                    <div class="menu-item-box">
                                        <template v-for="subItem in item.children">
                                            <authComponent :permission="subItem.role || ''" :key="subItem.menuId">
                                                <el-menu-item @click="changePath(2, subItem.menuName)"
                                                              :key="subItem.menuId"
                                                              :index="subItem.menuId" v-show="subItem.show"
                                                              :route="subItem.route">
                                                    <template slot="title">
                                                        <img :src="images.dot" alt=""/>
                                                        <span>{{ subItem.menuName }}</span>
                                                    </template>
                                                </el-menu-item>
                                            </authComponent>
                                        </template>
                                    </div>
                                </el-submenu>
                            </authComponent>
                        </template>
                        <!-- 不包含子菜单 -->
                        <template v-else>
                            <authComponent :permission="item.role || ''" :key="item.menuId">
                                <el-menu-item :key="item.menuId" :index="item.menuId" :route="item.route"
                                              @click="changePath(0, item.menuName)" v-show="item.show">
                                    <div class="bar"></div>
                                    <img :src="images.gear2" style="margin-right: 10px" alt=""/>
                                    <span slot="title">{{ item.menuName }}</span>
                                </el-menu-item>
                            </authComponent>
                        </template>
                    </template>
                </el-menu>
            </div>
            <div id="fold-btn" @click="showMenu = !showMenu"></div>
        </div>
        <div class="table-box">
            <div class="history">
                <top-btn-bar :org-name="userInfo.orgName"></top-btn-bar>
            </div>
            <div style="height: 100%; background-color: #fff;">
                <div class="router-box">
                    <top-step :stepInfo="steps" v-show="showSteps"/>
                    <keep-alive>
                        <router-view v-if="$route.meta.keepAlive"></router-view>
                    </keep-alive>
                    <router-view v-if="!$route.meta.keepAlive"></router-view>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import $ from 'jquery'
import topStep from '../../components/topstep/topstep'
import topBtnBar from '../../components/topButtonBar'
import foldBtn from '../../assets/menu_close.png'
import openBtn from '../../assets/menu_open.png'
import { mapState, mapActions } from 'vuex'
import authComponent from '@/components/authComponent'

export default {
    components: { topStep, topBtnBar, authComponent },
    data () {
        return {
            defaultActive: '1',
            showMenu: true,
            images: {
                gearActive: require('@/assets/images/zbgl2.png'),
                gear: require('@/assets/images/zbgl.png'),
                gear2: require('@/assets/images/ershou.png'),
                gear2Active: require('@/assets/images/zbgl.png'),
                rental: require('@/assets/images/zlgl.png'),
                rentalActive: require('@/assets/images/zlgl2.png'),
                repair: require('@/assets/images/wxbyfw.png'),
                repairActive: require('@/assets/images/wxbyfw2.png'),
                order: require('@/assets/images/ddgl.png'),
                orderActive: require('@/assets/images/ddgl2.png'),
                backTitle: require('@/assets/images/logodpgl.png'),
                dot: require('@/assets/images/dot219.png'),
                arrow: require('@/assets/images/tragle829.png')
            },
            currentSteps: [
                { description: '', },
                { description: '', },
            ],
            submenuName: '',
            menu: [
                {
                    menuName: '计划管理',
                    menuId: '1',
                    show: true,
                    children: [
                        {
                            show: false,
                            menuName: '大宗月供应计划',
                            menuId: '1-1',
                            route: { path: '/performanceManage/plan/monthPlan' },
                        },
                    ],
                },
                {
                    menuName: '订单管理',
                    menuId: '2',
                    show: true,
                    children: [
                        {
                            show: false,
                            menuName: '大宗月供订单',
                            menuId: '2-1',
                            route: { path: '/performance/synthesisMaterialOrder' },
                        },
                        // {
                        //     show: true,
                        //     menuName: '大宗采购收货单',
                        //     menuId: '2-2',
                        //     route: { path: '/performance/system/receiptPerson' },
                        // },
                    ],
                },
                {
                    menuName: '对账',
                    menuId: '3',
                    show: true,
                    children: [
                        { show: true, menuName: '对账单', menuId: '3-1', route: { path: '/performance/sheet/sheet' } },
                        // { show: true, menuName: '大宗临购对账单', menuId: '3-2', route: { path: '/performance/sheet/synthesizeTemporary' } },
                    ]
                },
                {
                    menuName: '发票管理',
                    menuId: '9',
                    show: true && this.showDevFunc,
                    children: [
                        { show: true && this.showDevFunc, menuName: '发票管理', menuId: '9-1', route: { path: '/performance/invoice/record' } },
                        {
                            show: true && this.showDevFunc,
                            menuName: '发票抬头',
                            menuId: '9-2',
                            route: { path: '/performance/invoice/invoiceRise' },
                        },
                    ]
                },
                {
                    menuName: '系统管理',
                    menuId: '7',
                    show: true,
                    children: [
                        {
                            show: true,
                            menuName: '收料员管理',
                            menuId: '7-1',
                            route: { path: '/performance/system/receiptPerson' },
                        },

                    ],
                },
                // {
                //     menuName: '个人信息管理',
                //     menuId: '8',
                //     show: true && this.showDevFunc,
                //     children: [
                //
                //     ],
                // },

            ],
        }
    },
    watch: {
        showMenu: {
            handler (newVal) {
                let btnImg = newVal ? `url(${foldBtn})` : `url(${openBtn})`
                $('#fold-btn').css('background-image', btnImg)
            }
        },
        $route: {
            handler () {
                this.setDefaultActive()
            },
            deep: true,
            immediate: true
        },
    },
    computed: {
        ...mapState(['userInfo', 'steps']),
        showSteps () {
            return !this.$route.path.includes('etail')
        },
        // 最终渲染的菜单
        visibleMenu () {
            let visibleMenu = this.menu.filter(item => (item.show && this.hasPermission(item.role)))
            visibleMenu.forEach(item => {
                if(item?.children.length > 0) {
                    item.children = item.children.filter(child => (child.show && this.hasPermission(child.role)))
                }
            })
            return visibleMenu
        },
    },
    methods: {
        ...mapActions(['changeSteps']),
        // 设置默认高亮的菜单项
        setDefaultActive () {
            let path = this.$route.path
            this.visibleMenu.forEach(item => {
                if(item.route && item.route.path === path) {
                    this.currentSteps = [{ description: item.menuName }]
                    this.defaultActive = item.menuId
                }
                if(!item.children) return
                item.children.forEach(subItem => {
                    if(subItem.route.path !== path) return
                    this.defaultActive = subItem.menuId
                    this.submenuName = item.menuName
                    this.currentSteps[0].description = item.menuName
                    this.currentSteps[1].description = subItem.menuName
                })
            })
        },
        // 传入权限字段，判断用户是否有此权限
        hasPermission (permission) {
            if(!permission || permission.length === 0) return true
            let { roles } = this.userInfo
            let hasPermission = true
            permission.forEach(item => {
                if(!hasPermission) return
                hasPermission = (roles && roles.includes(item)) || this.userInfo[item] === 1
            })
            return hasPermission
        },
        // 展开子菜单
        handleSubOpen (key) {
            this.menu.forEach(item => item.menuId === key ? this.submenuName = item.menuName : null)
        },
        // 修改路径显示
        changePath (num, name) {
            if (this.currentSteps.length === 1) {
                num === 0 ? this.currentSteps[0].description = name : this.currentSteps.push({ description: name })
            } else {
                if (num === 0) {
                    this.currentSteps.pop()
                    this.currentSteps[0].description = name
                } else {
                    this.currentSteps[0].description = this.submenuName
                    this.currentSteps[1].description = name
                }
            }
            this.changeSteps(this.currentSteps)
        },
    },
    async created () {
    },
}
</script>
<style scoped lang="scss">
@import '../../assets/css/backStage.css';

/deep/ .el-dialog__header {
    background: url(../../assets/test.png);
}

.page {
    display: flex;
    font-family: 'SourceHanSansCN-Regular';
    height: 100%;
}

.table-box {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    overflow: scroll;
    box-sizing: border-box;
    background-color: #eff2f6;

    &::-webkit-scrollbar {
        display: none;
    }

    /*.history {
        font-weight: bold;
        font-size: 17px;

        & > div {
            line-height: 84px;

            span {
                color: gray;
            }
        }
    }*/

    .router-box {
        height: 100%;
        display: flex;
        flex-direction: column;
        flex-grow: 1;

        & > *:last-child {
            flex-grow: 1;
        }
    }
}
</style>
