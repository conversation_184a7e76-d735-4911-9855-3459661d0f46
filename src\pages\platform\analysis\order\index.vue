<template>
    <div>
        <div class="right" v-loading="showLoading">
          <div style="display: flex; align-items: center; justify-content: space-between;">
            <el-tabs v-model="activeName" @tab-click="handleClick" style="padding-left: 20px">
              <el-tab-pane label="全部" name="all"></el-tab-pane>
              <el-tab-pane label="零星采购" name="first"></el-tab-pane>
              <el-tab-pane label="大宗临购" name="second"></el-tab-pane>
              <el-tab-pane label="周转材料" name="third"></el-tab-pane>
            </el-tabs>

            <div style="display: flex; gap: 10px;">
              <el-select v-model="orderCountOrProfitPriceTotal" placeholder="请选择" style="width: 90px" @change="handleClick">
                <el-option
                    v-for="item in orderCountOrProfitPriceTotalOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                </el-option>
              </el-select>
              <el-select v-model="date" placeholder="请选择" style="margin-right: 20px;width: 90px" @change="handleClick">
                <el-option
                    v-for="item in dateOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                </el-option>
              </el-select>
            </div>
          </div>
<!--            <el-date-picker-->
<!--                :default-time="['00:00:00', '23:59:59']"-->
<!--                @change="dateChange"-->
<!--                value-format="yyyy-MM-dd HH:mm:ss"-->
<!--                v-model="filterData.dateScope"-->
<!--                type="datetimerange"-->
<!--                range-separator="至"-->
<!--                :picker-options="pickerOptions"-->
<!--                start-placeholder="开始日期"-->
<!--                end-placeholder="结束日期">-->
<!--            </el-date-picker>-->
            <div style="width:100%;height:300px;" ref="chart"></div>
            <!--            表格-->
            <div class="e-table" :style="{ width: '100%' }">
                <div class="top">
                    <div class="left">
                        <div class="left-btn dfa">
                            <el-button style="margin-left: 10px" type="primary" @click="outputAll">导出</el-button>
                            <el-button  v-if="buttonMode === 'order'"  style="margin-left: 10px" type="success"  @click="toggleButton('material')">订单号</el-button>
                            <el-button v-else   style="margin-left: 10px" type="success" @click="toggleButton('order')">物资名称维度</el-button>
                            <div style="height: 50px; line-height: 50px;margin-left: 10px">
                                <el-date-picker
                                    :default-time="['00:00:00', '23:59:59']"
                                    @change="dateChange"
                                    value-format="yyyy-MM-dd HH:mm:ss"
                                    v-model="filterData.dateScope"
                                    type="datetimerange"
                                    range-separator="至"
                                    :picker-options="pickerOptions"
                                    start-placeholder="开始日期"
                                    end-placeholder="结束日期">
                                </el-date-picker>
                            </div>
                        </div>
                    </div>
                  <div class="search_box">
                    <el-input clearable style="width: 300px;margin-right: 10px" @blur="handleInputSearch" placeholder="输入搜索关键字" v-model="keywords">
                      <img src="@/assets/search.png" slot="suffix" @click="handleInputSearch" alt=""/>
                    </el-input>
                  </div>
                </div>
                <el-table class="table" v-loading="tableLoading" :data="tableData" border v-if="buttonMode === 'order'">
                    <el-table-column label="序号" type="index" width="60"/>
                    <el-table-column label="订单号" width="240" prop="orderSn"/>
                    <el-table-column label="订单类型" width="" prop="productType">
                      <template slot-scope="scope">
                        <el-tag v-if="scope.row.productType == 0">零星采购订单</el-tag>
                        <el-tag v-else-if="scope.row.productType == 1">大宗临购订单</el-tag>
                        <el-tag v-else-if="scope.row.productType == 2">周转材料订单</el-tag>
                        <el-tag v-else>未知</el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column label="供应商名称" width="100" prop="supplierName"/>
                    <el-table-column label="物资名称" width="100" prop="untitled"/>
<!--                    <el-table-column label="店铺名称" width="100" prop="shopName"/>
                    <el-table-column label="商品名称" prop="untitled"/>-->
                    <el-table-column label="不含税成本总价" prop="costPriceTotal" >
                      <template slot-scope="scope">
                        {{ calculateTax(scope.row.costPriceTotal,scope.row.taxRate)}}
                      </template>
                    </el-table-column>
                    <el-table-column label="含税成本总价" prop="costPriceTotal" />
                    <el-table-column label="不含税销售总价" prop="actualAmount" >
                      <template slot-scope="scope">
                      {{ calculateTax(scope.row.actualAmount,scope.row.taxRate)}}
                      </template>
                    </el-table-column>
                    <el-table-column label="含税销售总价" prop="actualAmount" />
                    <el-table-column label="不含税总利润" prop="profitPriceTotal" >
                      <template slot-scope="scope">
                      {{ calculateTax(scope.row.profitPriceTotal,scope.row.taxRate)}}
                      </template>
                    </el-table-column>
                    <el-table-column label="含税总利润" prop="profitPriceTotal" />
                    <el-table-column label="采购单位" prop="enterpriseName" />
                    <el-table-column label="完成时间" width="160" prop="successDate" />
                    <el-table-column label="创建时间" width="160" prop="gmtCreate" />
                </el-table>
              <el-table class="table" v-loading="tableLoading" :data="tableData" border v-if="buttonMode === 'material'">
                <el-table-column label="序号" type="index" width="60"/>
                <el-table-column label="物资名称" width="100" prop="relevanceName"/>
                <el-table-column label="商品名称" width="100" prop="productName">
                  <template slot-scope="scope">
                    {{scope.row.productName}}
                  </template>
                </el-table-column>
                <el-table-column label="计量单位" width="100" prop="unit"/>
                <el-table-column label="购买数量" width="100" prop="buyCounts"/>
<!--                <el-table-column label="成本价" width="" prop="costPrice" />-->
                <el-table-column label="不含税销售单价" width="" prop="productPrice" >
                  <template slot-scope="scope">
                    {{ calculateTax(scope.row.productPrice,scope.row.taxRate)}}
                  </template>
                </el-table-column>
                <el-table-column label="含税销售单价" width="" prop="productPrice" />
                <el-table-column label="不含税销售总价" width="" prop="totalAmount" >
                  <template slot-scope="scope">
                    {{ calculateTax(scope.row.totalAmount,scope.row.taxRate)}}
                  </template>
                </el-table-column>
                <el-table-column label="含税销售总价" width="" prop="totalAmount" />
                <el-table-column label="不含税成本总价" width="" prop="costAmount" >
                  <template slot-scope="scope">
                    {{ calculateTax(scope.row.costAmount,scope.row.taxRate)}}
                  </template>
                </el-table-column>
                <el-table-column label="含税成本总价" width="" prop="costAmount" />
                <el-table-column label="不含税总利润" width="" prop="profitPrice" >
                  <template slot-scope="scope">
                    {{ calculateTax(scope.row.profitPrice,scope.row.taxRate)}}
                  </template>
                </el-table-column>
                <el-table-column label="含税总利润" width="" prop="profitPrice" />
                <el-table-column label="订单类型" width="" prop="productType">
                  <template slot-scope="scope">
                    <el-tag v-if="scope.row.productType == 0">零星采购订单</el-tag>
                    <el-tag v-else-if="scope.row.productType == 1">大宗临购订单</el-tag>
                    <el-tag v-else-if="scope.row.productType == 2">周转材料订单</el-tag>
                    <el-tag v-else>未知</el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="订单号" width="240" prop="orderSn">
                  <template slot-scope="scope">
                    {{scope.row.orderSn}}
                  </template>
                </el-table-column>
<!--                <el-table-column label="二级订单号" width="240" />-->
                <el-table-column label="供应商名称" width="100" prop="supplierName"/>
                <el-table-column label="客户" prop="enterpriseName" />
                <el-table-column label="创建时间" width="160" prop="gmtCreate" />
                <el-table-column label="完成时间" width="160" prop="flishTime" />
              </el-table>
            </div>
            <!--分页-->
            <Pagination
                v-if="buttonMode === 'order'"
                v-show="tableData && tableData.length > 0"
                :total="paginationInfo.total"
                :pageSize.sync="paginationInfo.pageSize"
                :currentPage.sync="paginationInfo.currentPage"
                @currentChange="getPlatformOrdersCountM"
                @sizeChange="getPlatformOrdersCountM"
            />
            <Pagination
                v-else
                v-show="tableData && tableData.length > 0"
                :total="paginationInfo.total"
                :pageSize.sync="paginationInfo.pageSize"
                :currentPage.sync="paginationInfo.currentPage"
                @currentChange="getAssetNameDimensionListM"
                @sizeChange="getAssetNameDimensionListM"
            />
        </div>

    </div>
</template>
<script>
//局部引用
import { getPlatformOrdersCount, getAssetNameDimensionList } from '@/api/platform/order/orders'
import Pagination from '@/components/pagination/pagination'

const echarts = require('echarts')
export default{
    components: {
        Pagination
    },
    data () {
        return {
            buttonMode: 'order', // 初始显示订单号按钮
            date: 'week',
            dateOptions: [
                {
                    value: 'week',
                    label: '近一周'
                },
                {
                    value: 'month',
                    label: '近一月'
                },
                {
                    value: 'year',
                    label: '近一年'
                }
            ],
            orderCountOrProfitPriceTotal: 'orderCount',
            orderCountOrProfitPriceTotalOptions: [
                {
                    value: 'orderCount',
                    label: '订单数'
                },
                {
                    value: 'profitPriceTotal',
                    label: '总利润'
                }
            ],
            keywords: null,
            activeName: 'all',
            showLoading: false,
            pickerOptions: {
                shortcuts: [{
                    text: '最近一个月',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
                        start.setHours('00', '00', '00')
                        end.setHours('23', '59', '59')
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '最近三个月',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
                        start.setHours('00', '00', '00')
                        end.setHours('23', '59', '59')
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '最近半年',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 180)
                        start.setHours('00', '00', '00')
                        end.setHours('23', '59', '59')
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '最近一年',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 360)
                        start.setHours('00', '00', '00')
                        end.setHours('23', '59', '59')
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '最近二年',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 360 * 2)
                        start.setHours('00', '00', '00')
                        end.setHours('23', '59', '59')
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '全部',
                    onClick (picker) {
                        picker.$emit('pick', [])
                    }
                }]
            },
            tableData: [],
            paginationInfo: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            tableLoading: false,
            filterData: {
                dateScope: []
            },
            dataVOS: [],
            labelTitle: [], // 名称数组
            count: [], // 数量数组
            profitPriceTotals: [], // 总利润数数组
        }
    },
    methods: {
        // 切换维度
        toggleButton (mode) {
            this.buttonMode = mode
            this.paginationInfo.currentPage = 1
            this.paginationInfo.pageSize = 20
            this.buttonMode === 'order' ? this.getPlatformOrdersCountM() : this.getAssetNameDimensionListM()

        },
        // 切换页签
        handleClick (tab, event) {
            console.log(tab, event)
            console.log(this.activeName)
            this.buttonMode === 'order' ? this.getPlatformOrdersCountM() : this.getAssetNameDimensionListM()
        },
        calculateTax (amount, taxRate) {
            return amount * (1 - taxRate / 100).toFixed(2)
        },
        resetSearchConditions () {
            this.filterData.dateValue = []
        },
        handleInputSearch () {
            this.resetSearchConditions()
            this.buttonMode === 'order' ? this.getPlatformOrdersCountM() : this.getAssetNameDimensionListM()
        },
        getAssetNameDimensionListM () {
            let params = {
                page: this.paginationInfo.currentPage,
                limit: this.paginationInfo.pageSize,
                productType: this.activeName === 'first' ? 0 : this.activeName === 'second' ? 1 : this.activeName === 'third' ? 2 : null
            }
            if(this.filterData.dateScope != null) {
                params.startDate = this.filterData.dateScope[0],
                params.endDate = this.filterData.dateScope[1]
            }
            if (this.keywords != null && this.keywords != '') {
                params.keywords = this.keywords
            }
            params.date = this.date
            params.orderCountOrProfitPriceTotal = this.orderCountOrProfitPriceTotal
            this.showLoading = true
            getAssetNameDimensionList(params).then(res => {
                this.paginationInfo.total = res.totalCount
                this.paginationInfo.pageSize = res.pageSize
                this.paginationInfo.currentPage = res.currPage
                this.tableData = res.list
                if(res.list.length != 0) {
                    this.labelTitle = res.list[0].labelTitle
                    this.count = res.list[0].count
                    this.profitPriceTotals = res.list[0].profitPriceTotals
                }else {
                    this.count = []
                    this.profitPriceTotals = []
                }
                this.initCharts()
                this.showLoading = false
            }).catch(() => {
                this.showLoading = false
            })
        },
        getPlatformOrdersCountM () {
            let params = {
                page: this.paginationInfo.currentPage,
                limit: this.paginationInfo.pageSize,
                productType: this.activeName === 'first' ? 0 : this.activeName === 'second' ? 1 : this.activeName === 'third' ? 2 : null
            }
            if(this.filterData.dateScope != null) {
                params.startDate = this.filterData.dateScope[0],
                params.endDate = this.filterData.dateScope[1]
            }
            if (this.keywords != null && this.keywords != '') {
                params.keywords = this.keywords
            }
            params.date = this.date
            params.orderCountOrProfitPriceTotal = this.orderCountOrProfitPriceTotal
            this.showLoading = true
            getPlatformOrdersCount(params).then(res => {
                this.paginationInfo.total = res.totalCount
                this.paginationInfo.pageSize = res.pageSize
                this.paginationInfo.currentPage = res.currPage
                this.tableData = res.list
                if(res.list.length != 0) {
                    this.labelTitle = res.list[0].labelTitle
                    this.count = res.list[0].count
                    this.profitPriceTotals = res.list[0].profitPriceTotals
                }else {
                    this.count = []
                    this.profitPriceTotals = []
                }
                this.initCharts()
                this.showLoading = false
            }).catch(() => {
                this.showLoading = false
            })
        },
        dateChange () {
            this.buttonMode === 'order' ? this.getPlatformOrdersCountM() : this.getAssetNameDimensionListM()
        },
        initCharts () {
            // 基于准备好的dom，初始化echarts实例
            let myChart = echarts.init(this.$refs.chart)
            // 绘制图表
            myChart.setOption({
                itemStyle: {
                    // 高亮时点的颜色
                    color: '#74A0F9'
                },
                tooltip: {},
                xAxis: {
                    data: this.labelTitle
                },
                yAxis: {
                    type: 'value',
                    minInterval: 1,
                    axisLabel: {
                        formatter: '{value}'
                    }
                },
                series: [{
                    name: this.orderCountOrProfitPriceTotal === 'orderCount' ? '数量' : '总利润数',
                    type: 'bar',
                    data: this.orderCountOrProfitPriceTotal === 'orderCount' ? this.count : this.profitPriceTotals
                }]
            })
        },
        // 日期处理
        padZero (num) {
            return num < 10 ? `0${num}` : num
        },
        dateStrM (date) {
            const year = date.getFullYear()
            const month = this.padZero(date.getMonth() + 1)
            const day = this.padZero(date.getDate())
            const hour = this.padZero(date.getHours())
            const minute = this.padZero(date.getMinutes())
            const second = this.padZero(date.getSeconds())
            return `${year}-${month}-${day} ${hour}:${minute}:${second}`
        },
        outputAll () {

        }
    },
    //一加载页面就调用
    mounted () {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
        start.setHours('00', '00', '00')
        this.filterData.dateScope =  [this.dateStrM(start), this.dateStrM(end)]
        this.getPlatformOrdersCountM()
    }
}
</script>
<style scoped lang="scss">
</style>