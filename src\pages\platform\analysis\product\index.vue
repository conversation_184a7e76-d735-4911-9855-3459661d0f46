<template>
    <div>
        <div class="right" v-loading="showLoading">
          <el-tabs v-model="activeName" @tab-click="handleClick" style="padding-left: 20px">
            <el-tab-pane label="全部" name="all"></el-tab-pane>
            <el-tab-pane label="零星采购" name="first"></el-tab-pane>
            <el-tab-pane label="大宗临购" name="second"></el-tab-pane>
            <el-tab-pane label="周转材料" name="third"></el-tab-pane>
          </el-tabs>
<!--            <el-date-picker-->
<!--                :default-time="['00:00:00', '23:59:59']"-->
<!--                @change="dateChange"-->
<!--                value-format="yyyy-MM-dd HH:mm:ss"-->
<!--                v-model="filterData.dateScope"-->
<!--                type="datetimerange"-->
<!--                range-separator="至"-->
<!--                :picker-options="pickerOptions"-->
<!--                start-placeholder="开始日期"-->
<!--                end-placeholder="结束日期">-->
<!--            </el-date-picker>-->
            <div style="width:100%;height:300px;" ref="chart"></div>
            <!--            表格-->
            <div class="e-table" :style="{ width: '100%' }">
                <div class="top">
                    <div class="left">
                        <div class="left-btn dfa">
                            <el-button style="margin-left: 10px" type="primary" @click="outputAll">导出</el-button>
                            <div style="height: 50px; line-height: 50px;margin-left: 10px">
                                <el-date-picker
                                    :default-time="['00:00:00', '23:59:59']"
                                    @change="dateChange"
                                    value-format="yyyy-MM-dd HH:mm:ss"
                                    v-model="filterData.dateScope"
                                    type="datetimerange"
                                    range-separator="至"
                                    :picker-options="pickerOptions"
                                    start-placeholder="开始日期"
                                    end-placeholder="结束日期">
                                </el-date-picker>
                            </div>
                        </div>
                    </div>
                    <div class="search_box">
                      <el-input clearable style="width: 300px;margin-right: 10px" @blur="handleInputSearch" placeholder="输入搜索关键字" v-model="keywords">
                        <img src="@/assets/search.png" slot="suffix" @click="handleInputSearch" alt=""/>
                      </el-input>
                    </div>
                </div>
                <el-table class="table" v-loading="tableLoading" :data="tableData"  border
                >
                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                    <el-table-column label="商品编码" width="240" prop="serialNum">
                    </el-table-column>
<!--                    <el-table-column label="店铺名称" width="" prop="shopName"></el-table-column>-->
                    <el-table-column label="商品类型" width="" prop="productType">
                      <template slot-scope="scope">
                        <el-tag v-if="scope.row.productType == 0">零星采购</el-tag>
                        <el-tag v-else-if="scope.row.productType == 1">大宗临购</el-tag>
                        <el-tag v-else-if="scope.row.productType == 2">周转材料</el-tag>
                        <el-tag v-else>未知</el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column label="物资名称" width="" prop="relevanceName"></el-table-column>
                    <el-table-column label="商品名称" width="" prop="productName"></el-table-column>
                    <el-table-column label="供应商名称" width="" prop="supplierName"></el-table-column>
                    <el-table-column label="不含税成本单价" width="" prop="costPrice" >
                      <template slot-scope="scope">
                       {{ calculateTax(scope.row.costPrice,scope.row. taxRate)}}
                      </template>
                    </el-table-column>
                    <el-table-column label="不含税销售单价" width="" prop="productMinPrice" >
                      <template slot-scope="scope">
                        {{ calculateTax(scope.row.productMinPrice,scope.row. taxRate)}}
                      </template>
                    </el-table-column>
                    <el-table-column label="不含税原单价" width="" prop="originalPrice" >
                      <template slot-scope="scope">
                        {{ calculateTax(scope.row.originalPrice,scope.row. taxRate)}}
                      </template>
                    </el-table-column>
                    <el-table-column label="含税成本单价" width="" prop="costPrice" />
                    <el-table-column label="含税销售单价" width="" prop="productMinPrice" />
                    <el-table-column label="含税原单价" width="" prop="originalPrice" />
<!--                    <el-table-column label="差价" width="" prop="profitPrice" />-->
                    <el-table-column label="上架时间" width="160" prop="putawayDate" />
                    <el-table-column label="创建时间" width="160" prop="gmtCreate" />
                </el-table>
            </div>
            <!--            分页-->
            <Pagination
                v-show="tableData != null || tableData.length != 0"
                :total="paginationInfo.total"
                :pageSize.sync="paginationInfo.pageSize"
                :currentPage.sync="paginationInfo.currentPage"
                @currentChange="getProductCountListM"
                @sizeChange="getProductCountListM"
            />
        </div>

    </div>
</template>
<script>
//局部引用
import Pagination from '@/components/pagination/pagination'
import { getProductCountList } from '@/api/platform/product/productCount'

const echarts = require('echarts')
export default{
    components: {
        Pagination
    },
    data () {
        return {
            activeName: 'all',
            keywords: null,
            showLoading: false,
            pickerOptions: {
                shortcuts: [{
                    text: '最近一个月',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
                        start.setHours('00', '00', '00')
                        end.setHours('23', '59', '59')
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '最近三个月',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
                        start.setHours('00', '00', '00')
                        end.setHours('23', '59', '59')
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '最近半年',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 180)
                        start.setHours('00', '00', '00')
                        end.setHours('23', '59', '59')
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '最近一年',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 360)
                        start.setHours('00', '00', '00')
                        end.setHours('23', '59', '59')
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '最近二年',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 360 * 2)
                        start.setHours('00', '00', '00')
                        end.setHours('23', '59', '59')
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '全部',
                    onClick (picker) {
                        picker.$emit('pick', [])
                    }
                }]
            },
            tableData: [],
            paginationInfo: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            tableLoading: false,
            filterData: {
                dateScope: []
            },
            dataVOS: [],
            labelTitle: [], // 名称数组
            count: [], // 数量数组
        }
    },
    methods: {
        // 切换页签
        handleClick (tab, event) {
            console.log(tab, event)
            console.log(this.activeName)
            this.getProductCountListM()
        },
        calculateTax (amount, taxRate) {
            return amount * (1 - taxRate / 100).toFixed(2)
        },
        resetSearchConditions () {
            this.filterData.dateValue = []
        },
        handleInputSearch () {
            this.resetSearchConditions()
            this.getProductCountListM()
        },
        getProductCountListM () {
            let params = {
                page: this.paginationInfo.currentPage,
                limit: this.paginationInfo.pageSize,
                productType: this.activeName === 'first' ? 0 : this.activeName === 'second' ? 1 : this.activeName === 'third' ? 2 : null
            }
            if(this.filterData.dateScope != null) {
                params.startCreateDate = this.filterData.dateScope[0],
                params.endCreateDate = this.filterData.dateScope[1]
            }
            if (this.keywords != null && this.keywords != '') {
                params.keywords = this.keywords
            }
            this.showLoading = true
            getProductCountList(params).then(res => {
                this.paginationInfo.total = res.totalCount
                this.paginationInfo.pageSize = res.pageSize
                this.paginationInfo.currentPage = res.currPage
                this.tableData = res.list
                if(res.list.length != 0) {
                    this.labelTitle = res.list[0].labelTitle
                    this.count = res.list[0].count
                }else {
                    this.count = []
                }
                this.initCharts()
                this.showLoading = false
            }).catch(() => {
                this.showLoading = false
            })
        },
        dateChange () {
            this.getProductCountListM()
        },
        initCharts () {
            // 基于准备好的dom，初始化echarts实例
            let myChart = echarts.init(this.$refs.chart)
            // 绘制图表
            myChart.setOption({
                itemStyle: {
                    // 高亮时点的颜色
                    color: '#74A0F9'
                },
                tooltip: {},
                xAxis: {
                    data: this.labelTitle
                },
                yAxis: {},
                series: [{
                    name: '数量',
                    type: 'bar',
                    data: this.count
                }]
            })
        },
        // 日期处理
        padZero (num) {
            return num < 10 ? `0${num}` : num
        },
        dateStrM (date) {
            const year = date.getFullYear()
            const month = this.padZero(date.getMonth() + 1)
            const day = this.padZero(date.getDate())
            const hour = this.padZero(date.getHours())
            const minute = this.padZero(date.getMinutes())
            const second = this.padZero(date.getSeconds())
            return `${year}-${month}-${day} ${hour}:${minute}:${second}`
        },
        // 全部导出
        outputAll () {
            /* if (this.tableData.length == 0) {
          return this.$message.error('数据为空！')
        }
        if (this.dataListSelections.length != 0) {
          let ids = this.dataListSelections.map(item => {
            return item.dtlId
          })
          this.currentQuery.ids = ids
          this.tableLoading = true
          platformOutputShipExcel(this.currentQuery).then(res => {
            const blob = new Blob([res], { type: 'application/vnd.ms-excel' }) // 2.获取请求返回的response对象中的blob 设置文件类型，这里以excel为例
            const url = window.URL.createObjectURL(blob) // 3.创建一个临时的url指向blob对象
            const a = document.createElement('a') // 4.创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
            a.href = url
            a.download = '平台交易量信息.xlsx'
            a.click()
            window.URL.revokeObjectURL(url) // 5.释放这个临时的对象url
            this.currentQuery.ids = []
            this.dataListSelections = []
            this.$message.success('操作成功')
            this.tableLoading = false
          }).catch(() => {
            this.tableLoading = false
          })
        } else {
          this.tableLoading = true
          platformOutputShipExcel(this.currentQuery).then(res => {
            const blob = new Blob([res], { type: 'application/vnd.ms-excel' }) // 2.获取请求返回的response对象中的blob 设置文件类型，这里以excel为例
            const url = window.URL.createObjectURL(blob) // 3.创建一个临时的url指向blob对象
            const a = document.createElement('a') // 4.创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
            a.href = url
            a.download = '平台交易量信息.xlsx'
            a.click()
            window.URL.revokeObjectURL(url) // 5.释放这个临时的对象url
            this.$message.success('操作成功')
            this.tableLoading = false
          }).catch(() => {
            this.tableLoading = false
          })
        }*/
        },
    },
    //一加载页面就调用
    mounted () {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
        start.setHours('00', '00', '00')
        this.filterData.dateScope =  [this.dateStrM(start), this.dateStrM(end)]
        this.getProductCountListM()
    }
}
</script>
<style scoped lang="scss">
</style>