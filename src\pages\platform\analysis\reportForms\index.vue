<template>
    <div>
        <div class="right" v-loading="showLoading">
            <div class="e-table" :style="{ width: '100%' }">
              <el-tabs v-model="activeName" @tab-click="handleClick">
                <el-tab-pane label="全部" name="all"></el-tab-pane>
                <el-tab-pane label="零星采购" name="first"></el-tab-pane>
                <el-tab-pane label="大宗临购" name="second"></el-tab-pane>
                <el-tab-pane label="周转材料" name="third"></el-tab-pane>
              </el-tabs>
                <div class="top">
                    <div class="left">
                        <div class="left-btn" style=" display: flex; justify-content: space-between">
                            <el-button style="margin-top:10px" type="primary" @click="outputAll" class="">数据导出</el-button>

                            <div style="height: 50px; line-height: 50px;margin-left: 10px">
                                <el-date-picker
                                    size="small"
                                    :default-time="['00:00:00', '23:59:59']"
                                    @change="getProductFromListM"
                                    value-format="yyyy-MM-dd HH:mm:ss"
                                    v-model="filterData.dateValue"
                                    type="datetimerange"
                                    range-separator="至"
                                    :picker-options="pickerOptions"

                                    start-placeholder="开始日期"
                                    end-placeholder="结束日期"
                                    style="margin-right: 10px; width: 400px ;"
                                >
                                </el-date-picker>
                            </div>

                        </div>
                    </div>
                    <div class="search_box">
                        <el-input clearable style="width: 300px" @blur="handleInputSearch" placeholder="输入搜索关键字" v-model="keywords">
                            <img src="@/assets/search.png" slot="suffix" @click="handleInputSearch" alt=""/>
                        </el-input>
                        <div class="adverse">
                            <el-button type="primary" size="small" @click="queryVisible = true">高级搜索</el-button>
                        </div>
                    </div>
                </div>
                <el-table
                    class="table"
                    v-loading="tableLoading"
                    ref="eltableCurrentRow2"
                    @row-click="handleCurrentInventoryClick2"
                    @selection-change="selectionChangeHandle"
                    :data="tableData"
                    :height="rightTableHeight"
                    border
                >
                    <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                    <el-table-column label="店铺名称" width="240" prop="shopName"></el-table-column>
                    <el-table-column label="供应商全称" width="300" prop="enterpriseName"></el-table-column>
                    <el-table-column label="商品编码" width="140" prop="serialNum"></el-table-column>
                    <el-table-column label="商品名称" width="320" prop="productName"></el-table-column>
                    <el-table-column label="销售价格" width="" prop="sellPrice"/>
                    <el-table-column label="上架时间" width="240" prop="putawayDate"/>
                </el-table>
            </div>
            <!--            分页-->
            <Pagination
                :total="paginationInfo.total"
                :pageSize.sync="paginationInfo.pageSize"
                :currentPage.sync="paginationInfo.currentPage"
                @currentChange="getProductFromListM"
                @sizeChange="getProductFromListM"
            />
        </div>
        <el-dialog title="高级查询" :visible.sync="queryVisible" width="50%">
            <el-form :model="filterData" ref="form" label-width="120px" :inline="false">
                <el-row>
                    <el-col :span="12">
                        <el-form-item width="150px" label="商店名称：" prop="shopName">
                            <el-input v-model="filterData.shopName" placeholder="请输入商店名称" clearable></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item width="150px" label="供应商名称：" prop="shopName">
                            <el-input v-model="filterData.enterpriseName" placeholder="请输入供应商名称" clearable></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item width="150px" label="商品名称：" prop="shopName">
                            <el-input v-model="filterData.productName" placeholder="请输入商品名称" clearable></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item width="150px" label="商品编码：" prop="shopName">
                            <el-input v-model="filterData.serialNum" placeholder="请输入商品编码" clearable></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item width="150px" label="销售价格以上：">
                            <el-input type="number" v-model="filterData.staSellPrice" placeholder="请输入价格区间" style="width: 200px"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="销售价格以下：">
                            <el-input type="number" v-model="filterData.endSellPrice" placeholder="请输入价格区间" style="width: 200px"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="创建时间：">
                            <el-date-picker
                                value-format="yyyy-MM-dd HH:mm:ss" v-model="filterData.dateValue"
                                type="datetimerange"
                                range-separator="至" start-placeholder="开始日期"
                                end-placeholder="结束日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <span slot="footer">
                <el-button type="primary" @click="confirmSearch">查询</el-button>
                <el-button @click="resetSearchConditions">清空</el-button>
                <el-button @click="queryVisible = false">取消</el-button>
            </span>
        </el-dialog>
    </div>
</template>
<script>
//局部引用
import Pagination from '@/components/pagination/pagination.vue'
import { getPlatformProductFromList, platformOutputExcel } from '@/api/platform/product/productCount'

const echarts = require('echarts')
export default {
    components: {
        Pagination
    },
    data () {
        return {
            activeName: 'all',
            currentQuery: {},
            queryVisible: false,
            keywords: null,
            showLoading: false,
            pickerOptions: {
                shortcuts: [{
                    text: '最近一个月',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
                        start.setHours('00', '00', '00')
                        end.setHours('23', '59', '59')
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '最近三个月',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
                        start.setHours('00', '00', '00')
                        end.setHours('23', '59', '59')
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '最近半年',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 180)
                        start.setHours('00', '00', '00')
                        end.setHours('23', '59', '59')
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '最近一年',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 360)
                        start.setHours('00', '00', '00')
                        end.setHours('23', '59', '59')
                        picker.$emit('pick', [start, end])
                    }
                },
                    // {
                    //     text: '最近二年',
                    //     onClick (picker) {
                    //         const end = new Date()
                    //         const start = new Date()
                    //         start.setTime(start.getTime() - 3600 * 1000 * 24 * 360 * 2)
                    //         start.setHours('00', '00', '00')
                    //         end.setHours('23', '59', '59')
                    //         picker.$emit('pick', [start, end])
                    //     }
                    // }, {
                    //     text: '全部',
                    //     onClick (picker) {
                    //         picker.$emit('pick', [])
                    //     }
                    // }
                ]
            },
            tableData: [],
            dataListSelections: [],
            paginationInfo: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            tableLoading: false,
            filterData: {
                dateValue: [],
                serialNum: '',
                staSellPrice: null,
                endSellPrice: null,
                shopName: '',
                enterpriseName: '',
                productName: '',
                orderBy: 0,
            },
            screenHeight: 0
        }
    },
    watch: {
        'filterData.orderBy': {
            handler () {
                this.getProductFromListM()
            }
        }
    },
    computed: {
        rightTableHeight () {
            return this.screenHeight - 292
        },
    },
    created () {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
        start.setHours('00', '00', '00')
        this.filterData.dateScope = [this.dateStrM(start), this.dateStrM(end)]
        this.getDefaultDateRange()
    },
    methods: {
        // 切换页签
        handleClick (tab, event) {
            console.log(tab, event)
            console.log(this.activeName)
            this.getProductFromListM()
        },
        // 日期处理
        padZero (num) {
            return num < 10 ? `0${num}` : num
        },
        dateStrM (date) {
            const year = date.getFullYear()
            const month = this.padZero(date.getMonth() + 1)
            const day = this.padZero(date.getDate())
            const hour = this.padZero(date.getHours())
            const minute = this.padZero(date.getMinutes())
            const second = this.padZero(date.getSeconds())
            return `${year}-${month}-${day} ${hour}:${minute}:${second}`
        },
        handleCurrentInventoryClick2 (row) {
            row.flag = !row.flag
            this.$refs.eltableCurrentRow2.toggleRowSelection(row, row.flag)
        },
        selectionChangeHandle (val) {
            this.dataListSelections = val
            console.log(this.dataListSelections)
        },
        outputAll () {
            if (this.tableData.length == 0) {
                return this.$message.error('数据为空！')
            }
            if (this.dataListSelections.length != 0) {
                let ids = this.dataListSelections.map(item => {
                    return item.productId
                })
                this.currentQuery.ids = ids
                this.tableLoading = true
                platformOutputExcel(this.currentQuery).then(res => {
                    const blob = new Blob([res], { type: 'application/vnd.ms-excel' }) // 2.获取请求返回的response对象中的blob 设置文件类型，这里以excel为例
                    const url = window.URL.createObjectURL(blob) // 3.创建一个临时的url指向blob对象
                    const a = document.createElement('a') // 4.创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
                    a.href = url
                    a.download = '平台商品信息报表.xlsx'
                    a.click()
                    window.URL.revokeObjectURL(url) // 5.释放这个临时的对象url
                    this.currentQuery.ids = []
                    this.dataListSelections = []
                    this.$message.success('操作成功')
                    this.tableLoading = false
                }).catch(() => {
                    this.tableLoading = false
                })
            } else {
                this.tableLoading = true
                platformOutputExcel(this.currentQuery).then(res => {
                    const blob = new Blob([res], { type: 'application/vnd.ms-excel' }) // 2.获取请求返回的response对象中的blob 设置文件类型，这里以excel为例
                    const url = window.URL.createObjectURL(blob) // 3.创建一个临时的url指向blob对象
                    const a = document.createElement('a') // 4.创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
                    a.href = url
                    a.download = '平台商品信息报表.xlsx'
                    a.click()
                    window.URL.revokeObjectURL(url) // 5.释放这个临时的对象url
                    this.$message.success('操作成功')
                    this.tableLoading = false
                }).catch(() => {
                    this.tableLoading = false
                })
            }
        },

        handleInputSearch () {
            this.resetSearchConditions()
            this.getProductFromListM()
        },
        resetSearchConditions () {
            this.filterData.dateValue = []
            this.filterData.serialNum = ''
            this.filterData.staSellPrice = ''
            this.filterData.endSellPrice = ''
            this.filterData.shopName = ''
            this.filterData.enterpriseName = ''
            this.filterData.productName = ''
        },
        // 高级查询
        confirmSearch () {
            this.keywords = ''
            this.getProductFromListM()
            this.queryVisible = false
        },
        getDefaultDateRange () {
            const endDate = new Date()
            const startDate = new Date()
            startDate.setMonth(startDate.getMonth() - 3)
            this.filterData.dateValue = [startDate, endDate]
        },
        getProductFromListM () {
            let params = {
                page: this.paginationInfo.currentPage,
                limit: this.paginationInfo.pageSize,
                productType: this.activeName === 'first' ? 0 : this.activeName === 'second' ? 1 : this.activeName === 'third' ? 2 : null,
            }
            if (this.filterData.shopName != null) {
                params.shopName = this.shopName
            }
            if (this.filterData.enterpriseName != null) {
                params.enterpriseName = this.enterpriseName
            }
            if (this.filterData.dateValue != null) {
                params.staPutawayDate = this.filterData.dateValue[0]
                params.endPutawayDate = this.filterData.dateValue[1]
            }
            if (this.filterData.productName != null) {
                params.productName = this.filterData.productName
            }
            if (this.keywords != null) {
                params.keywords = this.keywords
            }
            if (this.filterData.staSellPrice != null) {
                params.staSellPrice = this.filterData.staSellPrice
            }
            if (this.filterData.serialNum != null) {
                params.serialNum = this.filterData.serialNum
            }
            if (this.filterData.endSellPrice != null) {
                params.endSellPrice = this.filterData.endSellPrice
            }
            if (this.filterData.orderBy != null) {
                params.orderBy = this.filterData.orderBy
            }
            this.currentQuery = params
            getPlatformProductFromList(params).then(res => {
                console.log('list', res)
                this.paginationInfo.total = res.totalCount
                this.paginationInfo.pageSize = res.pageSize
                this.paginationInfo.currentPage = res.currPage
                this.tableData = res.list
                this.tableLoading = false
            }).catch(() => {
                this.tableLoading = false
            })
        },
        dateChange () {
            this.getProductFromListM()
        },
        initCharts () {
            // 基于准备好的dom，初始化echarts实例
            let myChart = echarts.init(this.$refs.chart)
            // 绘制图表
            myChart.setOption({
                itemStyle: {
                    // 高亮时点的颜色
                    color: '#74A0F9'
                },
                tooltip: {},
                xAxis: {
                    data: this.labelTitle
                },
                yAxis: {},
                series: [{
                    name: '数量',
                    type: 'bar',
                    data: this.count
                }]
            })
        },
    },
    //一加载页面就调用
    mounted () {
        this.screenHeight = document.documentElement.clientHeight || document.body.clientHeight
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
        start.setHours('00', '00', '00')
        this.filterData.dateScope = [this.dateStrM(start), this.dateStrM(end)]
        this.getProductFromListM()
        console.log(this.rightTableHeight)
    }
}
</script>
<style lang="scss" scoped>
/deep/ .e-form .el-form-item .el-form-item__content {
    display: flex;
    align-items: center;
}

.detail-info {
    width: 100%;
    min-width: 670px;
    padding: 30px 20px 70px;
    background-color: #fff;
    border-radius: 14px;
    box-shadow: 1px 1px 5px rgba(136, 136, 136, 0.5);
    position: relative;

    .buttons {
        right: 30px;
        bottom: 20px;
        position: absolute;
        text-align: right;
        // bottom: 100px;
    }
}

/deep/ .el-dialog {
    .el-dialog__body {
        height: 380px;
        margin-top: 0px;
    }
}

</style>