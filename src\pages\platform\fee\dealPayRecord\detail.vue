<template>
    <div class="e-form">
        <BillTop @cancel="handleClose"></BillTop>
        <div class="tabs warningTabs" style="padding-top: 70px;"  v-loading="formLoading">
            <el-tabs tab-position="left" v-model="tabsName" @tab-click="onChangeTab">
                <!-- <el-tab-pane label="缴费详情" name="baseInfo" :disabled="clickTabFlag">
                </el-tab-pane>
                <el-tab-pane label="缴费明细" name="baseInfoDtl" :disabled="clickTabFlag"  v-if="formDtl.state === 2">
                </el-tab-pane>
                <el-tab-pane label="审核历史" name="auditRecords" :disabled="clickTabFlag" /> -->
                <el-tab-pane label="基础信息" name="msgID1" :disabled="clickTabFlag"/>
                <el-tab-pane label="对公账户" name="msgID2" :disabled="clickTabFlag" v-if="formDtl.state >= 2"/>
                <el-tab-pane label="服务对账单确认" name="msgID3" :disabled="clickTabFlag"/>
                <el-tab-pane label="缴费信息" name="msgID4" :disabled="clickTabFlag" v-if="formDtl.state >= 2"/>
                <el-tab-pane label="审核记录" name="msgID5" :disabled="clickTabFlag" v-if="formDtl.state >= 2"/>

                <div id="tabs-content">
                    <div class="con">
                        <el-form :model="formDtl" label-width="200px" :rules="formDtlRules" ref="formDtlRef" :disabled="false" class="demo-ruleForm">
                            <div class="tabs-title" id="msgID1">基础信息</div>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="店铺名称：" prop="shopName">
                                        <span>{{formDtl.shopName}}</span>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="供应商名称：" prop="enterpriseName">
                                        <span>{{formDtl.enterpriseName}}</span>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="本次结算交易额：" prop="periodTransactionAmount">
                                        <span>{{formDtl.periodTransactionAmount | currency}}元</span>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="缴费状态：" prop="state">
                                        <el-tag type="info" v-if="formDtl.state == 0">待确认</el-tag>
                                        <el-tag type="warning" v-if="formDtl.state == 1">确认中</el-tag>
                                        <el-tag type="success" v-if="formDtl.state == 2">确认成功</el-tag>
                                        <el-tag type="danger" v-if="formDtl.state == 3">确认失败</el-tag>
                                        <el-tag v-if="formDtl.state == 4">审核中</el-tag>
                                        <el-tag type="success" v-if="formDtl.state == 5">审核通过</el-tag>
                                        <el-tag type="danger" v-if="formDtl.state == 6">审核未通过</el-tag>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="结算日期：" prop="settleDate">
                                        <span>{{formDtl.settleDate}}</span>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="累计结算交易额：" prop="totalTransactionAmount">
                                        <span>{{formDtl.totalTransactionAmount | currency}}元</span>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="交易服务费：" prop="payAmount">
                                        <span>{{formDtl.payAmount | currency}}元</span>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="当期交易覆盖交易时间段：" prop="periodRange">
                                        <span>{{formDtl.periodStartDate}} 至 {{formDtl.periodEndDate}}</span>
                                    </el-form-item>
                                </el-col>
                            </el-row>

                            <div class="tabs-title" id="msgID3">服务对账单确认</div>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="确认状态：" prop="state">
                                        <el-tag type="warning" v-if="formDtl.state == 1">确认中</el-tag>
                                        <el-tag type="success" v-if="formDtl.state == 2">确认成功</el-tag>
                                        <el-tag type="danger" v-if="formDtl.state == 3">确认失败</el-tag>
                                        <el-tag v-if="formDtl.state == 4">审核中</el-tag>
                                        <el-tag type="success" v-if="formDtl.state == 5">审核通过</el-tag>
                                        <el-tag type="danger" v-if="formDtl.state == 6">审核未通过</el-tag>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="24">
                                    <el-form-item label="对账单确认文件：" prop="confirmFile">
                                        <div>
                                            <div style="display: flex; align-items: center;">
                                                <span class="qrwjSpan">{{formDtl.dealFeeRecordUn}}-交易服务费对账单.pdf</span>
                                                <div class="qrwjSpanDiv">
                                                    <el-button size="small" @click="downloadZd">下载</el-button>
                                                    <el-button size="small" v-print="printObj">导出结算单</el-button>
                                                </div>
                                            </div>
                                        </div>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="24">
                                    <el-form-item label="" style="margin: -20px 0 0 0;">
                                        <div style="color: #666; font-size: 12px;margin-left: 200px;">
                                            确认对账单确认文件后请同时重新核对，然后下载出应对账单确认文件，导出结算单交公司财务部门进行后续收款流程
                                        </div>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <div class="tabs-title" id="msgID2" v-if="formDtl.state >= 2">对公账户</div>
                            <el-row v-if="formDtl.state >= 2">
                                <el-col :span="12">
                                    <el-form-item label="开户银行：" prop="khyh">
                                        <span>{{platformFreeyhAddress}}</span>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="银行户名：" prop="yhzh">
                                        <span>{{platformFreeyhOrgName}}</span>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row v-if="formDtl.state >= 2">
                                <el-col :span="12">
                                    <el-form-item label="银行账号：" prop="yhzh">
                                        <span>{{platformFreeyhAccount}}</span>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <div class="tabs-title" id="msgID4" v-if="formDtl.state >= 2">缴费信息</div>
                            <el-row v-if="formDtl.state >= 2">
                                <el-col :span="12">
                                    <el-form-item label="缴费金额：" prop="payAmount">
                                        <span>{{formDtl.payAmount | currency}}元</span>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="缴交时间：" prop="paymentTime">
                                        <span>{{formDtl.paymentTime || '2025-03-12 14:35:44'}}</span>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row v-if="formDtl.state >= 2">
                                <el-col :span="12">
                                    <el-form-item label="缴费凭证：" prop="paymentProof">
                                        <el-upload
                                            class="hide_box_min"
                                            action="fakeaction"
                                            :disabled="pmtSlipsLoading"
                                            :limit="1"
                                            :file-list="pmtSlips.length>0?pmtSlips.slice(0,1):[]"
                                            list-type="picture-card">
                                            <i slot="default" class="el-icon-plus"></i>
                                            <div slot="file" slot-scope="{file}">
                                                <img
                                                    class="el-upload-list__item-thumbnail"
                                                    :src="file.url"
                                                    alt=""
                                                    />
                                                <span class="el-upload-list__item-actions">
                                                    <span
                                                        class="el-upload-list__item-preview"
                                                        @click="handlePictureCardPreview(file)">
                                                        <i class="el-icon-zoom-in"></i>
                                                    </span>
                                                    <span
                                                        class="el-upload-list__item-delete"
                                                        @click="handlePaymentProofDownload(file)">
                                                        <i class="el-icon-download"></i>
                                                    </span>
                                                </span>
                                            </div>
                                        </el-upload>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="缴费状态：" prop="paymentStatus">
                                        <el-tag v-if="formDtl.state === 2">未缴费</el-tag>
                                        <el-tag v-else-if="formDtl.state === 4">审核中</el-tag>
                                        <el-tag type="danger" v-else-if="formDtl.state === 6">审核失败</el-tag>
                                        <el-tag type="success" v-else-if="formDtl.state === 5">审核成功</el-tag>
                                        <el-tag type="success" v-else>已缴费</el-tag>
                                    </el-form-item>
                                </el-col>
                            </el-row>

                        </el-form>
                    </div>
                    <!-- <div id="baseInfo" class="con">
                        <div class="tabs-title" id="baseInfo">缴费详情</div>
                        <el-form :model="formDtl" label-width="200px" :rules="formDtlRules" ref="formDtlRef" :disabled="false" class="demo-ruleForm">
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="缴费编号：" prop="dealFeeRecordUn">
                                        <span>{{formDtl.dealFeeRecordUn}}</span>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="缴费人：" prop="founderName">
                                        <span>{{formDtl.founderName}}</span>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="缴费方式：" prop="payType">
                                        <el-radio v-model="formDtl.payType" :label="1" >线下</el-radio>
                                        <el-radio v-model="formDtl.payType" disabled :label="2" >线上</el-radio>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="状态：" prop="state">
                                        <el-tag type="info" v-if="formDtl.state == 0">草稿</el-tag>
                                        <el-tag v-if="formDtl.state == 1">待审核</el-tag>
                                        <el-tag type="success" v-if="formDtl.state == 2">审核通过</el-tag>
                                        <el-tag type="danger" v-if="formDtl.state == 3">审核不通过</el-tag>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="缴费类型：" prop="recordType">
                                        <span v-if="formDtl.recordType == 1">店铺交易服务费</span>
                                        <span v-if="formDtl.recordType == 2">合同履约服务费用</span>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="缴费金额（元）：" prop="payAmount">
                                        <span style="color: red">{{formDtl.payAmount}}</span>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="多余退回余额金额：" prop="returnBalance" v-if="formDtl.state === 2">
                                        <span style="color: red">{{formDtl.returnBalance}}元</span>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="创建时间：" prop="gmtCreate">
                                        {{formDtl.gmtCreate}}
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="修改时间：" prop="gmtModified">
                                        {{formDtl.gmtModified}}
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="24">
                                    <el-form-item label="备注：" prop="remarks">
                                        <el-input :disabled="formDtl.state == 1 || formDtl.state == 2" style="width: 1000px;" type="textarea" :auto-resize="false" v-model="formDtl.remarks"
                                                  placeholder="请输入备注" maxlength="1000" show-word-limit></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="24">
                                    <el-form-item  label="缴费证明：" prop="file">
                                        <el-upload
                                            :class="formDtl.files.length === 1 ? 'hide_box_min' : ''"
                                            v-loading="uploadLoading"
                                            class="upload-demo"
                                            action="fakeaction"
                                            :limit="1"
                                            :file-list="fileList"
                                            :before-upload="handleBeforeUpload"
                                            :auto-upload="true"
                                            :http-request="uploadLicenseBusiness"
                                            list-type="picture-card">
                                            <div slot="tip" class="el-upload__tip">只能上传图片文件</div>
                                            <i slot="default" class="el-icon-plus"></i>
                                            <div slot="file" slot-scope="{file}">
                                                <img
                                                    class="el-upload-list__item-thumbnail"
                                                    :src="file.url" alt="">
                                                <span class="el-upload-list__item-actions">
                                    <span
                                        class="el-upload-list__item-preview"
                                        @click="handlePictureCardPreview(file)">
                                      <i class="el-icon-zoom-in"></i>
                                    </span>
                                    <span
                                        class="el-upload-list__item-delete"
                                        @click="handleDownload(file)">
                                      <i class="el-icon-download"></i>
                                    </span>
                                  </span>
                                            </div>
                                        </el-upload>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </el-form>
                    </div>
                    <div id="baseInfoDtl" class="con"  v-if="formDtl.state === 2">
                        <div class="tabs-title" id="baseInfoDtl">缴费明细</div>
                        <div class="e-table"  style="background-color: #fff" v-loading="dtlTableListLoading">
                            <el-table
                                border
                                style="width: 100%"
                                max-height="342px"
                                ref="tableListRef"
                                :data="formDtl.dtlVOs"
                                @selection-change="tableListSelectChange"
                                @row-click="tableListRowClick"
                                :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
                                :row-style="{ fontSize: '14px', height: '48px' }"
                            >
                                <el-table-column label="序号" type="index" width="60"></el-table-column>
                                <el-table-column prop="relevanceNu" label="对账单编号"/>
                                <el-table-column prop="projectEnterpriseName" label="收货单位"/>
                                <el-table-column prop="enterpriseName" label="供货单位"/>
                                <el-table-column prop="dealAmount" label="交易金额"/>
                                <el-table-column prop="feeRatio" label="收取比例（‰）"/>
                                <el-table-column prop="serveFee" label="服务费用"/>
                                <el-table-column prop="payFee" label="已缴费金额"/>
                                <el-table-column prop="residuePayFee" label="剩余未缴费金额"/>
                                <el-table-column prop="finishPayFee" label="是否完成缴费">
                                    <template v-slot="scope">
                                        <el-tag type="success" v-if="scope.row.finishPayFee === 1">是</el-tag>
                                        <el-tag type="danger" v-if="scope.row.finishPayFee === 0">否</el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="payAmount" label="本次缴费金额">
                                    <template v-slot="scope">
                                        <span style="color: red">{{scope.row.payAmount}}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="gmtCreate" label="交易时间" width="160"/>
                            </el-table>
                        </div>
                    </div> -->
                    <div id="auditRecords" class="con" v-if="formDtl.state >= 2">
                        <div class="tabs-title" id="msgID5">审核记录</div>
                        <div class="e-table"  style="background-color: #fff">
                            <el-table
                                border
                                style="width: 100%"
                                :data="formDtl.auditRecords"
                                :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
                                :row-style="{ fontSize: '14px', height: '48px' }"
                            >
                                <el-table-column label="序号" type="index" width="60"></el-table-column>
                                <el-table-column prop="auditType" label="审核结果" width="160">
                                    <template slot-scope="scope">
                                        <span v-if="scope.row.auditType == 1">审核通过</span>
                                        <span v-if="scope.row.auditType == 2">审核未通过</span>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="relevanceType" label="审核类型" width="160">
                                    <template slot-scope="scope">
                                        <span v-if="scope.row.relevanceType == 12">对账单确认审核</span>
                                        <span v-else>缴费审核</span>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="auditResult" label="审核意见" width="">
                                </el-table-column>
                                <el-table-column prop="founderName" label="审核人" width="200">
                                </el-table-column>
                                <el-table-column prop="gmtCreate" label="审核时间" width="160">
                                </el-table-column>
                            </el-table>
                        </div>

                    </div>
                </div>
            </el-tabs>
            <el-dialog class="showImage" v-dialogDrag :visible.sync="dialogVisible">
                <img width="100%" :src="dialogImageUrl" alt="">
            </el-dialog>
            <el-dialog
                class="selectDealDia"
                title="选择待缴费明细" v-dialogDrag v-loading="selectNotPayFeeLoading" :visible.sync="showSelectNotPayFee"
                width="70%">
                <div class="e-table" style="background-color: #ffffff">
                    <div class="top" style="height: 50px; padding-left: 10px">
                        <div class="left">
                            <el-input  type="text" @blur="getNotPayFeeTableList" placeholder="输入搜索关键字" v-model="keywords2">
                                <img :src="require('@/assets/search.png')" slot="suffix" @click="getNotPayFeeTableList" />
                            </el-input>
                            <div class="search_box" style="margin-left: 20px">
                                <!--                            <el-button type="primary">11</el-button>-->
                            </div>
                        </div>
                    </div>
                    <el-table
                        class="table"
                        ref="siteReceivingTableRef"
                        max-height="342px"
                        border
                        @selection-change="siteReceivingTableSelectM"
                        @row-click="siteReceivingTableRowClickM"
                        :data="notPayFeeTableList"
                    >
                        <el-table-column type="selection" width="50"/>
                        <el-table-column label="序号" type="index" width="60"/>
                        <el-table-column prop="relevanceNu" label="对账单编号"/>
                        <el-table-column prop="projectEnterpriseName" label="收货单位"/>
                        <el-table-column prop="dealAmount" label="交易金额"/>
                        <el-table-column prop="feeRatio" label="收取比例（‰）"/>
                        <el-table-column prop="serveFee" label="服务费用"/>
                        <el-table-column prop="residuePayFee" label="缴费金额（元）">
                            <template v-slot="scope">
                                <span style="color: red">{{scope.row.residuePayFee}}</span>
                            </template>
                        </el-table-column>
                        <!--                    <el-table-column prop="payFee" label="已缴费金额"/>-->
                        <el-table-column prop="gmtCreate" label="交易时间" width="160"/>
                    </el-table>
                    <Pagination
                        v-show="notPayFeeTableList && notPayFeeTableList.length > 0"
                        :currentPage.sync="paginationInfo2.currentPage"
                        :pageSize.sync="paginationInfo2.pageSize"
                        :total="paginationInfo2.total"
                        @currentChange="getNotPayFeeTableList"
                        @sizeChange="getNotPayFeeTableList"
                    />
                </div>
                <span slot="footer">
                <el-button type="primary" class="btn-blue" @click="selectNotPayFeeAffirmClick">确认选择</el-button>
                <el-button @click="showSelectNotPayFee = false">取消</el-button>
            </span>
            </el-dialog>
        </div>
        <div class="buttons">
            <el-button type="primary" class="btn-greenYellow"
                       v-if="formDtl.state === 1 || formDtl.state === 4"
                       @click="handleAuditPass">审核通过
            </el-button>
            <el-button type="primary" class="btn-delete"
                       v-if="formDtl.state === 1 || formDtl.state === 4"
                       @click="handleAuditReject">审核不通过
            </el-button>
            <el-button @click="handleClose">返回</el-button>
        </div>
        <div class="printBrideDiv" id="brideID" ref="pdfContent">
        <table>
            <tr>
                <td class="wTd" colspan="5">四川路桥建设集团股份有限公司物资分公司</td>
                <td></td>
            </tr>
            <tr>
                <td class="wTd" colspan="4">结算单</td>
                <td></td>
                <td></td>
            </tr>
            <tr>
                <td class="leftTd" colspan="3">供应商：{{tableMsg.gys}}</td>
                <td class="sjTd">时间：{{tableMsg.sj}}</td>
                <td></td>
                <td></td>
            </tr>
            <tr>
                <td class="hTd">结算内容</td>
                <td class="hTd">结算区间</td>
                <td class="hTd">结算金额</td>
                <td class="hTd">备注</td>
                <td class="yebTd" rowspan="4">第二联财务部记账</td>
                <td class="yebTd" rowspan="4">第一联电商运营部记账</td>
            </tr>
            <tr>
                <td class="hTd2">交易服务费</td>
                <td class="hTd2">{{tableMsg.jsqj}}</td>
                <td class="hTd">{{tableMsg.jsje}}</td>
                <td class="hTd">{{tableMsg.bz}}</td>
            </tr>
            <tr>
                <td class="leftTd" colspan="4">合同编号：{{tableMsg.htbh}}</td>
            </tr>
            <tr>
                <td colspan="4">
                    <div class="flexTdDiv">
                        <div>分管领导：{{tableMsg.fgld}}</div>
                        <div>财务部：{{tableMsg.cwb}}</div>
                        <div>电商运营部：{{tableMsg.dsyyb}}</div>
                        <div>经办人：{{tableMsg.jbr}}</div>
                    </div>
                </td>
            </tr>
        </table>
        </div>
    </div>
</template>
<script>
import $ from 'jquery'
import '@/utils/jquery.scrollTo.min'
// eslint-disable-next-line no-unused-vars
import { toFixed } from '@/utils/common'
// eslint-disable-next-line no-unused-vars
import { getUuid, throttle, calculateYesTarRateAmount, calculateNotTarRateAmount } from '@/utils/common'
// eslint-disable-next-line no-unused-vars
import { mapState } from 'vuex'
import {
    findDealFeeBySn,
    platformDealFeeRecordUpdate,
    deleteDealFeeRecord,
    platformDealFeeRecordAudit,
    platformDealFeeRecordDtlBatchDelete,
    notPayDealFeeDtlList,
    platformDealFeeRecordDtlBatchCreate,
    supplierFetchSystemParams,
    supplierFetchAudits
} from '@/api/fee/feeApi'
import { previewFile, uploadFile } from '@/api/platform/common/file'
import html2pdf from 'html2pdf.js'
import { createFileRecordDelete } from '@/api/shopManage/fileRecordDelete/fileRecordDelete'
import Pagination from '@/components/pagination/pagination.vue'

const getSysValue = (systemParams, key) => {
    if (!systemParams) {
        return '加载中'
    }
    const systemParam = systemParams.find(s => s.code == key)
    if (!systemParam) {
        return '加载失败'
    }
    return systemParam.keyValue
}
export default {
    filters: {
        dateStr (dateStr) {
            if (dateStr == null) {
                return
            }
            return  dateStr.split(' ')[0]
        },
        currency (value) {
            if (!value) return '0.00'
            return Number(value).toFixed(2)
        }
    },
    components: {
        Pagination
    },
    data () {
        return {
            fileList: [],
            dialogImageUrl: '',
            dialogVisible: false,
            formDtlRules: {
                startEndTme: [
                    { required: true, message: '请选择对账时间', trigger: 'blur' },
                ],
                reconciliationProductType: [
                    { required: true, message: '请选择业务类型', trigger: 'blur' },
                ],
            },
            uploadLoading: false,
            dtlTableListLoading: false,
            freeAmount: 2000,
            formDtl: {
                files: []
            },
            paginationInfo2: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            tableData: [],
            notPayFeeTableList: [],
            selectNotPayFeeTableList: [],
            selectTableList: [],
            tabsName: 'baseInfo',
            screenWidth: 0,
            screenHeight: 0,
            lastConHeight: 0,
            formLoading: false,
            keywords2: null,
            showSelectNotPayFee: false,
            selectNotPayFeeLoading: false,
            clickTabFlag: false, // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            winEvent: {},
            topHeight: 120,
            systemParams: null, // 系统参数，用于获取对公账户信息
            pmtSlips: [], // 缴费凭证文件列表
            pmtSlipsLoading: false, // 缴费凭证加载状态
            // 打印
            printObj: {
                id: 'brideID',
                popTitle: '交易服务费对账',
                extraCss: 'https://www.baidu.com/',
                extraHead: '<meta http-equiv="Content-Language"content="zh-cn"/>'
            },
            tableMsg: {
                gys: '',
                sj: '',
                jsqj: '',
                jsje: '',
                bz: '',
                htbh: '',
                fgld: '',
                cwb: '',
                dsyyb: '',
                jbr: ''
            }
        }
    },
    created () {
        this.getFormDtl()
        // 获取系统参数（对公账户信息）
        supplierFetchSystemParams().then(res => {
            this.systemParams = res
        }).catch(error => {
            console.error('获取系统参数失败:', error)
            // 系统参数获取失败时不显示错误消息，因为这不是关键功能
        })
    },
    computed: {
        ...mapState(['userInfo']),
        // tab内容高度
        tabsContentHeight () {
            return this.screenHeight - 140 + 'px'
        },
        // 填补底部空白，以使高度够滚动
        seatHeight () {
            return this.screenHeight - 72 - this.lastConHeight
        },
        // 平台收费银行开户行
        platformFreeyhAddress () {
            return getSysValue(this.systemParams, 'platformFreeyhAddress')
        },
        // 平台收费银行账号
        platformFreeyhAccount () {
            return getSysValue(this.systemParams, 'platformFreeyhAccount')
        },
        // 平台收费公司名称
        platformFreeyhOrgName () {
            return getSysValue(this.systemParams, 'platformFreeyhOrgName')
        },
    },
    watch: {
        screenHeight: {
            handler (newVal) {
                $('#tabs-content').height(newVal - 71)
            }
        },
    },
    methods: {
        /** 下载账单 */
        downloadZd () {
            // 参照供应商端实现，生成PDF下载
            this.generatePdf()
        },
        /** 生成PDF */
        generatePdf () {
            const element = this.$refs.pdfContent || document.querySelector('.printBrideDiv')
            const opt = {
                margin: 0.5,
                filename: `${this.formDtl.dealFeeRecordUn || 'deal-fee-record'}-交易服务费对账单.pdf`,
                image: { type: 'jpeg', quality: 0.98 },
                html2canvas: { scale: 2, scrollX: 0, scrollY: 0 },
                jsPDF: { unit: 'in', format: 'a4', orientation: 'portrait' }
            }
            html2pdf().set(opt).from(element).save()
        },
        /** 缴费凭证图片预览 */
        handlePictureCardPreview (file) {
            this.dialogImageUrl = file.url
            this.dialogVisible = true
        },
        /** 缴费凭证下载 */
        handlePaymentProofDownload (file) {
            this.uploadLoading = true
            // 如果file有fileFarId，使用previewFile下载
            if (file.fileFarId) {
                previewFile({ recordId: file.fileFarId }).then(res => {
                    const blob = new Blob([res])
                    const url = window.URL.createObjectURL(blob)
                    let a = document.createElement('a')
                    a.href = url
                    a.download = file.name
                    a.click()
                    window.URL.revokeObjectURL(url)
                }).finally(() => {
                    this.uploadLoading = false
                })
            } else {
                // 如果没有fileFarId，直接使用url下载
                let a = document.createElement('a')
                a.href = file.url
                a.download = file.name || '缴费凭证'
                a.click()
                this.uploadLoading = false
            }
        },
        /** 导出账单 */
        exportZd () {
            //
        },
        /** 审核通过 */
        handleAuditPass () {
            this.clientPop('info', '您确定要审核通过吗？', async () => {
                let params = {
                    id: this.formDtl.dealFeeRecordId,
                    isOpen: 1,
                }
                this.formLoading = true
                platformDealFeeRecordAudit(params).then(res => {
                    if (res.code != null && res.code == 200) {
                        this.$message.success('审核通过成功')
                        this.getFormDtl()
                    }
                }).finally(() => {
                    this.formLoading = false
                })
            })
        },
        /** 审核不通过 */
        handleAuditReject () {
            this.$prompt('未通过原因', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'error',
                inputType: 'textarea',
                inputPlaceholder: '请输入不通过原因',
                inputPattern: /^.+$/,
                inputErrorMessage: '请输入不通过原因'
            }).then(({ value }) => {
                let params = {
                    id: this.formDtl.dealFeeRecordId,
                    isOpen: 0,
                    auditResult: value,
                }
                this.formLoading = true
                platformDealFeeRecordAudit(params).then(res => {
                    if (res.code != null && res.code == 200) {
                        this.$message.success('审核不通过成功')
                        this.getFormDtl()
                    }
                }).finally(() => {
                    this.formLoading = false
                })
            })
        },
        batchDelete () {
            if(this.selectTableList.length == 0) {
                return this.$message.error('未选择数据！')
            }
            this.clientPop('info', '您确定要批量删除操作吗？', async () => {
                this.dtlTableListLoading = true
                let arr = this.selectTableList.map(item => {
                    return item.dealFeeRecordDtlId
                })
                platformDealFeeRecordDtlBatchDelete(arr).then(res => {
                    if(res.code != null && res.code == 200) {
                        this.$message.success('操作成功')
                        this.selectTableList = []
                        this.getFormDtl()
                    }
                }).finally(() => {
                    this.dtlTableListLoading = false
                })
            })
        },
        rowDelete (row) {
            this.clientPop('info', '您确定要删除操作吗？', async () => {
                this.dtlTableListLoading = true
                platformDealFeeRecordDtlBatchDelete([row.dealFeeRecordDtlId]).then(res => {
                    if(res.code != null && res.code == 200) {
                        this.$message.success('操作成功')
                        this.getFormDtl()
                    }
                }).finally(() => {
                    this.dtlTableListLoading = false
                })
            })
        },
        selectNotPayFeeAffirmClick () {
            if(this.selectNotPayFeeTableList.length == 0) {
                return this.$message.error('未选择明细！')
            }
            this.clientPop('info', '您确定要批量追加至缴费明细吗？', async () => {
                this.selectNotPayFeeLoading = true
                let arr = []
                for (let i = 0; i < this.selectNotPayFeeTableList.length; i++) {
                    let t = this.selectNotPayFeeTableList[i]
                    arr.push({
                        dealFeeRecordDtlId: null,
                        dealFeeRecordId: this.formDtl.dealFeeRecordId,
                        platformDealFeeDtlId: t.platformDealFeeDtlId,
                        payAmount: t.residuePayFee
                    })
                }
                platformDealFeeRecordDtlBatchCreate(arr).then(res => {
                    if(res.code != null && res.code == 200) {
                        this.$message.success('操作成功')
                        this.selectNotPayFeeTableList = []
                        this.showSelectNotPayFee = false
                        this.getFormDtl()
                    }
                }).finally(() => {
                    this.selectNotPayFeeLoading = false
                })
            })
        },
        siteReceivingTableRowClickM (row) {
            row.flag = !row.flag
            this.$refs.siteReceivingTableRef.toggleRowSelection(row, row.flag)
        },
        siteReceivingTableSelectM (value) {
            this.selectNotPayFeeTableList = value
        },
        tableListRowClick (row) {
            row.flag = !row.flag
            this.$refs.tableListRef.toggleRowSelection(row, row.flag)
        },
        tableListSelectChange (value) {
            this.selectTableList = value
        },
        selectNotPayFeeClick () {
            this.showSelectNotPayFee = true
            this.getNotPayFeeTableList()
        },
        getNotPayFeeTableList () {
            let params = {
                page: this.paginationInfo2.currentPage,
                limit: this.paginationInfo2.pageSize,
                serveType: this.formDtl.recordType,
            }
            if (this.keywords2 != null && this.keywords2 != '') {
                params.keywords = this.keywords2
            }
            this.selectNotPayFeeLoading = true
            notPayDealFeeDtlList(params).then(res => {
                this.paginationInfo2.total = res.totalCount
                this.paginationInfo2.pageSize = res.pageSize
                this.paginationInfo2.currentPage = res.currPage
                this.notPayFeeTableList = res.list
            }).finally(() => {
                this.selectNotPayFeeLoading = false
            })
        },
        auditPlanM (state, title) {
            this.clientPop('info', '您确定进行【' + title + '】操作吗？', async () => {
                if (state == 0) {
                    this.$prompt('未通过原因', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'error',
                        inputType: 'textarea',
                        inputPlaceholder: '请输入不通过原因',
                        inputPattern: /^.+$/,
                        inputErrorMessage: '请输入不通过原因'
                    }).then(({ value }) => {
                        let params = {
                            id: this.formDtl.dealFeeRecordId,
                            isOpen: 0,
                            auditResult: value,
                        }
                        this.formLoading = true
                        platformDealFeeRecordAudit(params).then(res => {
                            if (res.code != null && res.code == 200) {
                                this.$message.success('操作成功')
                                this.getFormDtl()
                            }
                        }).finally(() => {
                            this.formLoading = false
                        })
                    })
                } else {
                    let params = {
                        id: this.formDtl.dealFeeRecordId,
                        isOpen: 1,
                    }
                    this.formLoading = true
                    platformDealFeeRecordAudit(params).then(res => {
                        if (res.code != null && res.code == 200) {
                            this.$message.success('操作成功')
                            this.getFormDtl()
                        }
                    }).finally(() => {
                        this.formLoading = false
                    })
                }
            })
        },
        checkInputQty () {
            const regex = /^-?\d+$/
            if (!regex.test(this.formDtl.paymentDuration)) {
                this.$message.error('请输入有效的数字')
                this.formDtl.payAmount = this.freeAmount
                return this.formDtl.paymentDuration = 1
            }
            if (this.formDtl.paymentDuration < 0 || this.formDtl.paymentDuration > 999) {
                this.$message.error('超过限制！')
                this.formDtl.payAmount = this.freeAmount
                return this.formDtl.paymentDuration = 1
            }
            this.formDtl.payAmount = this.formDtl.paymentDuration * this.freeAmount
        },
        handleBeforeUpload (file) {
            const sizeOk = file.size / 1024 / 1024 < this.uploadImgSize
            if(!sizeOk) {
                this.$message.error(`上传的图片大小不能超过 ${this.uploadImgSize}MB!`)
            }
            return sizeOk
        },
        uploadLicenseBusiness (params) {
            let file = params.file
            const form = new FormData()
            form.append('files', file)
            form.append('bucketName', 'mall-private') //存储桶名称
            form.append('directory', 'material') // 商城类型
            form.append('fileType', 1)
            this.uploadLoading = true
            uploadFile(form).then(res => {
                if(res.code != null && res.code != 200) {
                    this.formDtl.files = []
                    this.fileList = []
                }else {
                    this.formDtl.files = []
                    let resO = res[0]
                    this.formDtl.files.push({
                        name: resO.objectName,
                        relevanceType: 2,
                        url: resO.nonIpObjectPath,
                        fileFarId: resO.recordId,
                        fileType: 1
                    })
                }
                this.$message({
                    message: '上传成功',
                    type: 'success'
                })
            }).finally(() =>{
                this.uploadLoading = false
            })
        },
        // eslint-disable-next-line no-unused-vars
        formDtlFileRemove (file, fileList) {
            this.uploadLoading = true
            createFileRecordDelete({ recordId: this.formDtl.files[0].fileFarId }).then(res => {
                if(res.code == 200) {
                    this.$message({
                        message: res.message,
                        type: 'success'
                    })
                }
                this.formDtl.files = []
                this.fileList = []
            }).finally(() =>{
                this.uploadLoading = false
            })
        },
        // eslint-disable-next-line no-unused-vars
        handleDownload (file) {
            this.uploadLoading = true
            let image = this.formDtl.files[0]
            previewFile({ recordId: image.fileFarId }).then(res => {
                const blob = new Blob([res])
                const url = window.URL.createObjectURL(blob)
                let a = document.createElement('a')
                a.href = url
                a.download = image.name
                a.click()
                window.URL.revokeObjectURL(url)
            }).finally(() =>{
                this.uploadLoading = false
            })
        },
        deleteOneM () {
            this.clientPop('info', '您确定要删除操作吗？', async () => {
                this.formLoading = true
                deleteDealFeeRecord({ id: this.formDtl.dealFeeRecordId }).then(res => {
                    if(res.code != null && res.code == 200) {
                        this.$message.success('操作成功')
                        this.$router.go(-1)
                    }
                }).finally(() => {
                    this.formLoading = false
                })
            })
        },
        saveSheetM (num) {
            this.clientPop('info', '您确定要该操作吗？', async () => {
                this.formDtl.submitAud = num
                this.formLoading = true
                platformDealFeeRecordUpdate(this.formDtl).then(res => {
                    if(res.code != null && res.code == 200) {
                        this.$message.success('操作成功')
                        this.getFormDtl()
                    }
                }).finally(() => {
                    this.formLoading = false
                })
            })
        },
        getFormDtl () {
            this.formLoading = true
            findDealFeeBySn({ sn: this.$route.query.sn }).then(res => {
                this.formDtl = res

                // 设置打印表格数据
                this.tableMsg.gys = res.enterpriseName || '企业名称'
                this.tableMsg.sj = res.settleDate || new Date().toLocaleDateString()
                this.tableMsg.jsqj = (res.periodStartDate || '') + ' 至 ' + (res.periodEndDate || '')
                this.tableMsg.jsje = (res.payAmount ? Number(res.payAmount).toFixed(2) : '0.00') + '元'
                this.tableMsg.bz = res.remarks || '备注'
                this.tableMsg.htbh = res.contractNo || res.dealFeeRecordUn || '合同编号'
                this.tableMsg.fgld = '分管领导'
                this.tableMsg.cwb = '财务部'
                this.tableMsg.dsyyb = '电商运营部'
                this.tableMsg.jbr = res.auditorName || res.founderName || '经办人'

                // 处理缴费证明文件
                if (res.files && res.files.length > 0) {
                    let image = res.files[0]
                    this.pmtSlipsLoading = true
                    previewFile({ recordId: image.fileFarId }).then(fileRes => {
                        const blob = new Blob([fileRes])
                        const url = window.URL.createObjectURL(blob)
                        this.pmtSlips = []
                        this.pmtSlips.push({
                            name: image.name,
                            url: url,
                            fileFarId: image.fileFarId
                        })
                        // 同时保持原有的fileList逻辑，以免影响其他功能
                        this.fileList = []
                        this.fileList.push({
                            name: image.name,
                            url: url
                        })
                    }).finally(() => {
                        this.pmtSlipsLoading = false
                        this.uploadLoading = false
                    })
                } else {
                    this.pmtSlips = []
                    this.fileList = []
                }

                // 获取审核记录
                if (res.dealFeeRecordId) {
                    supplierFetchAudits(res.dealFeeRecordId).then(auditRes => {
                        // 将审核记录添加到formDtl中，以便模板使用
                        this.$set(this.formDtl, 'auditRecords', auditRes || [])
                    }).catch(error => {
                        console.error('获取审核记录失败:', error)
                        this.$set(this.formDtl, 'auditRecords', [])
                    })
                } else {
                    this.$set(this.formDtl, 'auditRecords', [])
                }
            }).finally(() => {
                this.formLoading = false
            })
        },
        fixed4 (num) {
            return toFixed(num, 4)
        },
        fixed2 (num) {
            return toFixed(num, 2)
        },

        // 获取最后一个内容区域的高度，计算底部空白
        getLastConHeight () {
            let si = setInterval(() => {
                // 因为dom异步加载，通过轮询找到渲染后的dom，获取高度
                if (document.getElementById('terminationLog')) {
                    this.lastConHeight = document.getElementById('terminationLog').offsetHeight
                    clearInterval(si)
                    si = null
                }
            }, 100)
        },
        // 返回
        handleClose () {
            this.$router.go(-1)
        },
        onChangeTab (e) {
            const height = $('#' + e.name).offset().top
            try{
                $('#tabs-content').scrollTo(height - this.topHeight, 500)
            }catch (err) {
                console.log(err)
            }
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            this.clickTabFlag = true
            // 动画结束后，恢复状态
            setTimeout(() => {
                this.clickTabFlag = false
            }, 600)
        },
        beforeDestroy () {
            window.removeEventListener('scroll', this.winEvent.onScroll)
            window.removeEventListener('resize', this.winEvent.onResize)
        },
    },
    mounted () {
        // 获取数据
        // 获取最后一个内容区域的高度，计算底部空白
        this.getLastConHeight()
        // 保存所有tabName
        const arr = ['baseInfo', 'auditRecords']
        this.tabArr = arr
        let $idsTop = []
        this.winEvent.onScroll = () => {
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            if (this.clickTabFlag) {
                return
            }
            if (!$idsTop[$idsTop.length - 1]) {
                $idsTop = arr.map(item => {
                    const $item = document.getElementById(item)
                    let itemTop = null
                    if ($item) {
                        itemTop = $item.offsetTop
                    }
                    return itemTop
                })
            }
            const scrollTop = $('#tabs-content')[0].scrollTop
            // 倒序查找
            let curLocal = 0
            for (let i = $idsTop.length - 1; i >= 0; i--) {
                let item = $idsTop[i]
                if (scrollTop + 1 >= item) {
                    curLocal = i
                    break
                }
            }
            // 设置对应tabName
            this.tabsName = arr[curLocal]
        }
        $('#tabs-content')[0].addEventListener('scroll', this.winEvent.onScroll)
        this.screenWidth = document.documentElement.clientWidth - this.topHeight
        this.screenHeight = document.documentElement.clientHeight - this.topHeight
        const onResize = () => {
            this.screenWidth = document.documentElement.clientWidth - this.topHeight
            this.screenHeight = document.documentElement.clientHeight - this.topHeight
            $idsTop = arr.map(item => {
                return document.getElementById(item).offsetTop
            })
        }
        this.winEvent.onResize = throttle(onResize, 500)
        window.addEventListener('resize', this.winEvent.onResize)
        if (this.$route.query.name) {
            setTimeout(() => {
                this.onChangeTab({ name: this.$route.query.name })
                this.tabsName = this.$route.query.name
            }, 200)
        }
    },
}
</script>

<style lang='scss' scoped>
.e-table {
    min-height: auto;
    background: #fff;
}

/deep/ .e-form .el-form-item .el-form-item__content {
    height: unset !important;
    display: flex;
    align-items: center;
}

.action span {
    color: #216EC6;
    cursor: pointer;
    user-select: none;

    &:not(:last-of-type) {
        margin-right: 15px;
    }
}
::v-deep input::-webkit-outer-spin-button,
::v-deep input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}
::v-deep input[type=‘number’] {
    -moz-appearance: textfield !important;
}
/deep/ .showImage {
    .el-dialog__header {
        //display: none;
        padding-bottom: 30px;
        margin-bottom: 0px;
    }
    .el-dialog__body {
        height: 680px;
        padding: 0px;
        margin: 0px;
    }
}
/deep/ .hide_box_min .el-upload--picture-card {
    display: none;
}

/deep/ .selectDealDia {
    .el-dialog__body {
        margin-top: 0px;
    }
}
.qrwjSpan {
    color: #0000ff;margin-right: 10px;
}
.qrwjSpanDiv {display: inline-block;height: 32px;}

/** 去掉页眉、页脚 */
@page{
    margin-top:1mm;
    margin-bottom: 1mm;
}
.printBrideDiv {
    color: #000;font-size: 14px;width: 100%;position: absolute;z-index: -1;
    line-height: 20px;width: 100%;padding: 20px 0;top: 0;
    table {
        width: 100%;border-collapse: collapse;
        td {height: 50px;border: 1px solid #000;padding: 3px 5px;text-align: center;}
        td.sjTd {width: 25%;}
        td.leftTd {text-align: left;}
        td.wTd {font-weight: bold;font-size: 16px;}
        td.yebTd {
            width: 50px;padding: 3px 20px;
        }
        td.hTd {
            height: 80px;
        }
        td.hTd2 {
            height: 100px;
        }
        td.hTd {height: 100px;}
        .flexTdDiv {display: flex;justify-content: space-between;}
    }
}
</style>