<template>
  <div class="base-page">
    <!-- 上半部分 -->
    <div class="top-section">
      <div class="e-table">
        <div class="top">
          <div class="left">
            <el-tabs v-model="activeName" @tab-click="handleClick">
              <el-tab-pane label="内部人员" name="first"></el-tab-pane>
              <el-tab-pane label="外部人员" name="second"></el-tab-pane>
            </el-tabs>
          </div>
          <div class="search_box">
            <el-input
                @blur="handleInputSearch"
                placeholder="输入搜索关键字"
                v-model="keywords"
            >
              <img src="@/assets/search.png" slot="suffix" @click="handleInputSearch" />
            </el-input>
            <div class="adverse">
              <el-button type="primary" size="small" @click="queryVisible = true">高级搜索</el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 下半部分：左右结构 -->
    <div class="bottom-section" v-if="activeName === 'first'">
      <!-- 左侧树 -->
      <div class="left">
        <org-tree ref="materialClassRef" :productType="0" is-lc="0" />
      </div>

      <!-- 右侧表格与分页 -->
      <div class="right">
        <!-- 表格 -->
        <div class="e-table" v-loading="tableLoading">
          <el-table
              class="table"
              :height="rightTableHeight"
              :data="tableData"
              border
          >
            <el-table-column label="序号" type="index" width="60"></el-table-column>
            <el-table-column label="昵称" prop="nickname"></el-table-column>
            <el-table-column label="经办人姓名" prop="name"></el-table-column>
            <el-table-column label="性别" prop="gender"></el-table-column>
            <el-table-column label="联系电话" prop="phone"></el-table-column>
            <el-table-column label="地址" prop="address"></el-table-column>
            <el-table-column label="所属机构" prop="orgName"></el-table-column>
          </el-table>
        </div>

        <!-- 分页 -->
        <Pagination
            v-show="tableData && tableData.length > 0"
            :total="paginationInfo.total"
            :pageSize.sync="paginationInfo.pageSize"
            :currentPage.sync="paginationInfo.currentPage"
            @currentChange="getTableData"
            @sizeChange="getTableData"
        />
      </div>
    </div>
    <div class="bottom-section" v-else>
      <div class="right">
        <!-- 表格 -->
        <div class="e-table" v-loading="tableLoading">
          <el-table
              class="table"
              :height="rightTableHeight"
              :data="outTableData"
              border
          >
            <el-table-column label="序号" type="index" width="60"></el-table-column>
            <el-table-column label="昵称" prop="adminName"></el-table-column>
            <el-table-column label="经办人姓名" prop="adminName"></el-table-column>
            <el-table-column label="性别" prop="gender">
              <template slot-scope="scope">
               {{ getGenderById(scope.row.adminNumber) || '--' }}
              </template>
            </el-table-column>
            <el-table-column label="联系电话" prop="adminPhone"></el-table-column>
            <el-table-column label="地址" prop="detailedAddress"></el-table-column>
            <el-table-column label="所属机构" prop="enterpriseName"></el-table-column>
          </el-table>
        </div>

        <!-- 分页 -->
        <Pagination
            v-show="outTableData && outTableData.length > 0"
            :total="outPaginationInfo.total"
            :pageSize.sync="outPaginationInfo.pageSize"
            :currentPage.sync="outPaginationInfo.currentPage"
            @currentChange="getOutTableData"
            @sizeChange="getOutTableData"
        />
      </div>
    </div>
    <!--        高级查询-->
    <el-dialog v-dialogDrag title="高级查询" :visible.sync="queryVisible" width="50%">
      <el-form :model="filterData" ref="form" label-width="120px" :inline="false" >
        <el-row>
          <el-col :span="12">
            <el-form-item width="150px" label="昵称：" prop="nickName">
              <el-input v-model="filterData.nickName" placeholder="请输入昵称" clearable></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item width="150px" label="名称：" prop="name">
              <el-input v-model="filterData.name" placeholder="请输入名称" clearable></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item width="150px" label="联系电话：" prop="phone">
              <el-input v-model="filterData.phone" placeholder="请输入昵称" clearable></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <!--                <el-row>
                            <el-col :span="12">
                                <el-form-item label="协议编号：" prop="city">
                                    <el-input v-model="filterData.city" placeholder="请输入协议编号" clearable></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="12">
                                <el-form-item label="创建时间：">
                                    <el-date-picker
                                        value-format="yyyy-MM-dd HH:mm:ss"
                                        v-model="filterData.dateValue"
                                        type="datetimerange"
                                        range-separator="至"
                                        start-placeholder="开始日期"
                                        end-placeholder="结束日期">
                                    </el-date-picker>
                                </el-form-item>
                            </el-col>
                        </el-row>-->
      </el-form>
      <span slot="footer">
                <el-button type="primary" @click = "confirmSearch">查询</el-button>
                <el-button @click="resetSearchConditions">清空</el-button>
                <el-button @click="queryVisible = false">取消</el-button>
            </span>
    </el-dialog>

  </div>

</template>

<script>
import '@/utils/jquery.scrollTo.min'
// eslint-disable-next-line no-unused-vars
import Pagination from '@/components/pagination/pagination'
// eslint-disable-next-line no-unused-vars
import { debounce, advanceSearch, showLoading, hideLoading, getUuid, selectDataClient, openAppClient, parseList, treeToList } from '@/utils/common'
import OrgTree from '@/components/orgTree.vue'
import { getOutUserLedger } from '@/api/platform/supplier/supplierAudit'
export default {
    components: {
        OrgTree,
        Pagination
    },
    watch: {
        'filterData.orderBy': {
            handler () {
                this.getTableData()
            }
        }
    },
    computed: {
    // 左侧树高度
        leftTreeHeight () {
            return (this.screenHeight - 21) + 'px'
        },
        // 列表宽度
        rightTableWidth () {
            return (this.screenWidth - 302) + 'px'
        },
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 292
        },
        // 分配用户、分配岗位列表宽度
        rightTableWidth2 () {
            return (this.screenWidth - 300) + 'px'
        },
        rightTableHeight2 () {
            return this.screenHeight - 210
        }
    },
    data () {
        return {
            activeName: 'first',
            tableLoading: false,
            // 状态选择查询
            selectOptionValue: null, // 选中的值
            // 表格数据
            keywords: null, // 关键字
            alertName: '商品信息',
            queryVisible: false, // 高级搜索显示
            paginationInfo: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            outPaginationInfo: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            // 表格数据
            tableData: [
                {
                    id: 1,
                    nickname: '张三',
                    name: '张三',
                    gender: '男',
                    phone: '13540441514',
                    address: '巴中市',
                    orgName: '镇广C1项目',
                }
            ],
            outTableData: [],
            // 高级搜索
            filterData: {
                nickName: null,
                name: null,
                orderBy: 2,
                phone: null
            },
            // 高和宽
            screenWidth: 0,
            screenHeight: 0,
        }
    },
    mounted () {
        // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
        this.getTableData()
    },
    // keepAlive使用了触发
    activated () {
        this.getTableData()
    },
    methods: {
        handleClick (tab, event) {
            console.log(tab, event)
            console.log(this.activeName)
            if(this.activeName == 'second') {
                this.getOutTableData()
            }else if(this.activeName == 'first') {
                this.getOutTableData()
            }
        },
        resetSearchConditions () {
            this.filterData.nickName = ''
            this.filterData.name = ''
            this.filterData.phone = ''
        },
        // 高级搜索
        confirmSearch () {
            this.keywords = ''
            if (this.activeName == 'first') {
                this.getTableData()
            } else if (this.activeName == 'second') {
                this.getOutTableData()
            }
            this.queryVisible = false
        },
        // 查询
        handleInputSearch () {
            this.resetSearchConditions()
            this.getTableData()
        },
        // 获取表格数据
        getOutTableData () {
            let params = {
                isSupplier: 2,
                page: this.outPaginationInfo.currentPage,
                limit: this.outPaginationInfo.pageSize,
            }
            if(this.keywords != null && this.keywords != '') {

                params.keywords = this.keywords
            }
            if(this.filterData.nickName != null) {
                params.nickName = this.filterData.nickName
            }
            if(this.filterData.name != null) {
                params.name = this.filterData.name
            }
            if(this.filterData.orderBy != null) {
                params.orderBy = this.filterData.orderBy
            }
            if(this.filterData.phone != null) {
                params.phone = this.filterData.phone
            }
            this.tableLoading = true
            getOutUserLedger(params).then(res => {
                this.outTableData = res.list
                this.outPaginationInfo.total = res.totalCount
                this.outPaginationInfo.pageSize = res.pageSize
                this.outPaginationInfo.currentPage = res.currPage
            }).finally(() => {
                this.tableLoading = false
            })
        },
        getScreenInfo () {
            this.screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            this.screenHeight = document.documentElement.clientHeight || document.body.clientHeight
        },
        getGenderById (id) {
            if (!id || id.length !== 18) {
                return '身份证号码格式不正确'
            }
            const genderCode = id.charAt(16) // 获取第17位数字（注意：中国大陆的身份证号码是18位）
            if (genderCode % 2 === 0) {
                return '女'
            } else {
                return '男'
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.base-page {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
}
.e-table {
  .top {
    margin: 0;
    padding: 0;
  }
}
.top-section {
  flex-shrink: 0; /* 固定高度 */
  background-color: #f9fafc;
}

.bottom-section {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.left {
  flex: 0 0 200px; /* 固定左侧宽度 */
  background-color: #fff;
  padding: 10px;
  overflow-y: auto;
}

.right {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
.e-table {
  flex: 1;
  overflow: hidden;
}

/deep/ .e-form .el-form-item .el-form-item__content {
  display: flex;
  align-items: center;
}

.detail-info {
  width: 100%;
  min-width: 670px;
  padding: 30px 20px 70px;
  background-color: #fff;
  border-radius: 14px;
  box-shadow: 1px 1px 5px rgba(136, 136, 136, 0.5);
  position: relative;
  .buttons {
    right: 30px;
    bottom: 20px;
    position: absolute;
    text-align: right;
    // bottom: 100px;
  }
}
/deep/ .el-dialog {
  .el-dialog__body {
    height: 380px;
    margin-top: 0px;
  }
}

.e-form {
  padding: 0 20px;

  .tabs-title::before {
    content: '';
    height: 22px;
    width: 8px;
    border-radius: 40px;
    background-color: #2e61d7;
    display: block;
    position: absolute;
    left: 20px;
    margin-right: 20px;
  }
}

.e-table {min-height: auto;}

.separ {
  display: inline-block;
  font-weight: bold;
  width: 4%;
  margin: 0 1% 0 1%;
  text-align: center;
}
.action {
  margin-right: 10px;
  color: #2e61d7;
  cursor: pointer;
}
/deep/.el-input--suffix .el-input__inner {
  padding-right: 5px;
}
/deep/ .el-dialog__body {
  margin-top: 0;
}
</style>
