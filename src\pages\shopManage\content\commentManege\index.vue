<template>
    <div class="base-page">
        <!-- 列表 -->
        <div class="right" v-show="viewList === true">
            <div class="e-table">
                <!-- -搜索栏----------------------------搜索栏 -->
                <div class="top">
                    <!-- 新增按钮 -->
                    <div class="left">
                        <div class="left-btn">
                            <el-select v-model="filterData.isRead" @change="getReceiveList">
                                <el-option v-for="item in isReads" :key="item.value" :label="item.label" :value="item.value">
                                </el-option>
                            </el-select>

                        </div>
                        <div style="margin-left: 10px">
                            <el-button type="primary" @click="handleDelete" class="btn-delete">批量删除</el-button>
                        </div>
                    </div>

                    <div class="search_box">
                        <el-input type="text" @keyup.enter.native="handleInputSearch" placeholder="输入搜索关键字" v-model="keywords">
                            <img src="@/assets/search.png" slot="suffix" @click="handleInputSearch" alt=""/>
                        </el-input>
                        <div class="adverse">
                            <el-button type="primary" size="small" @click="queryVisible = true">高级搜索1</el-button>
                        </div>
                    </div>
                </div>
                <!-- -搜索栏----------------------------搜索栏 -->
            </div>
            <!-- ---------------------------------表格开始--------------------------------- -->
            <div class="e-table" :style="{ width: '100%' }">
                <el-table
                    class="table" :height="rightTableHeight" :data="tableData" border highlight-current-row
                    @current-change="handleCurrentChange" @selection-change="handleSelectionChange"
                >
                    <el-table-column type="selection" width="40"></el-table-column>
                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                    <el-table-column label="操作" width="80">
                        <template slot-scope="scope">
                            <span class="action" @click="onDel(scope)"><img src="../../../../assets/btn/delete.png" alt=""></span>
                        </template>
                    </el-table-column>
                    <!-- 发件人名称 -->
                    <el-table-column label="发件人名称" width="">
                        <template slot-scope="scope">
                            <span class="action" @click="handleView(scope)">{{ scope.row.sendName }}</span>
                        </template>
                    </el-table-column>
                    <!-- 标题 -->
                    <el-table-column label="标题" width="">
                        <template slot-scope="scope">
                            <span :style="{ fontWeight: scope.row.allRead == 1 ? 'normal' : 'bold' }" @click="handleView(scope)">
                                {{ scope.row.title }}
                            </span>
                        </template>
                    </el-table-column>
                    <!--          <el-table-column label="内容" width="">
                                <template slot-scope="scope">
                                                <span @click="handleView(scope)">
                                                    {{ scope.row.content }}
                                                </span>
                                </template>
                              </el-table-column>-->
                    <el-table-column label="发送时间" width="">
                        <template slot-scope="scope">
                            <span @click="handleView(scope)">
                                {{ scope.row.sendDate }}
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column label="是否已读" width="120">
                        <template slot-scope="scope">
                            <el-tag v-if="scope.row.isRead==0">未读</el-tag>
                            <el-tag v-else-if="scope.row.isRead==1" type="success">已读</el-tag>
                        </template>
                    </el-table-column>

                </el-table>
            </div>
            <!-- 分页器 -->
            <ComPagination
                :total="pages.totalCount" :limit="20" :pageSize.sync="pages.pageSize"
                :currentPage.sync="pages.currPage"
                @currentChange="currentChange" @sizeChange="sizeChange"
            />
        </div>
        <div class="right" v-show="viewList !== true">
            <!-- ---------------------消息查看窗口--------------------- -->
            <div class="e-form" style="padding: 0 10px 10px;" v-show="viewList === 'class'">
                <div class="tabs-title">我的消息</div>
                <el-form ref="formEdit" :model="formData" label-width="150px">
                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="发件人名称：" prop="sendName">
                                <span>{{ formData.sendName }}</span>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="时间：" prop="sendDate">
                                <span>{{ formData.sendDate }}</span>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="标题：" prop="title">
                                <span>{{ formData.title }}</span>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-col>
                        <el-col :span="24" class="editorCol">
                            <el-form-item label="内容：" prop="content">
                                <div class="p20 content">
                                    <p v-html="formData.content"></p>
                                </div>
                                <!--                                <span class="receiveText" >{{ formData.content }}</span>-->

                            </el-form-item>
                        </el-col>
                    </el-col>
                </el-form>
            </div>
            <div id="filesInfo" class="e-form">
                <div class="tabs-title" id="tabs-title">附件资料</div>
                <div class="e-table" style="background-color: #fff">
                    <el-table
                        ref="tableRef"
                        border
                        style="width: 100%"
                        :data="fileList"
                        class="table"
                    >
                        <el-table-column label="序号" type="index" width="60"></el-table-column>
                        <el-table-column prop="name" label="附件名称" width=""></el-table-column>
                        <el-table-column label="操作" width="100">
                            <template slot-scope="scope">
                                <el-button type="primary" @click="handleDownload(scope.row)">下载</el-button>
                            </template>
                        </el-table-column>

                    </el-table>
                </div>
            </div>
            <div class="footer">
                <div class="right-btn">
                    <el-button v-show="formData.isRead==1" native-type="button" @click="viewList = true">返回</el-button>
                    <el-button v-show="formData.isRead==0" native-type="button" type="primary" @click="updateMessageState">确定</el-button>

                </div>
            </div>
        </div>
        <!-- ----------------查询弹框---------------- -->
        <el-dialog v-dialogDrag title="高级查询" :visible.sync="queryVisible" width="50%">
            <el-form :model="filterData" ref="form" label-width="120px" :inline="false">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="是否已读：">
                            <el-select v-model="filterData.isRead">
                                <el-option v-for="item in isReads" :key="item.value" :label="item.label" :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="发件人名称：">
                            <el-input v-model="filterData.sendName" placeholder="请输入发件人名称" clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="标题：">
                            <el-input v-model="filterData.title" placeholder="请输入标题" clearable></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="创建时间：">
                            <el-date-picker
                                value-format="yyyy-MM-dd HH:mm:ss" v-model="filterData.dateValue" type="datetimerange"
                                range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <span slot="footer">
<!--                <el-button type="primary" @click="onSave">确定</el-button>-->
                <el-button type="primary" @click="confirmSearch">查询</el-button>
                <el-button @click="resetSearchConditions">清空</el-button>
                <el-button @click="queryVisible = false">取消</el-button>
            </span>
        </el-dialog>
    </div>
</template>
<script>
import '@/utils/jquery.scrollTo.min'
import ComPagination from '@/components/pagination/pagination.vue'
import { batchDeleteR, del, getShopList, update, updateState } from '@/api/platform/mail/inbox'
import { debounce, hideLoading, showLoading, stripHtmlTags } from '@/utils/common'
import { mapActions } from 'vuex'
import { previewFile } from '@/api/platform/common/file'
import { selectFileList } from '@/api/base/file'

export default {
    components: {
        ComPagination,
        // editor
    },
    watch: {
        'filterData.orderBy': {
            handler () {
                this.getParams()
                getShopList(this.requestParams).then(res => {
                    this.tableData = res.list
                })
            }
        }
    },
    computed: {
        // 左侧树高度
        leftTreeHeight () {
            return (this.screenHeight - 21) + 'px'
        },
        // 列表宽度
        rightTableWidth () {
            return (this.screenWidth - 302) + 'px'
        },
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 291
        },
    },
    data () {
        return {
            fileList: [],
            alertName: '消息',
            queryVisible: false,
            action: '编辑',
            viewList: true, // 显示列表还是编辑界面: true 显示列表 false 显示编辑界面
            keywords: '',
            currentRow: null,
            changedRow: [],
            selectedRows: [],
            pages: {
                currPage: 1,
                pageSize: 20,
            },
            // 高级查询数据对象
            filterData: {
                isRead: null,
                sendName: '',
                title: '',
                dateValue: [], // 开始时间和结束时间
                orderBy: 2
            },
            tableData: [],
            formData: {
                content: '',
            },
            isReads: [
                {
                    value: null,
                    label: '全部'
                },
                {
                    value: 0,
                    label: '未读'
                },
                {
                    value: 1,
                    label: '已读'
                }],
            mapObj: null,
            screenWidth: 0,
            screenHeight: 0,
            arr: [],
            requestParams: {}
        }
    },
    mounted () {
        // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
        // 获取计量单位
        this.setUnitMeasur()
    },
    created () {
        this.getReceiveList()
        this.getParams()
    },
    methods: {
        stripHtmlTags,
        async handleDownload (file) {
            let fileRes = await previewFile({ recordId: file.fileFarId })
            const blob = new Blob([fileRes])
            const url = window.URL.createObjectURL(blob)
            let a = document.createElement('a')
            a.href = url
            a.download = file.name
            a.click()
            window.URL.revokeObjectURL(url)
        },
        resetSearchConditions () {
            this.filterData.dateValue = [] // 开始时间和结束时间
            this.filterData.title = ''// 选中的标题
            this.filterData.isRead = null
            this.filterData.sendName = ''
        },
        handleInputSearch () {
            this.resetSearchConditions()
            this.getReceiveList()
        },
        confirmSearch () {
            this.keywords = ''
            this.getReceiveList()
            this.queryVisible = false
        },
        stateOptionsClick (value) {
            this.filterData.isRead = value
        },
        updateMessageState () {
            updateState({ id: this.formData.stationMessageReceiveId }).then(res => {
                if (res.code == 200) {
                    this.viewList = true
                    this.$message({
                        message: '阅读成功',
                        type: 'success'
                    })
                    this.getReceiveList()
                }
                this.viewList = true
            })
        },
        getReceiveList () {
            let params = {
                limit: this.pages.pageSize,
                page: this.pages.currPage,
                orderBy: this.filterData.orderBy,
                receiveType: 0, //用户类型 0 店铺  1 用户
            }
            if (this.keywords != null) {
                params.keywords = this.keywords
            }
            if (this.filterData.isRead != null) {
                params.isRead = this.filterData.isRead
            }
            if (this.filterData.title != null) {
                params.title = this.filterData.title
            }
            if (this.keywords != null && this.keywords != '') {
                params.title = this.filterData.title
            }
            if (this.filterData.sendName != null) {
                params.userName = this.filterData.sendName
            }
            if (this.filterData.dateValue != null) {
                params.startDate = this.filterData.dateValue[0]
                params.endDate = this.filterData.dateValue[1]
            }
            getShopList(params).then(res => {
                this.pages = res
                this.tableData = res.list
            })
        },
        // 获取页面参数
        getParams () {
            this.requestParams = {
                keywords: this.keywords,
                limit: this.pages.pageSize,
                ...this.filterData
            }
            this.requestParams.page = this.pages.currPage
        },
        // 删除
        onDel (scope) {
            this.clientPop('info', `您确定要删除该${scope.row.title}吗？`, async () => {
                showLoading()
                // eslint-disable-next-line no-undef
                del({ id: scope.row.stationMessageReceiveId }).then(res => {
                    if (res.message === '操作成功') {
                        this.$message({
                            message: '删除成功',
                            type: 'error'
                        })
                        this.getReceiveList()
                    }
                    hideLoading()
                })
                hideLoading()
            })
        },
        // 批量删除
        handleDelete () {
            if (!this.selectedRows[0]) {
                return this.clientPop('warn', '请选择要删除的信息', () => {
                })
            }
            this.clientPop('info', '您确定要删除选中信息吗？', async () => {
                showLoading()
                let arr = this.selectedRows.map(item => {
                    return item.stationMessageReceiveId
                })
                batchDeleteR(arr).then(res => {
                    if (res.message === '操作成功') {
                        this.clientPop('suc', '删除成功', () => {
                            this.getReceiveList()
                        })
                    } else {
                        this.clientPop('suc', '删除失败请稍后再试', () => {
                        })
                    }
                })
                hideLoading()
            })
            hideLoading()
        },
        // 表格勾选
        handleSelectionChange (selection) {
            this.selectedRows = selection
        },
        // 分页函数
        currentChange () {
            if (this.pages.currPage === undefined) {
                this.pages.currPage = this.pages.totalPage
            }
            this.getTableData()
        },
        sizeChange () {
            this.getTableData()
        },
        ...mapActions('equip', ['setUnitMeasur']),
        handleClose () {
        },
        handleView (scope) {
            update({ stationMessageId: scope.row.stationMessageId, allRead: 1 })
            this.viewList = 'class'
            this.formData = scope.row
            this.action = '编辑'
            this.getFileInfos(this.formData.stationMessageId)

        },
        getFileInfos (relevanceId) {
            let params = {
                relevanceId: relevanceId,
                relevanceType: 7,
            }
            selectFileList(params).then(res => {
                this.fileList = res.list
            })
        },
        // 获取列表数据
        async getTableData () {
            this.getParams()
            getShopList(this.requestParams).then(res => {
                if (res.list) {
                    this.tableData = res.list
                } else {
                    this.clientPop('warn', res.message, () => {
                    })
                }
                this.pages = res
            })

            this.viewList = true
        },
        cellClsNm ({ column }) {
            if (column.label === '') {
                return 'fixed-left'
            }
        },
        // 关键词搜索
        handleCurrentChange (val) {
            this.currentRow = val
        },
        //查看消息的接口
        onSave () {
            this.getParams()
            getShopList(this.requestParams).then(res => {
                this.tableData = res.list
                this.viewList = true
            })
        },
        // 置空表单
        emptyForm () {
            for (let key in this.formData) {
                this.formData[key] = null
            }
        },
        // 获取屏幕大小
        getScreenInfo () {
            let screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            let screenHeight = document.documentElement.clientHeight || document.body.clientHeight
            this.screenWidth = screenWidth
            this.screenHeight = screenHeight
        }
    }
}
</script>

<style lang="scss" scoped>
.base-page .left {
    min-width: 180px;
    padding: 0;
}

.base-page {
    width: 100%;
    height: 100%;
}

/deep/ .e-form .el-form-item .el-form-item__content {
    display: flex;
    align-items: center;
}

.detail-info {
    width: 100%;
    // min-width: 670px;
    padding: 30px 20px 70px;
    background-color: #fff;
    border-radius: 14px;
    box-shadow: 1px 1px 5px rgba(136, 136, 136, 0.5);
    position: relative;

    .buttons {
        right: 30px;
        bottom: 20px;
        position: absolute;
        text-align: right;
    }
}

.e-table {
    min-height: auto;
}

.separ {
    display: inline-block;
    font-weight: bold;
    width: 4%;
    margin: 0 1% 0 1%;
    text-align: center;
}

/deep/ .el-input--suffix .el-input__inner {
    padding-right: 5px;
    border-radius: 5px;
}

.action {
    margin-right: 10px;
    color: #2e61d7;
    cursor: pointer;
}

/deep/ .el-table td.el-table__cell {
    // height: 150px !important; // 单独修改表格行高度
}

/deep/ .el-col.editorCol {
    .el-form-item__content {
        height: unset !important;

        .content {
            width: 100%;
            height: 300px;
            overflow: auto;
            border: 1px solid lightgray
        }
    }
}

.receiveText {

    font-size: 15px;
    width: 1115px;
    height: 304px;
    border: solid 1px #cbc3c3;
    margin-right: 30px;
    padding: 20px
}
</style>
