<template>
    <div>
        <div class="right" v-loading="showLoading">
            <div class="e-table" :style="{ width: '100%' }">
                <div class="top">
                    <div class="left">
                        <div class="left-btn" style=" display: flex; justify-content: space-between">
                            <el-button style="margin-top:10px" type="primary" @click="outputAll" class="">数据导出</el-button>
<!--                          <el-button  style="margin-top:10px" type="primary" v-if="this.userInfo.shopId==this.businessShopID&&!changeShopView" @click="changShopIdM"   class="btn-greenYellow">查看物资分公司数据</el-button>
                          <el-button style="margin-top:10px" type="primary" v-if=this.changeShopView @click="returnShop()"  class="btn-greenYellow">返回子公司</el-button>-->
                            <div style="height: 50px; line-height: 50px;margin-left: 10px">
                                <el-date-picker
                                    size="small"
                                    :default-time="['00:00:00', '23:59:59']"
                                    @change="getProductFromListM"
                                    value-format="yyyy-MM-dd HH:mm:ss"
                                    v-model="filterData.dateValue"
                                    type="datetimerange"
                                    range-separator="至"
                                    :picker-options="pickerOptions"

                                    start-placeholder="开始日期"
                                    end-placeholder="结束日期"
                                    style="margin-right: 10px; width: 400px ;"
                                >
                                </el-date-picker>
                            </div>
                        </div>
                        <div v-if="tableData && tableData.length > 0" class="ml10">含税：<span style="color: red">{{ tableData[0].countAmount }}</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;不含税：<span style="color: red">{{ tableData[0].countNoRateAmount }}</span>
                        </div>
                        <div v-else class="ml10">含税：<span style="color: red">0</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;不含税：<span style="color: red">0</span></div>
                    </div>
                    <div class="search_box">
                        <el-input clearable style="width: 300px" @blur="handleInputSearch" placeholder="输入搜索关键字" v-model="keywords">
                            <img src="@/assets/search.png" slot="suffix" @click="handleInputSearch" alt=""/>
                        </el-input>
                        <div class="adverse">
                            <el-button type="primary" size="small" @click="queryVisible = true">高级搜索</el-button>
                        </div>
                    </div>
                </div>
                <el-table
                    class="table" v-loading="tableLoading" ref="mainTable2" @row-click="handleCurrentInventoryClick2" :height="rightTableHeight" @selection-change="selectionChangeHandle" :data="tableData" border
                >
                    <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
                    <el-table-column label="序号" type="index" width="60" :index="indexMethod"></el-table-column>
                    <el-table-column label="订单编号" prop="orderSn" width="300"></el-table-column>
                    <el-table-column label="供应商" width="200" prop="enterpriseName"></el-table-column>
                    <el-table-column label="客户" width="200" prop="productName"></el-table-column>
                    <el-table-column label="物资名称" width="200" prop="productName"></el-table-column>
                    <el-table-column label="规格型号" width="200" prop="spec"></el-table-column>
                    <el-table-column label="计量单位" width="200" prop="unit"></el-table-column>
                    <el-table-column label="交易数量" width="140" prop="buyCounts">
                      <template slot="header">
                        <span>交易数量&nbsp;</span>
                        <el-tooltip
                          effect="light"
                          content="负数为退货数量，正数为发货数量"
                          placement="top"
                        >
                          <img src="@/assets/images/ico_question.png" alt="" style="width: 15px;height: 15px">
                        </el-tooltip>
                      </template>
                    </el-table-column>
                    <el-table-column label="含税交易金额" width="140" prop="totalAmount"></el-table-column>
                    <el-table-column label="不含税交易金额" width="140" prop=""></el-table-column>
                    <el-table-column label="交易完成时间" width="240" prop="successDateStr"></el-table-column>
                    <!--<el-table-column label="上架时间" width="240" prop="putawayDate"/>-->
                </el-table>
            </div>
            <!--            分页-->
            <Pagination
                :total="paginationInfo.total"
                :pageSize.sync="paginationInfo.pageSize"
                :currentPage.sync="paginationInfo.currentPage"
                @currentChange="getProductFromListM"
                @sizeChange="getProductFromListM"
            />
        </div>
        <el-dialog title="高级查询" :visible.sync="queryVisible" width="50%">
            <el-form :model="filterData" ref="form" label-width="120px" :inline="false">
                <el-row>
                    <el-col :span="12">
                        <el-form-item width="150px" label="订单编号：" prop="orderSn">
                            <el-input v-model="filterData.orderSn" placeholder="请输入订单编号" clearable></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item width="150px" label="客户：" prop="enterpriseName">
                            <el-input v-model="filterData.enterpriseName" placeholder="请输入客户" clearable></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item width="150px" label="物资名称：" prop="productName">
                            <el-input v-model="filterData.productName" placeholder="请输入物资名称" clearable></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
<!--                <el-row>
                    <el-col :span="12">
                        <el-form-item width="150px" label="商品编码：" prop="shopName">
                            <el-input v-model="filterData.serialNum" placeholder="请输入商品编码" clearable></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item width="150px" label="销售价格以上：">
                            <el-input type="number" v-model="filterData.staSellPrice" placeholder="请输入价格区间" style="width: 200px"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="销售价格以下：">
                            <el-input type="number" v-model="filterData.endSellPrice" placeholder="请输入价格区间" style="width: 200px"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>-->
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="交易完成时间：">
                            <el-date-picker
                                value-format="yyyy-MM-dd HH:mm:ss" v-model="filterData.dateValue"
                                type="datetimerange"
                                range-separator="至" start-placeholder="开始日期"
                                end-placeholder="结束日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <span slot="footer">
                <el-button type="primary" @click="confirmSearch">查询</el-button>
                <el-button @click="resetSearchConditions">清空</el-button>
                <el-button @click="queryVisible = false">取消</el-button>
            </span>
        </el-dialog>
    </div>
</template>
<script>
//局部引用
import Pagination from '@/components/pagination/pagination.vue'
import { getShopManageMaterial, supplierOutputExcel } from '@/api/platform/product/productCount'
import { mapState } from 'vuex'
import { debounce } from '@/utils/common'

const echarts = require('echarts')
export default {
    components: {
        Pagination
    },
    computed: {
        ...mapState(['userInfo']),
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 292
        },
    },
    data () {
        return {
            currentQuery: {},
            changeShopView: false,
            shopId: '',
            queryVisible: false,
            keywords: null,
            showLoading: false,
            pickerOptions: {
                shortcuts: [{
                    text: '最近一个月',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
                        start.setHours('00', '00', '00')
                        end.setHours('23', '59', '59')
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '最近三个月',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
                        start.setHours('00', '00', '00')
                        end.setHours('23', '59', '59')
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '最近半年',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 180)
                        start.setHours('00', '00', '00')
                        end.setHours('23', '59', '59')
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '最近一年',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 360)
                        start.setHours('00', '00', '00')
                        end.setHours('23', '59', '59')
                        picker.$emit('pick', [start, end])
                    }
                },
                    // {
                    //     text: '最近二年',
                    //     onClick (picker) {
                    //         const end = new Date()
                    //         const start = new Date()
                    //         start.setTime(start.getTime() - 3600 * 1000 * 24 * 360 * 2)
                    //         start.setHours('00', '00', '00')
                    //         end.setHours('23', '59', '59')
                    //         picker.$emit('pick', [start, end])
                    //     }
                    // }, {
                    //     text: '全部',
                    //     onClick (picker) {
                    //         picker.$emit('pick', [])
                    //     }
                    // }
                ]
            },
            tableData: [],
            dataListSelections: [],
            paginationInfo: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            tableLoading: false,
            filterData: {
                dateValue: [],
                serialNum: '',
                staSellPrice: null,
                endSellPrice: null,
                shopName: '',
                enterpriseName: '',
                productName: '',
                orderBy: 0,
                orderSn: ''
            },
            screenWidth: 0,
            screenHeight: 0,
        }
    },
    watch: {
        'filterData.orderBy': {
            handler () {
                this.getProductFromListM()
            }
        }
    },
    created () {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
        start.setHours('00', '00', '00')
        this.filterData.dateScope = [this.dateStrM(start), this.dateStrM(end)]
        this.getDefaultDateRange()
    },
    methods: {

        changShopIdM () {
            this.changeShopView = true
            // 物资分公司店铺名称
            this.shopId = '1645601878095495170'
            this.getProductFromListM()
        },
        returnShop () {
            this.changeShopView = false
            this.shopId = null
            this.getProductFromListM()
        },
        indexMethod (index) {
            return this.paginationInfo.pageSize * (this.paginationInfo.currentPage - 1) + index + 1
        },
        // 日期处理
        padZero (num) {
            return num < 10 ? `0${num}` : num
        },
        dateStrM (date) {
            const year = date.getFullYear()
            const month = this.padZero(date.getMonth() + 1)
            const day = this.padZero(date.getDate())
            const hour = this.padZero(date.getHours())
            const minute = this.padZero(date.getMinutes())
            const second = this.padZero(date.getSeconds())
            return `${year}-${month}-${day} ${hour}:${minute}:${second}`
        },
        handleCurrentInventoryClick2 (row) {
            row.flag = !row.flag
            this.$refs.mainTable2.toggleRowSelection(row, row.flag)
        },
        selectionChangeHandle (val) {
            this.dataListSelections = val
            console.log(this.dataListSelections)
        },
        getScreenInfo () {
            let screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            let screenHeight = document.documentElement.clientHeight || document.body.clientHeight
            this.screenWidth = screenWidth
            this.screenHeight = screenHeight
        },
        outputAll () {
            if (this.tableData.length == 0) {
                return this.$message.error('数据为空！')
            }
            if (this.dataListSelections.length != 0) {
                let ids = this.dataListSelections.map(item => {
                    return item.orderItemId
                })
                this.currentQuery.ids = ids
                if (this.shopId != null && this.shopId != '') {
                    this.currentQuery.shopId = this.shopId
                }
                this.tableLoading = true
                console.log(this.dataListSelections)
                supplierOutputExcel(this.currentQuery).then(res => {
                    const blob = new Blob([res], { type: 'application/vnd.ms-excel' }) // 2.获取请求返回的response对象中的blob 设置文件类型，这里以excel为例
                    const url = window.URL.createObjectURL(blob) // 3.创建一个临时的url指向blob对象
                    const a = document.createElement('a') // 4.创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
                    a.href = url
                    a.download = '商品交易量报表.xlsx'
                    a.click()
                    window.URL.revokeObjectURL(url) // 5.释放这个临时的对象url
                    this.currentQuery.ids = []
                    this.dataListSelections = []
                    this.$message.success('操作成功')
                    this.tableLoading = false
                }).catch(() => {
                    this.tableLoading = false
                })
            } else {
                this.tableLoading = true
                supplierOutputExcel(this.currentQuery).then(res => {
                    const blob = new Blob([res], { type: 'application/vnd.ms-excel' }) // 2.获取请求返回的response对象中的blob 设置文件类型，这里以excel为例
                    const url = window.URL.createObjectURL(blob) // 3.创建一个临时的url指向blob对象
                    const a = document.createElement('a') // 4.创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
                    a.href = url
                    a.download = '平台商品信息报表.xlsx'
                    a.click()
                    window.URL.revokeObjectURL(url) // 5.释放这个临时的对象url
                    this.$message.success('操作成功')
                    this.tableLoading = false
                }).catch(() => {
                    this.tableLoading = false
                })
            }
        },

        handleInputSearch () {
            this.resetSearchConditions()
            this.getProductFromListM()
        },

        // 高级查询
        confirmSearch () {
            this.keywords = ''
            this.getProductFromListM()
            this.queryVisible = false
        },
        //高级查询返回
        resetSearchConditions () {
            this.filterData = {
                serialNum: '',
                staSellPrice: null,
                endSellPrice: null,
                productName: '',
            }

        },
        getDefaultDateRange () {
            const endDate = new Date()
            const startDate = new Date()
            startDate.setMonth(startDate.getMonth() - 3)
            this.filterData.dateValue = [startDate, endDate]
        },
        getProductFromListM () {
            let params = {
                page: this.paginationInfo.currentPage,
                limit: this.paginationInfo.pageSize,
            }
            if (this.shopId != null && this.shopId != '') {
                params.shopId = this.shopId
            }
            if (this.filterData.dateValue != null) {
                params.staPutawayDate = this.filterData.dateValue[0]
                params.endPutawayDate = this.filterData.dateValue[1]
            }
            if (this.filterData.productName != null) {
                params.productName = this.filterData.productName
            }
            if (this.keywords != null) {
                params.keywords = this.keywords
            }
            this.currentQuery = params
            getShopManageMaterial(params).then(res => {
                this.paginationInfo.total = res.totalCount
                this.paginationInfo.pageSize = res.pageSize
                this.paginationInfo.currentPage = res.currPage
                this.tableData = res.list
                this.tableLoading = false
            }).catch(() => {
                this.tableLoading = false
            })
        },
        dateChange () {
            this.getProductFromListM()
        },
        initCharts () {
            // 基于准备好的dom，初始化echarts实例
            let myChart = echarts.init(this.$refs.chart)
            // 绘制图表
            myChart.setOption({
                itemStyle: {
                    // 高亮时点的颜色
                    color: '#74A0F9'
                },
                tooltip: {},
                xAxis: {
                    data: this.labelTitle
                },
                yAxis: {},
                series: [{
                    name: '数量',
                    type: 'bar',
                    data: this.count
                }]
            })
        },
    },
    //一加载页面就调用
    mounted () {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
        start.setHours('00', '00', '00')
        this.filterData.dateScope = [this.dateStrM(start), this.dateStrM(end)]
        this.getProductFromListM()
        // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
    }
}
</script>
<style lang="scss" scoped>
/deep/ .e-form .el-form-item .el-form-item__content {
    display: flex;
    align-items: center;
}

.detail-info {
    width: 100%;
    min-width: 670px;
    padding: 30px 20px 70px;
    background-color: #fff;
    border-radius: 14px;
    box-shadow: 1px 1px 5px rgba(136, 136, 136, 0.5);
    position: relative;

    .buttons {
        right: 30px;
        bottom: 20px;
        position: absolute;
        text-align: right;
        // bottom: 100px;
    }
}

/deep/ .el-dialog {
    .el-dialog__body {
        height: 380px;
        margin-top: 0px;
    }
}

</style>