<template>
  <div class="base-page">
    <!-- 列表 -->
    <div class="right">
      <div class="e-table">
        <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane label="待审核" name="first">
            <div class="search_box1">
              <el-radio v-model="filterData.orderBy" :label="1"
                >按创建时间排序</el-radio
              >
              <el-radio v-model="filterData.orderBy" :label="2"
                >按发布时间</el-radio
              >
              <el-radio v-model="filterData.orderBy" :label="3"
                >按截止时间</el-radio
              >
              <el-input
                clearable
                style="width: 300px"
                type="text"
                @blur="handleInputSearch"
                placeholder="输入搜索关键字"
                v-model="keywords"
              >
                <img
                  src="@/assets/search.png"
                  slot="suffix"
                  @click="handleInputSearch"
                  alt=""
                />
              </el-input>
              <div class="adverse">
                <el-button
                  type="primary"
                  size="small"
                  @click="queryVisible = true"
                  >高级搜索</el-button
                >
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="发布竞价列表" name="second">
            <div class="top">
              <div class="left">
                <div class="left-btn">
                  <el-button type="primary" class="" @click="createBidingClickM"
                    >生成订单商品竞价</el-button
                  >
                  <el-button
                    type="primary"
                    class=""
                    @click="createInventoryBiding"
                    >生成清单商品竞价</el-button
                  >
                  <!--                            <el-button type="primary" v-if="showDevFunc" class="" @click="createBidingClickM2">生成物资基础库竞价</el-button>-->
                  <el-button
                    v-has-permi="{
                      platform: 'oneselfShopManage',
                      auth: 'submit-bid',
                    }"
                    type="primary"
                    class=""
                    @click="submitBidingAudit"
                    >提交审核</el-button
                  >
                </div>
              </div>
              <div class="search_box">
                <el-radio v-model="filterData.orderBy" :label="1"
                  >按创建时间排序</el-radio
                >
                <el-radio v-model="filterData.orderBy" :label="2"
                  >按发布时间</el-radio
                >
                <el-radio v-model="filterData.orderBy" :label="3"
                  >按截止时间</el-radio
                >
                <el-input
                  clearable
                  style="width: 300px"
                  type="text"
                  @blur="handleInputSearch"
                  placeholder="输入搜索关键字"
                  v-model="keywords"
                >
                  <img
                    src="@/assets/search.png"
                    slot="suffix"
                    @click="handleInputSearch"
                    alt=""
                  />
                </el-input>
                <div class="adverse">
                  <el-button
                    type="primary"
                    size="small"
                    @click="queryVisible = true"
                    >高级搜索</el-button
                  >
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
      <!--表格-->
      <div class="e-table">
        <el-table
          class="table"
          ref="tableRef"
          :height="rightTableHeight"
          @selection-change="tableSelectM"
          @row-click="tableRowClickM"
          v-loading="tableLoading"
          :data="tableData"
          border
        >
          <el-table-column type="selection" width="40"></el-table-column>
          <el-table-column
            label="序号"
            type="index"
            width="60"
          ></el-table-column>
          <el-table-column label="竞价编号" width="300" prop="biddingSn">
            <template v-slot="scope">
              <span class="action" @click="handleView(scope.row)">{{
                scope.row.biddingSn
              }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="标题"
            width="200"
            prop="title"
          ></el-table-column>
          <el-table-column label="竞价采购类型" width="90" prop="type">
            <template v-slot="scope">
              <span v-if="scope.row.type == 1"
                ><el-tag type="success">公开竞价</el-tag></span
              >
              <span v-if="scope.row.type == 2"> <el-tag>邀请竞价</el-tag></span>
            </template>
          </el-table-column>
          <el-table-column label="截止时间" width="160" prop="endTime">
            <template v-slot="scope">
              <span>{{ scope.row.endTime | dateStr }}</span>
            </template>
          </el-table-column>
          <el-table-column label="商品类型" prop="productType" width="140">
            <template v-slot="scope">
              <el-tag v-if="scope.row.productType == 0">低值易耗品</el-tag>
              <el-tag v-if="scope.row.productType == 1">大宗临购订单</el-tag>
              <el-tag v-if="scope.row.productType == 2">大宗临购清单</el-tag>
            </template>
          </el-table-column>
          <el-table-column
            label="生成来源"
            prop="biddingSourceType"
            width="100"
          >
            <template v-slot="scope">
              <el-tag v-if="scope.row.biddingSourceType == 1">订单</el-tag>
              <el-tag v-if="scope.row.biddingSourceType == 2"
                >物资基础库</el-tag
              >
              <el-tag v-if="scope.row.biddingSourceType == 3">清单</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="状态" prop="state">
            <template v-slot="scope">
              <el-tag type="info" v-if="scope.row.state == 0">待提交</el-tag>
              <el-tag v-if="scope.row.state == 1">待审核</el-tag>
              <el-tag type="danger" v-if="scope.row.state == 2"
                >审核失败</el-tag
              >
              <el-tag type="success" v-if="scope.row.state == 5"
                >提交审核通过</el-tag
              >
              <el-tag type="" v-if="scope.row.state == 6">中标待审核</el-tag>
              <el-tag type="success" v-if="scope.row.state == 7">已中标</el-tag>
              <el-tag type="danger" v-if="scope.row.state == 9">已流标</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="时间状态" width="100" prop="biddingState">
            <template v-slot="scope">
              <el-tag type="info" v-if="scope.row.biddingState == 1"
                >未开始</el-tag
              >
              <el-tag type="success" v-if="scope.row.biddingState == 2"
                >进行中</el-tag
              >
              <el-tag type="danger" v-if="scope.row.biddingState == 3"
                >已结束</el-tag
              >
            </template>
          </el-table-column>
          <el-table-column label="联系人名称" width="" prop="linkName" />
          <el-table-column label="联系电话" width="150" prop="linkPhone" />
          <el-table-column label="发布时间" width="160" prop="startTime" />
          <el-table-column label="创建时间" width="160" prop="gmtCreate" />
          <el-table-column
            v-if="activeName == 'second'"
            label="操作"
            fixed="right"
            width="260"
          >
            <template slot-scope="scope">
              <el-popconfirm
                v-if="scope.row.state == 5"
                confirm-button-text="确认"
                cancel-button-text="取消"
                icon="el-icon-info"
                icon-color="red"
                @confirm="bidOpening(scope.row)"
                style="margin-right: 5px"
                title="确定要开标吗？"
              >
                <el-button size="mini" type="text" slot="reference"
                  >开标</el-button
                >
              </el-popconfirm>

              <el-button
                size="mini"
                type="text"
                v-if="
                  scope.row.state == 0 ||
                  scope.row.state == 1 ||
                  scope.row.state == 2 ||
                  scope.row.state == 5
                "
                @click="deadlineM(scope.row)"
                :disabled="scope.row.status === 0"
                >延长截至时间</el-button
              >
              <el-button size="mini" type="text" @click="handleView(scope.row)"
                >详情</el-button
              >
              <el-popconfirm
                v-if="
                  scope.row.state == 0 ||
                  scope.row.state == 1 ||
                  scope.row.state == 2 ||
                  scope.row.state == 5
                "
                confirm-button-text="确认"
                cancel-button-text="取消"
                icon="el-icon-info"
                icon-color="red"
                @click="handleDelete(scope.row)"
                style="margin-right: 10px"
                title="确定要删除吗？"
              >
                <el-button
                  size="mini"
                  type="text"
                  :style="
                    scope.row.state == 0 ||
                    scope.row.state == 1 ||
                    scope.row.state == 2 ||
                    scope.row.state == 5
                      ? 'color:#f56c6c'
                      : 'color:#F9A6A6'
                  "
                  slot="reference"
                  >删除</el-button
                >
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!--分页-->
      <Pagination
        v-show="tableData && tableData.length > 0"
        :total="paginationInfo.total"
        :pageSize.sync="paginationInfo.pageSize"
        :currentPage.sync="paginationInfo.currentPage"
        @currentChange="getTableData"
        @sizeChange="getTableData"
      />
    </div>
    <!--高级查询-->
    <el-dialog title="高级查询" :visible.sync="queryVisible" width="50%">
      <el-form
        :model="filterData"
        ref="form"
        label-width="120px"
        :inline="false"
      >
        <el-row>
          <el-col :span="12">
            <el-form-item label="商品类型：">
              <el-select
                v-model="filterData.productType"
                placeholder="请选择商品类型"
              >
                <el-option
                  v-for="item in filterData.productTypeSelect"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="状态：">
              <el-select v-model="filterData.state" placeholder="请选择状态">
                <el-option
                  v-for="item in filterData.stateSelect"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="单据时间状态：">
              <el-select
                v-model="filterData.biddingState"
                placeholder="请选择订单类型"
              >
                <el-option
                  v-for="item in filterData.biddingStateSelect"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="竞价类型：">
              <el-select v-model="filterData.type" placeholder="请选择竞价类型">
                <el-option
                  v-for="item in filterData.typeSelect"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <!--                <el-row>-->
        <!--                    <el-col :span="12">-->
        <!--                        <el-form-item label="公示状态：">-->
        <!--                            <el-select v-model="filterData.publicityState" placeholder="请选择状态">-->
        <!--                                <el-option-->
        <!--                                    v-for="item in filterData.publicityStateSelect"-->
        <!--                                    :key="item.value"-->
        <!--                                    :label="item.label"-->
        <!--                                    :value="item.value">-->
        <!--                                </el-option>-->
        <!--                            </el-select>-->
        <!--                        </el-form-item>-->
        <!--                    </el-col>-->
        <!--                </el-row>-->
        <el-row>
          <el-col :span="12">
            <el-form-item label="竞价编号：">
              <el-input
                clearable
                maxlength="100"
                placeholder="请输入竞价编号"
                v-model="filterData.biddingSn"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <!--                <el-row>-->
        <!--                    <el-col :span="12">-->
        <!--                        <el-form-item label="订单编号：" >-->
        <!--                            <el-input clearable maxlength="100" placeholder="请输入订单编号" v-model="filterData.orderSn"></el-input>-->
        <!--                        </el-form-item>-->
        <!--                    </el-col>-->
        <!--                </el-row>-->
        <el-row>
          <el-col :span="12">
            <el-form-item label="标题：">
              <el-input
                clearable
                maxlength="100"
                placeholder="请输入标题"
                v-model="filterData.title"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="创建时间：">
              <el-date-picker
                value-format="yyyy-MM-dd HH:mm:ss"
                v-model="filterData.createDate"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="竞价截止时间：">
              <el-date-picker
                value-format="yyyy-MM-dd HH:mm:ss"
                v-model="filterData.endDate"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-radio v-model="filterData.orderBy" :label="1"
              >按创建时间排序</el-radio
            >
            <el-radio v-model="filterData.orderBy" :label="2"
              >按发布时间</el-radio
            >
            <el-radio v-model="filterData.orderBy" :label="3"
              >按截止时间</el-radio
            >
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer">
        <el-button type="primary" @click="confirmSearch">查询</el-button>
        <el-button @click="resetSearchConditions">清空</el-button>
        <el-button @click="queryVisible = false">取消</el-button>
      </span>
    </el-dialog>
    <el-dialog
      v-loading="bidingFormLoading"
      v-dialogDrag
      id="bidingDialog"
      title="生成订单竞价"
      :visible.sync="showBidingForm"
      width="90%"
      style="margin-left: 10%"
      :close-on-click-modal="false"
    >
      <el-divider content-position="left">竞价信息</el-divider>
      <el-form
        :model="bidingForm"
        :rules="bidingFormRules"
        label-width="200px"
        ref="bidingFormRef"
        :disabled="false"
        class="demo-ruleForm"
      >
        <el-row>
          <el-col :span="12">
            <el-form-item label="商品类型：" prop="productType">
              <!--                            <el-radio-group v-model="bidingForm.productType" @change="agreeChange">-->
              <!--                                <el-radio label="1" border>零星采购</el-radio>-->
              <!--                                <el-radio label="2" border>大宗临购</el-radio>-->
              <!--                            </el-radio-group>-->
              <el-radio
                v-model="bidingForm.productType"
                v-has-permi="{
                  platform: 'oneselfShopManage',
                  auth: 'created-lxBid',
                }"
                @change="agreeChange"
                :label="0"
                >零星采购</el-radio
              >
              <el-radio
                v-model="bidingForm.productType"
                v-has-permi="{
                  platform: 'oneselfShopManage',
                  auth: 'created-lcBid',
                }"
                @change="agreeChange"
                :label="1"
                >大宗临购</el-radio
              >
              <el-radio
                v-model="bidingForm.productType"
                v-has-permi="{
                  platform: 'oneselfShopManage',
                  auth: 'created-lcBid',
                }"
                @change="agreeChange"
                :label="2"
                >周转材料</el-radio
              >
            </el-form-item>
          </el-col>
          <el-col :span="12" v-show="bidingForm.productType === 1 || bidingForm.productType === 2">
            <el-form-item label="价格类型：" prop="billType">
              <el-radio
                v-model="bidingForm.billType"
                @change="agreeChange"
                :label="1"
                >浮动价格</el-radio
              >
              <el-radio
                v-model="bidingForm.billType"
                @change="agreeChange"
                :label="2"
                >固定价格</el-radio
              >
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="标题：" prop="title">
              <el-input
                maxlength="200"
                placeholder="请输入标题"
                clearable
                v-model="bidingForm.title"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="截止时间：" prop="endTime">
              <!--                            <el-date-picker-->
              <!--                                value-format="yyyy-MM-dd HH:mm:ss"-->
              <!--                                v-model="bidingForm.endTime"-->
              <!--                                align="right"-->
              <!--                                type="date"-->
              <!--                                placeholder="选择日期"-->
              <!--                                :picker-options="pickerOptions"-->
              <!--                            >-->
              <!--                            </el-date-picker>-->
              <el-date-picker
                v-model="bidingForm.endTime"
                type="datetime"
                placeholder="选择日期时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                :picker-options="pickerOptions"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系人：" prop="linkName">
              <el-input
                maxlength="10"
                placeholder="请输入联系人"
                clearable
                v-model="bidingForm.linkName"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系电话：" prop="linkPhone">
              <el-input
                type="number"
                clearable
                v-model="bidingForm.linkPhone"
                placeholder="请输入11位手机号码"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="竞价类型：" prop="type">
              <el-radio-group v-model="bidingForm.type">
                <el-radio :label="1">公开竞价</el-radio>
                <span> <el-radio :label="2">邀请竞价</el-radio></span>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="bidingForm.type === 2">
            <el-form-item label="选择供应商：" prop="">
              <el-button type="primary" @click="showSupplierDialog"
                >选择供应商</el-button
              >
              <!--                            <el-select style="width: 80%" name="name" no-data-text="请选择供应商" v-model="bidingForm.suppliers"  multiple collapse-tags  placeholder="请选择">-->
              <!--                                <el-option-->
              <!--                                    v-for="item in bidingForm.suppliers"-->
              <!--                                    :key="item.enterpriseId"-->
              <!--                                    :label="item.enterpriseName"-->
              <!--                                    :value="item.enterpriseId"-->
              <!--                                    :disabled="item.disabled">-->
              <!--                                </el-option>-->
              <!--                            </el-select>-->
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="说明：" prop="biddingExplain">
              <editor v-model="bidingForm.biddingExplain"></editor>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row style="margin-top: 10px">
          <el-col :span="24">
            <el-form-item label="竞价函说明：" prop="biddingNotice">
              <el-input
                type="textarea"
                :rows="6"
                v-model="bidingForm.biddingNotice"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-divider content-position="left">订单商品</el-divider>
      <div
        class="e-table"
        style="background-color: #fff"
        v-loading="biddingOrderItemLoading"
      >
        <div class="top" style="height: 50px; padding-left: 10px">
          <div class="left">
            <div class="search_box" style="margin-left: 10px">
              <el-button type="primary" class="" @click="selectProductDia"
                >选择订单商品</el-button
              >
            </div>
          </div>
        </div>
        <el-table
          ref="bidingOrderItemRef"
          border
          style="width: 100%"
          :data="bidingFormOrderItems"
          class="table"
          :max-height="$store.state.tableHeight"
        >
          <el-table-column
            label="序号"
            type="index"
            width="60"
          ></el-table-column>
          <el-table-column label="操作" label-width="">
            <template v-slot="scope">
              <span class="action" @click="deleteRow(scope.row)">删除</span>
            </template>
          </el-table-column>
          <el-table-column prop="state" label="订单明细状态" width="100">
            <template v-slot="scope">
              <el-tag v-if="scope.row.state == 2" type="info">待分配</el-tag>
              <el-tag v-if="scope.row.state == 3" type="info"
                >待分配竞价</el-tag
              >
            </template>
          </el-table-column>
          <el-table-column
            label="订单编号"
            width="230"
            prop="orderSn"
          ></el-table-column>
          <el-table-column prop="productImg" label="商品图片" width="130">
            <template v-slot="scope">
              <el-image
                style="width: 90px; height: 60px"
                :src="imgUrlPrefixAdd + scope.row.productImg"
              ></el-image>
            </template>
          </el-table-column>
          <el-table-column
            prop="productName"
            label="商品名称"
            width="、"
          ></el-table-column>
          <el-table-column
            prop="skuName"
            label="规格"
            width="200"
          ></el-table-column>
          <el-table-column
            prop="texture"
            label="材质"
            width="200"
          ></el-table-column>
          <el-table-column
            prop="unit"
            label="计量单位"
            width=""
          ></el-table-column>
          <el-table-column
            prop="buyCounts"
            label="数量"
            width="100"
          ></el-table-column>
          <el-table-column prop="maxPrice" label="最高单价" width="180">
            <template v-slot="scope">
              <el-input-number
                :controls="false"
                :precision="2"
                v-model="scope.row.maxPrice"
              ></el-input-number>
            </template>
          </el-table-column>
          <el-table-column
            prop="gmtCreate"
            label="创建时间"
            width="160"
          ></el-table-column>
        </el-table>
      </div>
      <span slot="footer">
        <el-button class="btn-blue" @click="createBidingM"
          >生成订单商品竞价</el-button
        >
        <el-button @click="showBidingForm = false">返回</el-button>
      </span>
    </el-dialog>
    <!--        选择供应商-->
    <!--选择供应商发送-->
    <el-dialog
      v-dialogDrag
      title="选择供应商"
      id="supplierDialog2"
      v-loading="showTableLoading"
      heigh
      :visible.sync="showSupplierList"
      width="100%"
    >
      <div
        style="
          display: flex;
          flex-direction: row;
          justify-content: space-between;
        "
      >
        <div class="box-left" style="max-width: 100%">
          <div class="e-table" style="background-color: #fff">
            <div class="top" style="height: 30px">
              <div class="left-btn1">
                <!--               Shop和supplier反着的       -->
                <!--                        <el-checkbox v-model="checkAllShop" label="选择所有供应商" :indeterminate="false" @change="toggleSelectAllShop"/>-->
              </div>
              <div class="search_box">
                <el-input
                  style="width: 400px"
                  clearable
                  type="text"
                  @keyup.enter.native="getshopLists"
                  placeholder="输入搜索关键字"
                  v-model="shopList.keywords"
                >
                  <img
                    src="@/assets/search.png"
                    slot="suffix"
                    @click="getshopLists"
                    alt=""
                  />
                </el-input>
              </div>
              <span style="color: #f1083b">双击选择供应商！</span>
            </div>
            <!--                    @selection-change="handleSelectionChangeShop"-->
            <!--                    @row-click="handleCurrentInventoryClickShop"-->
            <el-table
              ref="tableShop1"
              highlight-current-row
              border
              :data="shopList.tableData"
              class="table"
              :height="rightTableHeight"
              @row-dblclick="handleCurrentInventoryClickShop"
            >
              <!--                    <el-table-column type="selection" header-align="center" align="center" width="50"/>-->
              <el-table-column label="序号" type="index" width="60" />
              <el-table-column
                prop="enterpriseName"
                width=""
                label="企业名称"
              />
            </el-table>
            <Pagination
              v-show="shopList.tableData || shopList.tableData.length > 0"
              :total="shopList.paginationInfo.total"
              :pageSize.sync="shopList.paginationInfo.pageSize"
              :currentPage.sync="shopList.paginationInfo.currentPage"
              @currentChange="currentChange"
              @sizeChange="sizeChange"
            />
          </div>
        </div>
        <div class="box-right" style="width: 100%">
          <div class="e-table">
            <div class="top" style="height: 30px">
              <span style="color: #ea083a">双击移除供应商！</span>
            </div>
            <!--                                            @selection-change="handleSelectionChangeShop"
                        @row-click="handleCurrentInventoryClickShop"-->
            <el-table
              ref="tableShop2"
              highlight-current-row
              border
              :data="selectedSupplierList"
              class="table"
              :height="rightTableHeight"
              @row-dblclick="handleRemoveCurrentClickShop"
            >
              <!--                        <el-table-column type="selection" header-align="center" align="center" width="50"/>-->
              <el-table-column label="序号" type="index" width="60" />
              <el-table-column
                prop="enterpriseName"
                width=""
                label="企业名称"
              />
            </el-table>
          </div>
        </div>
      </div>
      <span slot="footer">
        <el-button
          type="primary"
          style="margin-top: 20px"
          @click="confirmSupplierDialog"
          >确定</el-button
        >
        <el-button style="margin-top: 20px" @click="closeSupplierDialog"
          >取消</el-button
        >
      </span>
    </el-dialog>
    <!--        <el-dialog v-loading="bidingFormLoading2" v-dialogDrag id="bidingDialog" title="生成竞价" :visible.sync="showBidingForm2" width="90%" style="margin-left: 10%;" :close-on-click-modal="false">-->
    <!--            <el-divider content-position="left">竞价信息</el-divider>-->
    <!--            <el-form :model="bidingForm2" :rules="bidingFormRules2" label-width="200px" ref="bidingFormRef" :disabled="false" class="demo-ruleForm">-->
    <!--                <el-row>-->
    <!--                    <el-col :span="12">-->
    <!--                        <el-form-item label="商品类型：" prop="productType">-->
    <!--                            <el-radio v-model="bidingForm2.productType" :label="0">零星采购</el-radio>-->
    <!--                            <el-radio v-model="bidingForm2.productType" :label="1">大宗临购</el-radio>-->
    <!--                        </el-form-item>-->
    <!--                    </el-col>-->
    <!--                </el-row>-->
    <!--                <el-row>-->
    <!--                    <el-col :span="12">-->
    <!--                        <el-form-item label="标题：" prop="title">-->
    <!--                            <el-input maxlength="200" placeholder="请输入标题" clearable v-model="bidingForm2.title"></el-input>-->
    <!--                        </el-form-item>-->
    <!--                    </el-col>-->
    <!--                    <el-col :span="12">-->
    <!--                        <el-form-item label="截止时间：" prop="endTime">-->
    <!--                            <el-date-picker-->
    <!--                                value-format="yyyy-MM-dd HH:mm:ss"-->
    <!--                                v-model="bidingForm2.endTime"-->
    <!--                                align="right"-->
    <!--                                type="date"-->
    <!--                                placeholder="选择日期"-->
    <!--                                :picker-options="pickerOptions"-->
    <!--                            >-->
    <!--                            </el-date-picker>-->
    <!--                        </el-form-item>-->
    <!--                    </el-col>-->
    <!--                    <el-col :span="12">-->
    <!--                        <el-form-item label="联系人：" prop="linkName">-->
    <!--                            <el-input maxlength="20" placeholder="请输入联系人" clearable v-model="bidingForm2.linkName"></el-input>-->
    <!--                        </el-form-item>-->
    <!--                    </el-col>-->
    <!--                    <el-col :span="12">-->
    <!--                        <el-form-item label="联系电话：" prop="linkPhone">-->
    <!--                            <el-input type="number" clearable  v-model="bidingForm2.linkPhone" placeholder="请输入11位手机号码"/>-->
    <!--                        </el-form-item>-->
    <!--                    </el-col>-->
    <!--                </el-row>-->
    <!--                <el-row>-->
    <!--                    <el-col :span="24">-->
    <!--                        <el-form-item label="说明：" prop="biddingExplain">-->
    <!--                            <editor v-model="bidingForm2.biddingExplain"></editor>-->
    <!--                        </el-form-item>-->
    <!--                    </el-col>-->
    <!--                </el-row>-->
    <!--            </el-form>-->
    <!--            <el-divider content-position="left">商品列表</el-divider>-->
    <!--            <div class="e-table" style="background-color: #fff" v-loading="biddingOrderItemLoading2">-->
    <!--                <div class="top" style="height: 50px; padding-left: 10px">-->
    <!--                    <div class="left">-->
    <!--                        <el-button class="btn-blue" @click="addMaterialButClick">选择物资</el-button>-->
    <!--                    </div>-->
    <!--                </div>-->
    <!--                <el-table-->
    <!--                    ref="bidingOrderItemRef"-->
    <!--                    border-->
    <!--                    style="width: 100%"-->
    <!--                    :data="bidingFormOrderItems2"-->
    <!--                    class="table"-->
    <!--                    :max-height="$store.state.tableHeight"-->
    <!--                >-->
    <!--                    <el-table-column label="序号" type="index" width="60"></el-table-column>-->
    <!--                    <el-table-column prop="productSn" label="物资编号" width=""/>-->
    <!--                    <el-table-column prop="productName" label="物资名称" width=""/>-->
    <!--                    <el-table-column prop="spec" label="规格" width="200"/>-->
    <!--                    <el-table-column prop="classPathName" label="分类路径" width="200"/>-->
    <!--                    <el-table-column prop="unit" label="计量单位" width="100"/>-->
    <!--                    <el-table-column prop="num" label="数量" width="180">-->
    <!--                        <template slot-scope="scope">-->
    <!--                            <el-input-number size="mini" v-model="scope.row.num"-->
    <!--                                             :min="0" :precision="4" :step="0.1" :max="9999999">-->
    <!--                            </el-input-number>-->
    <!--                        </template>-->
    <!--                    </el-table-column>-->
    <!--                    <el-table-column prop="referencePrice" label="最高单价" width="" v-if="showDevFunc">-->
    <!--                        <template v-slot="scope">-->
    <!--                            <el-input-number-->
    <!--                                :controls="false"-->
    <!--                                :precision="2"-->
    <!--                                v-model="scope.row.referencePrice"-->
    <!--                            ></el-input-number>-->
    <!--                        </template>-->
    <!--                    </el-table-column>-->
    <!--                </el-table>-->
    <!--            </div>-->
    <!--            <span slot="footer">-->
    <!--                <el-button class="btn-blue" @click="createBidingM2">生成竞价</el-button>-->
    <!--                <el-button @click="showBidingForm2 = false">返回</el-button>-->
    <!--            </span>-->
    <!--        </el-dialog>-->
    <!--        选择订单商品-->
    <!--        选择订单商品-->
    <!--        选择订单商品-->
    <!--        选择订单商品-->
    <!--        选择订单商品-->
    <!--        选择订单商品-->
    <!--        选择订单商品-->
    <!--        选择订单商品-->
    <!--        选择订单商品-->
    <el-dialog
      v-dialogDrag
      custom-class="dlg"
      @close="closeDialog"
      width="90%"
      title="订单商品选择"
      :visible.sync="showSelectOrderItem"
    >
      <div class="box-left">
        <div class="e-table">
          <div class="top">
            <div style="width: 100%">
              <el-input
                type="text"
                @blur="listBidingOrderListIdsM"
                placeholder="输入订单编号"
                v-model="orderSn"
              >
                <img
                  src="@/assets/search.png"
                  slot="suffix"
                  @click="listBidingOrderListIdsM"
                  alt=""
                />
              </el-input>
            </div>
          </div>
          <el-table
            max-height="340px"
            @row-click="biddingOrderListClick"
            v-loading="biddingOrderListLoading"
            class="table"
            :height="rightTableHeight"
            :data="bidingOrderList"
            border
          >
            <el-table-column label="订单编号" width="250" prop="orderSn" />
            <el-table-column label="创建时间" width="150" prop="gmtCreate">
              <template v-slot="scope">
                <span>{{ scope.row.gmtCreate | dateStr }}</span>
              </template>
            </el-table-column>
          </el-table>
          <myPagination
            :total="paginationOrderList.total"
            :current-page="paginationOrderList.currentPage"
            :page-size.sync="paginationOrderList.pageSize"
            :page-sizes="[20, 40, 60, 80]"
            @currentChange="listBidingOrderListIdsM"
            @sizeChange="listBidingOrderListIdsM"
          />
        </div>
      </div>
      <div class="box-right">
        <div class="e-table">
          <div class="top" style="height: 50px; padding-left: 10px">
            <div style="width: 200px">
              <el-input
                type="text"
                @blur="listBiddingOrderItemByOrderIdM"
                placeholder="输入搜索关键字"
                v-model="selectOrderItemKeywords"
              >
                <img
                  src="@/assets/search.png"
                  slot="suffix"
                  @click="listBiddingOrderItemByOrderIdM"
                  alt=""
                />
              </el-input>
            </div>
            <span style="color: #ff0000; margin-left: 20px">点击选择！</span>
          </div>
          <el-table
            max-height="340px"
            @row-click="handleCurrentInventoryOrderItemClick"
            ref="eltableCurrentRow"
            v-loading="biddingorderItemSelectLoading"
            class="table"
            :height="rightTableHeight"
            :data="selectOrderItemTable"
            border
          >
            <el-table-column
              label="序号"
              type="index"
              width="60"
            ></el-table-column>
            <!--                        <el-table-column prop="orderSn" label="订单编号" width="100"/>-->
            <!--                        <el-table-column prop="productSn" label="商品编号" width="100"/>-->
            <el-table-column prop="productName" label="商品名称" width="200" />
            <!--                        <el-table-column prop="productImg" label="商品图片" width="130">-->
            <!--                            <template v-slot="scope">-->
            <!--                                <el-image style="width: 90px; height: 60px" :src="imgUrlPrefixAdd + scope.row.productImg"></el-image>-->
            <!--                            </template>-->
            <!--                        </el-table-column>-->
            <el-table-column prop="spec" label="规格" width="100" />
            <el-table-column prop="unit" label="单位" width="80" />
            <el-table-column prop="buyCounts" label="数量" width="100" />
            <el-table-column
              prop="classPathName"
              label="分类路径"
              width="200"
            />
          </el-table>
          <Pagination
            v-show="selectOrderItemTable && selectOrderItemTable.length > 0"
            :total="selectOrderItemPage.total"
            :pageSize.sync="selectOrderItemPage.pageSize"
            :currentPage.sync="selectOrderItemPage.currentPage"
            @currentChange="listBiddingOrderItemByOrderIdM"
            @sizeChange="listBiddingOrderItemByOrderIdM"
          />
        </div>
      </div>
      <div class="box-right">
        <div class="e-table">
          <div class="top" style="height: 50px; padding-left: 10px">
            <span style="color: #ff0000">双击移除！</span>
          </div>
          <el-table
            max-height="340px"
            class="table"
            @row-dblclick="removeSelectOrderItemList"
            :height="rightTableHeight"
            :data="yesSelectOrderItem"
            border
          >
            <el-table-column
              label="序号"
              type="index"
              width="50"
            ></el-table-column>
            <el-table-column prop="orderSn" label="订单编号" width="100" />
            <!--                        <el-table-column prop="productSn" label="商品编号" width="100"/>-->
            <el-table-column prop="productName" label="商品名称" width="200" />
            <!--                        <el-table-column prop="productImg" label="商品图片" width="130">-->
            <!--                            <template v-slot="scope">-->
            <!--                                <el-image style="width: 90px; height: 60px" :src="imgUrlPrefixAdd + scope.row.productImg"></el-image>-->
            <!--                            </template>-->
            <!--                        </el-table-column>-->
            <el-table-column prop="spec" label="规格" width="" />
            <el-table-column prop="unit" label="计量单位" width="100" />
            <el-table-column prop="buyCounts" label="数量" width="100" />
            <el-table-column
              prop="classPathName"
              label="分类路径"
              width="200"
            />
          </el-table>
        </div>
      </div>
      <span slot="footer">
        <el-button type="primary" @click="selectOrderItemAffirm"
          >选择物资</el-button
        >
        <el-button @click="showSelectOrderItem = false">取消</el-button>
      </span>
    </el-dialog>
    <!--        选择基础库-->
    <!--        <el-dialog-->
    <!--            v-dialogDrag custom-class="dlg" @close='closeDialog' width="90%" title="物资选择"-->
    <!--            :visible.sync="showDialog"-->
    <!--        >-->
    <!--            <div class="box-left">-->
    <!--                <select-material-class @classNodeClick="classNodeClick" ref="materialClassRef" :productType="0"/>-->
    <!--            </div>-->
    <!--            <div class="box-right">-->
    <!--                <div class="e-table">-->
    <!--                    <div class="top" style="height: 50px; padding-left: 10px;">-->
    <!--                        <div style="width: 200px">-->
    <!--                            <el-input-->
    <!--                                type="text" @blur="handleInputSearch2" placeholder="输入搜索关键字"-->
    <!--                                v-model="materialKeywords">-->
    <!--                                <img src="@/assets/search.png" slot="suffix" @click="handleInputSearch2" alt=""/>-->
    <!--                            </el-input>-->
    <!--                        </div>-->
    <!--                        <span style="color: #a0a2a9">双击选择物资！</span>-->
    <!--                        <el-button  type="primary" size="small" @click="materialQueryVisible = true">高级搜索</el-button>-->
    <!--                    </div>-->
    <!--                    <el-table-->
    <!--                        max-height="340px"-->
    <!--                        @row-dblclick="handleCurrentInventoryClick"-->
    <!--                        ref="eltableCurrentRow"-->
    <!--                        v-loading="materialTableLoading" class="table" :height="rightTableHeight" :data="materialTableData" border-->
    <!--                    >-->
    <!--                        <el-table-column label="序号" type="index" width="60"></el-table-column>-->
    <!--                        <el-table-column label="物料编号" width="" prop="billNo"/>-->
    <!--                        <el-table-column label="物料名称" width="" prop="materialName"/>-->
    <!--                        <el-table-column label="物料分类" width="" prop="className"/>-->
    <!--                        <el-table-column label="规格" width="" prop="spec"/>-->
    <!--                        <el-table-column label="单位" width="" prop="unit"/>-->
    <!--                    </el-table>-->
    <!--                <Pagination-->
    <!--                    v-show="materialTableData && materialTableData.length > 0"-->
    <!--                    :total="materialPaginationInfo.total"-->
    <!--                    :pageSize.sync="materialPaginationInfo.pageSize"-->
    <!--                    :currentPage.sync="materialPaginationInfo.currentPage"-->
    <!--                    @currentChange="getMaterialTableList"-->
    <!--                    @sizeChange="getMaterialTableList"-->
    <!--                />-->
    <!--                </div>-->
    <!--            </div>-->
    <!--            <div class="box-right">-->
    <!--                <div class="e-table">-->
    <!--                    <div class="top" style="height: 50px; padding-left: 10px;">-->
    <!--                        <span style="color: #a0a2a9">双击移除物资！</span>-->
    <!--                    </div>-->
    <!--                    <el-table-->
    <!--                        max-height="340px"-->
    <!--                        class="table"-->
    <!--                        :height="rightTableHeight" :data="materialSelectRow" border-->
    <!--                    >-->
    <!--                        <el-table-column label="序号" type="index" width="60"></el-table-column>-->
    <!--                        <el-table-column prop="productSn" label="物资编号" width="100"/>-->
    <!--                        <el-table-column prop="productName" label="物资名称" width="100"/>-->
    <!--                        <el-table-column prop="classPathName" label="分类路径" width=""/>-->
    <!--                        <el-table-column prop="spec" label="规格" width="200"/>-->
    <!--                        <el-table-column prop="unit" label="单位" width="100"/>-->
    <!--                    </el-table>-->
    <!--                </div>-->
    <!--            </div>-->
    <!--            <span slot="footer">-->
    <!--                <el-button type="primary" @click="selectMaterialClick">选择物资</el-button>-->
    <!--                <el-button @click="showDialog = false">取消</el-button>-->
    <!--            </span>-->
    <!--        </el-dialog>-->
    <!--        <el-dialog v-dialogDrag title="高级查询" :visible.sync="materialQueryVisible" width="50%">-->
    <!--            <el-form :model="materialFilterData" ref="form" label-width="120px" :inline="false">-->
    <!--                <el-row>-->
    <!--                    <el-col :span="12">-->
    <!--                        <el-form-item  label="物料名称：" prop="materialName">-->
    <!--                            <el-input   placeholder="请输入物料名称" clearable v-model="materialFilterData.materialName">-->
    <!--                            </el-input>-->
    <!--                        </el-form-item>-->
    <!--                    </el-col>-->
    <!--                </el-row>-->
    <!--                <el-row>-->
    <!--                    <el-col :span="12">-->
    <!--                        <el-form-item  label="物料编号：" prop="materialNo">-->
    <!--                            <el-input  placeholder="请输入物料编号" clearable v-model="materialFilterData.materialNo" >-->
    <!--                            </el-input>-->
    <!--                        </el-form-item>-->
    <!--                    </el-col>-->
    <!--                </el-row>-->
    <!--                <el-row>-->
    <!--                    <el-col :span="12">-->
    <!--                        <el-form-item label="规格：" prop="spec">-->
    <!--                            <el-input   placeholder="请输入规格" clearable v-model="materialFilterData.spec">-->
    <!--                            </el-input>-->
    <!--                        </el-form-item>-->
    <!--                    </el-col>-->
    <!--                </el-row>-->
    <!--                <el-row>-->
    <!--                    <el-col :span="12">-->
    <!--                        <el-form-item label="计量单位：" prop="unit">-->
    <!--                            <el-input  placeholder="请输入计量单位" clearable v-model="materialFilterData.unit">-->
    <!--                            </el-input>-->
    <!--                        </el-form-item>-->
    <!--                    </el-col>-->
    <!--                </el-row>-->
    <!--            </el-form>-->
    <!--            <span slot="footer">-->
    <!--                <el-button type="primary" @click="confirmSearch2">查询</el-button>-->
    <!--                <el-button @click="resetSearchConditions2">清空</el-button>-->
    <!--                <el-button @click="materialQueryVisible = false">取消</el-button>-->
    <!--            </span>-->
    <!--        </el-dialog>-->
    <!--    临购清单竞价    -->
    <el-dialog
      v-loading="bidingFormLoading"
      v-dialogDrag
      id="inventorybidingDialog"
      title="生成清单竞价"
      :visible.sync="inventoryBidFormVisible"
      width="90%"
      style="margin-left: 10%"
      :close-on-click-modal="false"
    >
      <el-divider content-position="left">竞价信息</el-divider>
      <el-form
        :model="inventoryBidForm"
        :rules="bidingFormRules"
        label-width="200px"
        ref="inventoryBidingFormRef"
        :disabled="false"
        class="demo-ruleForm"
      >
        <el-row>
          <el-col :span="12">
            <el-form-item label="商品类型：" prop="productType">
              <el-radio
                v-model="inventoryBidForm.productType"
                v-has-permi="{
                  platform: 'oneselfShopManage',
                  auth: 'created-inventoryBid',
                }"
                @change="agreeChange"
                :label="1"
                >大宗临购清单</el-radio
              >
              <el-radio
                v-model="inventoryBidForm.productType"
                v-has-permi="{
                  platform: 'oneselfShopManage',
                  auth: 'created-inventoryBid',
                }"
                @change="agreeChange"
                :label="2"
                >周转材料清单</el-radio
              >
            </el-form-item>
          </el-col>
          <el-col :span="12" v-show="inventoryBidForm.productType === 1 || inventoryBidForm.productType === 2">
            <el-form-item label="价格类型：" prop="billType">
              <el-radio v-model="inventoryBidForm.billType" :label="1"
                >浮动价格</el-radio
              >
              <el-radio v-model="inventoryBidForm.billType" :label="2"
                >固定价格</el-radio
              >
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="标题：" prop="title">
              <el-input
                maxlength="200"
                placeholder="请输入标题"
                clearable
                v-model="inventoryBidForm.title"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="截止时间：" prop="endTime">
              <!--                            <el-date-picker-->
              <!--                                value-format="yyyy-MM-dd HH:mm:ss"-->
              <!--                                v-model="inventoryBidForm.endTime"-->
              <!--                                align="right"-->
              <!--                                type="date"-->
              <!--                                placeholder="选择日期"-->
              <!--                                :picker-options="pickerOptions"-->
              <!--                            >     </el-date-picker>-->
              <el-date-picker
                v-model="inventoryBidForm.endTime"
                type="datetime"
                placeholder="选择日期时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                :picker-options="pickerOptions"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系人：" prop="linkName">
              <el-input
                maxlength="10"
                placeholder="请输入联系人"
                clearable
                v-model="inventoryBidForm.linkName"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系电话：" prop="linkPhone">
              <el-input
                type="number"
                clearable
                v-model="inventoryBidForm.linkPhone"
                placeholder="请输入11位手机号码"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="竞价类型：" prop="type">
              <el-radio-group v-model="inventoryBidForm.type">
                <el-radio :label="1">公开竞价</el-radio>
                <span> <el-radio :label="2">邀请竞价</el-radio></span>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="inventoryBidForm.type === 2">
            <el-form-item label="选择供应商：" prop="">
              <el-button type="primary" @click="showSupplierDialog"
                >选择供应商</el-button
              >
              <!--                            <el-select style="width: 80%" name="name" no-data-text="请选择供应商" v-model="bidingForm.suppliers"  multiple collapse-tags  placeholder="请选择">-->
              <!--                                <el-option-->
              <!--                                    v-for="item in bidingForm.suppliers"-->
              <!--                                    :key="item.enterpriseId"-->
              <!--                                    :label="item.enterpriseName"-->
              <!--                                    :value="item.enterpriseId"-->
              <!--                                    :disabled="item.disabled">-->
              <!--                                </el-option>-->
              <!--                            </el-select>-->
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="说明：" prop="biddingExplain">
              <editor v-model="inventoryBidForm.biddingExplain"></editor>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row style="margin-top: 10px">
          <el-col :span="24">
            <el-form-item label="竞价函说明：" prop="biddingNotice">
              <el-input
                type="textarea"
                :rows="6"
                v-model="inventoryBidForm.biddingNotice"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-divider content-position="left">清单商品</el-divider>
      <div
        class="e-table"
        style="background-color: #fff"
        v-loading="biddingOrderItemLoading"
      >
        <div class="top" style="height: 50px; padding-left: 10px">
          <div class="left">
            <div class="search_box" style="margin-left: 10px">
              <el-button type="primary" class="" @click="selectInventoryProduct"
                >选择清单商品</el-button
              >
            </div>
          </div>
        </div>
        <el-table
          ref="bidingOrderItemRef"
          border
          style="width: 100%"
          :data="inventoryBidForm.synthesizeTemporaryDtlList"
          class="table"
          :max-height="$store.state.tableHeight"
        >
          <el-table-column
            label="序号"
            type="index"
            width="60"
          ></el-table-column>
          <el-table-column label="操作" label-width="">
            <template v-slot="scope">
              <span class="action" @click="deleteInventoryRow(scope.row)"
                >删除</span
              >
            </template>
          </el-table-column>
          <!--                    <el-table-column prop="state" label="订单明细状态" width="100">-->
          <!--                        <template v-slot="scope">-->
          <!--                            <el-tag v-if="scope.row.state == 2" type="info">待分配</el-tag>-->
          <!--                            <el-tag v-if="scope.row.state == 3" type="info">待分配竞价</el-tag>-->
          <!--                        </template>-->
          <!--                    </el-table-column>-->
          <el-table-column
            label="商品编号"
            width="230"
            prop="productSn"
          ></el-table-column>
          <el-table-column prop="productImg" label="商品图片" width="130">
            <template v-slot="scope">
              <el-image
                style="width: 90px; height: 60px"
                :src="imgUrlPrefixAdd + scope.row.productImg"
              ></el-image>
            </template>
          </el-table-column>
          <el-table-column
            prop="productName"
            label="商品名称"
            width="、"
          ></el-table-column>
          <el-table-column
            prop="skuName"
            label="规格"
            width="200"
          ></el-table-column>
          <el-table-column
            prop="texture"
            label="材质"
            width="200"
          ></el-table-column>
          <el-table-column
            prop="unit"
            label="计量单位"
            width=""
          ></el-table-column>
          <el-table-column
            prop="qty"
            label="数量"
            width="100"
          ></el-table-column>
          <el-table-column
            v-if="inventoryBidForm.billType === 1"
            prop="bidNetPrice"
            label="网价"
            width="180"
          >
            <template v-slot="scope">
              <el-input-number
                :controls="false"
                :precision="2"
                v-model="scope.row.bidNetPrice"
              ></el-input-number>
            </template>
          </el-table-column>
          <el-table-column prop="maxPrice" label="最高单价" width="180">
            <template v-slot="scope">
              <el-input-number
                :controls="false"
                :precision="2"
                v-model="scope.row.maxPrice"
              ></el-input-number>
            </template>
          </el-table-column>
          <el-table-column
            prop="gmtCreate"
            label="创建时间"
            width="160"
          ></el-table-column>
        </el-table>
      </div>
      <span slot="footer">
        <el-button class="btn-blue" @click="createInventoryBid"
          >生成清单竞价</el-button
        >
        <el-button @click="inventoryBidFormVisible = false">返回</el-button>
      </span>
    </el-dialog>
    <!--  清单选择      -->
    <el-dialog
      v-dialogDrag
      custom-class="dlg"
      @close="closeDialog"
      width="90%"
      title="清单商品选择"
      :visible.sync="showInventoryList"
    >
      <div class="box-left">
        <div class="e-table">
          <div class="top">
            <div style="width: 100%">
              <el-input
                type="text"
                @blur="getInventoryList"
                v-model="inventoryKey"
                placeholder="输入清单编号"
              >
                <img
                  src="@/assets/search.png"
                  slot="suffix"
                  @click="getInventoryList"
                  alt=""
                />
              </el-input>
            </div>
          </div>
          <el-table
            max-height="340px"
            @row-click="inventoryListClick"
            v-loading="biddingOrderListLoading"
            class="table"
            :height="rightTableHeight"
            :data="inventoryList"
            border
          >
            <el-table-column
              label="清单编号"
              width="250"
              prop="synthesizeTemporarySn"
            />
            <el-table-column label="清单类型" width="100" prop="清单类型">
              <template v-slot="scope">
                <el-tag v-if="scope.row.billType == 1">浮动价格</el-tag>
                <el-tag v-if="scope.row.billType == 2">固定价格</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="创建时间" width="150" prop="gmtCreate">
              <template v-slot="scope">
                <span>{{ scope.row.gmtCreate | dateStr }}</span>
              </template>
            </el-table-column>
          </el-table>
          <myPagination
            :total="inventoryListPagination.total"
            :current-page="inventoryListPagination.currentPage"
            :page-size.sync="inventoryListPagination.pageSize"
            :page-sizes="[20, 40, 60, 80]"
            @currentChange="getInventoryList"
            @sizeChange="getInventoryList"
          />
        </div>
      </div>
      <div class="box-right">
        <div class="e-table">
          <div class="top" style="height: 50px; padding-left: 10px">
            <span style="color: #ff0000; margin-left: 20px">点击选择！</span>
          </div>
          <el-table
            max-height="340px"
            @row-click="handleCurrentInventoryProductClick"
            ref="eltableCurrentRow"
            v-loading="biddingorderItemSelectLoading"
            class="table"
            :height="rightTableHeight"
            :data="inventoryProductList"
            border
          >
            <el-table-column
              label="序号"
              type="index"
              width="60"
            ></el-table-column>
            <el-table-column prop="productName" label="商品名称" width="200" />
            <el-table-column prop="spec" label="规格" width="100" />
            <el-table-column prop="unit" label="单位" width="80" />
            <el-table-column prop="qty" label="数量" width="100" />
            <el-table-column
              prop="classNamePath"
              label="分类路径"
              width="200"
            />
          </el-table>
          <!--                    <Pagination-->
          <!--                        v-show="selectOrderItemTable && selectOrderItemTable.length > 0"-->
          <!--                        :total="selectOrderItemPage.total"-->
          <!--                        :pageSize.sync="selectOrderItemPage.pageSize"-->
          <!--                        :currentPage.sync="selectOrderItemPage.currentPage"-->
          <!--                        @currentChange="listBiddingOrderItemByOrderIdM"-->
          <!--                        @sizeChange="listBiddingOrderItemByOrderIdM"-->
          <!--                    />-->
        </div>
      </div>
      <div class="box-right">
        <div class="e-table">
          <div class="top" style="height: 50px; padding-left: 10px">
            <span style="color: #ff0000">双击移除！</span>
          </div>
          <el-table
            max-height="340px"
            class="table"
            @row-dblclick="removeSelectProductList"
            :height="rightTableHeight"
            :data="inventorySelectedProductList"
            border
          >
            <el-table-column
              label="序号"
              type="index"
              width="50"
            ></el-table-column>
            <el-table-column prop="productSn" label="商品编号" width="100" />
            <!--                        <el-table-column prop="productSn" label="商品编号" width="100"/>-->
            <el-table-column prop="productName" label="商品名称" width="200" />
            <el-table-column prop="spec" label="规格" width="" />
            <el-table-column prop="unit" label="计量单位" width="100" />
            <el-table-column prop="qty" label="数量" width="100" />
            <el-table-column
              prop="classNamePath"
              label="分类路径"
              width="200"
            />
          </el-table>
        </div>
      </div>
      <span slot="footer">
        <el-button type="primary" @click="selectInventoryProductAffirm"
          >选择物资</el-button
        >
        <el-button @click="showInventoryList = false">取消</el-button>
      </span>
    </el-dialog>
    <!-- 延期截至时间 -->
    <el-dialog
      title="修改截止时间"
      :visible.sync="deadlineTimeDialog"
      width="30%"
    >
      <el-form
        :model="timeExpandForm"
        :rules="timeExpandRules"
        label-width="100px"
        ref="timeExpandFormRef"
        :disabled="false"
        class="demo-ruleForm"
      >
        <el-form-item label="截止时间" prop="endTime">
          <el-date-picker
            style="width: 100%; margin-right: 20px"
            v-model="timeExpandForm.endTime"
            type="datetime"
            placeholder="选择日期时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="延期理由" prop="result">
          <el-input
            type="textarea"
            :rows="6"
            style="width: 100%; margin-right: 20px"
            clearable
            v-model="timeExpandForm.result"
            placeholder="请输入延期理由"
          />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button style="padding: 0px 20px" @click="deadlineTimeDialog = false"
          >取 消</el-button
        >
        <el-button
          style="padding: 0px 20px"
          type="primary"
          @click="deadlineTimeHandle"
          >确 定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { queryPageMaterialDtl } from '@/api/platform/product/materialManage'

// import SelectMaterialClass from '@/components/classTree'
import '@/utils/jquery.scrollTo.min'
import Pagination from '@/components/pagination/pagination'
import myPagination from '@/components/pagination/myPagination'
import editor from '@/components/quillEditor'
// eslint-disable-next-line no-unused-vars
import { debounce, hideLoading, showLoading } from '@/utils/common'
// eslint-disable-next-line no-unused-vars
import {
    listBidingOrderItemsList,
    listMyCreateBiding,
    submitBidingByIds,
    listBidingOrderListIds,
    createInventoryBidding,
    bidOpening,
    deadlineTime,
} from '@/api/shopManage/biding/biding'
import {
    createBidingByOrder,
    listByEntitySynthesizeTemporary,
} from '@/api/platform/order/orders'
import { getSupplierShopsList } from '@/api/platform/supplier/supplierAudit'
import { getShopList } from '@/api/platform/mail/inbox'
import { mapState } from 'vuex'
import { suppliersSynthesizeTemporaryGetBySn } from '@/api/frontStage/userCenter'
// import { startProcessByKey } from '@/pages/cloudCenter/workflow/api/instance'

export default {
    components: {
        Pagination,
        myPagination,
        editor,
    // SelectMaterialClass
    },
    watch: {
        'bidingForm.endTime': {
            handler () {
                this.formatStr()
                this.$forceUpdate()
            },
        },
        'inventoryBidForm.endTime': {
            handler () {
                this.formatInventoryStr()
                this.$forceUpdate()
            },
        },
        'filterData.orderBy': {
            handler () {
                this.getTableData()
            },
        },
    },
    computed: {
        ...mapState(['userInfo']),
        // 左侧树高度
        leftTreeHeight () {
            return this.screenHeight - 21 + 'px'
        },
        // 列表宽度
        rightTableWidth () {
            return this.screenWidth - 302 + 'px'
        },
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 292
        },
        // 分配用户、分配岗位列表宽度
        rightTableWidth2 () {
            return this.screenWidth - 300 + 'px'
        },
        rightTableHeight2 () {
            return this.screenHeight - 210
        },
    },
    filters: {
        dateStr (dateStr) {
            if (dateStr == null) return
            let newDateSr = dateStr.split(' ')
            return newDateSr[0]
        },
    },
    data () {
        return {
            rowData: {},
            timeExpandForm: {
                endTime: null,
                result: null,
            },
            deadlineTimeDialog: false,
            // 第二个选择框的供应商
            activeName: 'first',
            selectedSupplierList: [],
            materialSelectRow: [],
            // 选中供应商
            selectedSupplierRow: [],
            // 选中供应商名称
            selectedSupplierRowName: [],
            checkAllShop: false, //全选控制
            shopList: {
                tableData: [],
                keywords: null,
                paginationInfo: {
                    // 分页
                    total: 200,
                    pageSize: 20,
                    currentPage: 1,
                },
            },
            showTableLoading: false,
            showSupplierList: false,
            tableSelectRow: [],
            bidingOrderItemState: null,
            bidingOrderItemStateSelect: [
                { value: null, label: '全部' },
                { value: 2, label: '待分配' },
                { value: 3, label: '待分配竞价' },
            ],
            // 竞价
            bidingOrderItemSelectRow: [],
            biddingOrderItemLoading: false,
            biddingOrderListLoading: false,
            bidingFormRules: {
                orderSn: [{ required: true, message: '请输入订单号', trigger: 'blur' }],
                title: [
                    { required: true, message: '请输入标题', trigger: 'blur' },
                    { min: 1, max: 200, message: '超过限制', trigger: 'blur' },
                ],
                billType: [
                    { required: true, message: '请选择竞价类型', trigger: 'blur' },
                ],
                endTime: [
                    { required: true, message: '请选择截止时间', trigger: 'blur' },
                ],
                linkPhone: [
                    { required: false, message: '请输入11位手机号', trigger: 'blur' },
                    {
                        pattern: /^1[3456789]\d{9}$/,
                        message: '请输入正确的手机号',
                        trigger: 'blur',
                    },
                ],
            },
            biddingOrderItemLoading2: false,
            showBidingForm2: false,
            showSelectOrderItem: false,
            showDialog: false,
            bidingFormRules2: {
                orderSn: [{ required: true, message: '请输入订单号', trigger: 'blur' }],
                title: [
                    { required: true, message: '请输入标题', trigger: 'blur' },
                    { min: 1, max: 200, message: '超过限制', trigger: 'blur' },
                ],
                endTime: [
                    { required: true, message: '请选择截止时间', trigger: 'blur' },
                ],
                linkPhone: [
                    { required: false, message: '请输入11位手机号', trigger: 'blur' },
                    {
                        pattern: /^1[3456789]\d{9}$/,
                        message: '请输入正确的手机号',
                        trigger: 'blur',
                    },
                ],
            },
            timeExpandRules: {
                endTime: [{ required: true, message: '延期时间', trigger: 'blur' }],
                result: [{ required: true, message: '延期时间', trigger: 'blur' }],
            },
            orderProductType: 10,
            bidingFormLoading2: false,
            bidingForm: {
                productType: 0,
                biddingSourceType: 1,
                billType: 1,
                title: null,
                type: 1,
                endTime: null,
                linkName: null,
                linkPhone: null,
                biddingExplain: null,
                orderItems: [],
                suppliers: [],
                // biddingNotice: '说明：\n' +
                //     '1、本表中各材料数量、金额为暂估数量，结算以实际供应并经双方签认的数量和金额为准；\n' +
                //     '2、“到场含税单价”包含但不限于材料费、运杂费、装车、捆扎、利润、管理费、安全费、税费、开卷校平等一切费用；税率13%，若因国家税率发生变化，按国家最新税率执行，发票开具均采用一票制； \n' +
                //     '3、材料执行标准：满足国家、行业相关标准文件规定以及项目施工设计文件要求\n' +
                //     '4、结算与支付方式：\n' +
                //     '5、竞价函报送要求：请将完成填报的竞价函PDF彩色扫描件上传至物资采购平台竞价专区，报价截止时间为年月日分，逾期送达的视为报价无效。\n' +
                //     '6、竞价函启封比价：竞价发起单位将于报价截止时间后组织比价，最终根据比价情况，由竞价发起单位采购领导小组选择确定供应商。\n' +
                //     '7、报价方申明（若有必要）： '
                /*                biddingNotice: `说明：
1、本表中各材料数量、金额为暂估数量，结算以实际供应并经双方签认的数量和金额为准；
2、“到场含税单价”包含但不限于材料费、运杂费、装车、捆扎、利润、管理费、安全费、税费、开卷校平等一切费用；税率13%，若因国家税率发生变化，按国家最新税率执行，发票开具均采用一票制；
3、材料执行标准：满足国家、行业相关标准文件规定以及项目施工设计文件要求
4、结算与支付方式：
5、竞价函报送要求：请将完成填报的竞价函PDF彩色扫描件上传至物资采购平台竞价专区，报价截止时间为'{year}'年'{month}'月'{day}'日'{minutes}'分，逾期送达的视为报价无效。
6、竞价函启封比价：竞价发起单位将于报价截止时间后组织比价，最终根据比价情况，由竞价发起单位采购领导小组选择确定供应商。
7、报价方申明（若有必要）： `*/
                biddingNotice: `说明：
   1、本表中各材料数量、金额为暂估数量，结算以实际供应并经双方签认的数量和金额为准。
    2、“到场含税单价”为材料到达供货地点的含税单价，包含但不限于材料费、运杂费、装车、捆扎、利润、管理费、安全费、税费等一切费用（卸车由买方负责并承担相关费用）；
        3、材料执行标准：满足国家、行业相关标准文件规定以及项目施工设计文件要求
        4、发票开具均采用一票制。
        5、结算与支付方式：先货后款，卖方全部货物到场后需持双方收料签字确认的对账单与买方3日内完成对账（有特殊情况需双方协商），甲方在收到乙方提供的符合甲方财务要求的发票和对账单后5天内完成上账，60天内以银行转账的方式支付当期货款。
        6、竞价函报送要求：请将线上完成填报的竞价函导出打印，签字盖章后以PDF彩色扫描件上传至竞价专区，标题以“公司名称+**材料竞价”命名，'{year}'年'{month}'月'{day}'日'{minutes}'分，逾期送达的视为报价无效。
        7、竞价函比价：竞价发起单位将于报价截止时间后组织比价，最终根据比价情况，由竞价发起单位采购领导小组选择确定供应商。
        8、其他说明：`,
            },
            bidingForm2: {
                biddingSourceType: 2,
                title: null,
                type: 1,
                billType: 1,
                productType: 0,
                endTime: null,
                linkName: null,
                linkPhone: null,
                biddingExplain: null,
                orderItems: [],
            },
            infoStr: '选择供应商',
            bidingFormOrderItems: [],
            bidingOrderList: [],
            bidingFormOrderItems2: [],
            pickerOptions: {
                disabledDate (time) {
                    return time.getTime() < Date.now()
                },
            },
            bidingFormLoading: false,
            showBidingForm: false,
            tableLoading: false,
            materialTableLoading: false,
            // 状态选择查询
            // 表格数据
            keywords: null, // 关键字
            keywords2: null, // 关键字
            queryVisible: false, // 高级搜索显示
            paginationInfo: {
                // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            paginationInfo2: {
                // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            paginationOrderList: {
                // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            materialPaginationInfo: {
                // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            selectOrderItemPage: {
                // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            tableData: [], // 表格数据
            materialTableData: [], // 表格数据
            selectOrderItemTable: [], // 表格数据
            yesSelectOrderItem: [], // 表格数据
            // 高级搜索
            filterData: {
                createDate: [],
                endDate: [],
                orderSn: null,
                title: null,
                biddingSn: null,
                state: null,
                productType: null,
                stateSelect: [
                    { value: null, label: '全部' },
                    { value: 0, label: '待提交' },
                    { value: 1, label: '待审核' },
                    { value: 2, label: '审核失败' },
                    { value: 5, label: '审核通过' },
                    // { value: 7, label: '已确认' },
                    { value: 9, label: '已流标' },
                ],
                productTypeSelect: [
                    { value: null, label: '全部' },
                    { value: 0, label: '低值易耗品' },
                    { value: 1, label: '大宗临购' },
                    { value: 2, label: '大宗临购清单' },
                ],
                biddingState: null,
                biddingStateSelect: [
                    { value: null, label: '全部' },
                    { value: 1, label: '未开始' },
                    { value: 2, label: '进行中' },
                    { value: 3, label: '已结束' },
                ],
                publicityState: null,
                publicityStateSelect: [
                    { value: null, label: '全部' },
                    { value: 0, label: '未发布' },
                    { value: 1, label: '已发布' },
                ],
                orderBy: 1,
                type: null,
                typeSelect: [
                    { value: null, label: '全部' },
                    { value: 1, label: '公开竞价' },
                    { value: 2, label: '邀请竞价' },
                ],
            },
            materialClassId: null,
            materialKeywords: null,
            selectOrderItemKeywords: null,
            biddingorderItemSelectLoading: false,
            orderSn: null,
            orderId: null,
            // 选中的价格类型
            currentBillType: null,
            materialQueryVisible: false,
            materialFilterData: {
                spec: null,
                unit: null,
                materialName: null,
                materialNo: null,
                stateOptionTitle: null,
            },
            // 高和宽
            screenWidth: 0,
            screenHeight: 0,
            inventoryKey: '',
            inventoryBidFormVisible: false,
            // 清单列表
            showInventoryList: false,
            inventoryList: [],
            inventoryListPagination: {
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            inventoryProductList: [],
            // 选中清单的商品明细
            inventorySelectedProductList: [],
            inventoryBidForm: {
                productType: 2,
                biddingSourceType: 3,
                billType: 1,
                title: null,
                type: 1,
                endTime: null,
                linkName: null,
                linkPhone: null,
                biddingExplain: null,
                synthesizeTemporaryDtlList: [],
                suppliers: [],
                //                 biddingNotice: `说明：
                // 1、本表中各材料数量、金额为暂估数量，结算以实际供应并经双方签认的数量和金额为准；
                // 2、“到场含税单价”包含但不限于材料费、运杂费、装车、捆扎、利润、管理费、安全费、税费、开卷校平等一切费用；税率13%，若因国家税率发生变化，按国家最新税率执行，发票开具均采用一票制；
                // 3、材料执行标准：满足国家、行业相关标准文件规定以及项目施工设计文件要求
                // 4、结算与支付方式：
                // 5、竞价函报送要求：请将完成填报的竞价函PDF彩色扫描件上传至物资采购平台竞价专区，报价截止时间为'{year}'年'{month}'月'{day}'日'{minutes}'分，逾期送达的视为报价无效。
                // 6、竞价函启封比价：竞价发起单位将于报价截止时间后组织比价，最终根据比价情况，由竞价发起单位采购领导小组选择确定供应商。
                // 7、报价方申明（若有必要）： `
                /*                biddingNotice: `说明：
// 备注：
// 1、本表中各材料数量、金额为暂估数量，结算以实际供应并经双方签认的数量和金额为准。
// 2、网价参照《我的钢铁网》“成都市场”2023年12月20日流体管对应规格型号公布的最低价格报价（如无该规格型号报价，则选取相应报价），结算以到货当日对应规格型号网价+固定费，若到货当日无网价，按照到货当日接近日期最低网价执行；“到场含税单价”为材料到达供货地点的含税单价，包含但不限于材料费、运杂费、装车、捆扎、利润、管理费、安全费、税费等一切费用（卸车由买方负责并承担相关费用）；
// 3、材料执行标准：满足国家、行业相关标准文件规定以及项目施工设计文件要求按照国家最新的标准验收并符合招标人设计图纸要求，供方提供加盖印章的材质证明。
// 4、发票开具均采用一票制。
// 5、结算与支付方式：先货后款，卖方全部货物到厂后需持双方收料签字确认的对账单与买方3日内完成对账（有特殊情况需双方协商），对账完成后乙方开具有效增值税专用发票，甲方收到增值税专用发票60个工作日内（有特殊情况需双方协商）向乙方付清已开票金额。
// 6、竞价函报送要求：请将完成填报的竞价函PDF彩色扫描件加密后（文档密码由报价方妥善保管）发送至四川路桥建设集团股份有限公司物资分公司企业邮箱**************，邮件标题以“公司名称+电话号码”命名，报价截止时间为'{year}'年'{month}'月'{day}'日'{minutes}'分，电子邮件逾期送达的视为报价无效。
// 7、竞价函启封比价：竞价发起单位将于报价截止时间后组织启封比价。届时，竞价发起单位将根据邮件标题预留电话，联系报价单位询问文件密码启封竞价函，请报价方保持电话畅通；最终根据比价情况，由竞价发起单位采购领导小组选择确定供应商。
// 8、报价方申明（若有必要）：`*/
                biddingNotice: `说明：
   1、本表中各材料数量、金额为暂估数量，结算以实际供应并经双方签认的数量和金额为准。
    2、“到场含税单价”为材料到达供货地点的含税单价，包含但不限于材料费、运杂费、装车、捆扎、利润、管理费、安全费、税费等一切费用（卸车由买方负责并承担相关费用）；
        3、材料执行标准：满足国家、行业相关标准文件规定以及项目施工设计文件要求
        4、发票开具均采用一票制。
        5、结算与支付方式：先货后款，卖方全部货物到场后需持双方收料签字确认的对账单与买方3日内完成对账（有特殊情况需双方协商），甲方在收到乙方提供的符合甲方财务要求的发票和对账单后5天内完成上账，60天内以银行转账的方式支付当期货款。
        6、竞价函报送要求：请将线上完成填报的竞价函导出打印，签字盖章后以PDF彩色扫描件上传至竞价专区，标题以“公司名称+**材料竞价”命名，报价截止时间为'{year}'年'{month}'月'{day}'日'{minutes}'分，逾期送达的视为报价无效。
        7、竞价函比价：竞价发起单位将于报价截止时间后组织比价，最终根据比价情况，由竞价发起单位采购领导小组选择确定供应商。
        8、其他说明：`,
                // 说明：
                // 备注：
                // 1、本表中各材料数量、金额为暂估数量，结算以实际供应并经双方签认的数量和金额为准。
                // 2、“到场含税单价”为材料到达供货地点的含税单价，包含但不限于材料费、运杂费、装车、捆扎、利润、管理费、安全费、税费等一切费用（卸车由买方负责并承担相关费用）；
                // 3、材料执行标准：满足国家、行业相关标准文件规定以及项目施工设计文件要求按照国家最新的标准验收并符合招标人设计图纸要求，供方提供加盖印章的材质证明。
                // 4、发票开具均采用一票制。                                                                                                                                                                 5、结算与支付方式：先货后款，卖方全部货物到厂后需持双方收料签字确认的对账单与买方3日内完成对账（有特殊情况需双方协商），对账完成后乙方开具有效增值税专用发票，甲方收到增值税专用发票60个工作日内（有特殊情况需双方协商）向乙方付清已开票金额。
                // 6、竞价函报送要求：请将完成填报的竞价函PDF彩色扫描件加密后（文档密码由报价方妥善保管）发送至四川路桥建设集团股份有限公司物资分公司企业邮箱**************，邮件标题以“公司名称+电话号码”命名，报价截止时间为2024年3月6日上午下午16:30分，电子邮件逾期送达的视为报价无效。
                // 7、竞价函启封比价：竞价发起单位将于报价截止时间后组织启封比价。届时，竞价发起单位将根据邮件标题预留电话，联系报价单位询问文件密码启封竞价函，请报价方保持电话畅通；最终根据比价情况，由竞价发起单位采购领导小组选择确定供应商。
                // 8、报价方申明（若有必要）：
            },
        }
    },
    mounted () {
    // 获取屏幕宽高
        this.filterData.state = null
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
    },
    // keepAlive使用了触发
    activated () {
        this.filterData.state = 1
        this.activeName = 'first'
        this.getTableData()
    },
    created () {},
    methods: {
        deadlineM (row) {
            this.rowData = row
            this.deadlineTimeDialog = true
        },
        deadlineTimeHandle () {
            const params = {
                biddingId: this.rowData.biddingId,
                deadlineTime: this.timeExpandForm.endTime,
                deadlineTimeResult: this.timeExpandForm.result,
            }
            deadlineTime(params).then(() => {
                this.$message.success('延长截止时间成功！')
                this.getTableData()
                this.deadlineTimeDialog = false
            })
        },
        bidOpening (row) {
            const params = {
                id: row.biddingId,
            }
            bidOpening(params).then(() => {
                this.$message.success('开标成功，请前往开标记录查看！')
                this.getTableData()
            })
        },
        handleClick (tab) {
            if (tab.label === '待审核') {
                this.filterData.state = 1
                this.getTableData()
            } else {
                this.filterData.state = null
                this.getTableData()
            }
        },
        // 创建清单竞价
        createInventoryBid () {
            this.$refs.inventoryBidingFormRef.validate(valid => {
                if (valid) {
                    if (this.inventoryBidForm.synthesizeTemporaryDtlList.length === 0) {
                        return this.$message.warning('请选择商品！')
                    }
                    if (this.inventoryBidForm.billType != this.currentBillType) {
                        return this.$message.error('价格类型不一致，请重新选择')
                    }
                    if (
                        this.inventoryBidForm.type === 2 &&
            this.inventoryBidForm.suppliers.length === 0
                    ) {
                        return this.$message.warning('请选择供应商！')
                    }
                    // 浮动价格必须先填写网价
                    if (this.inventoryBidForm.billType === 1) {
                        let arr = this.inventoryBidForm.synthesizeTemporaryDtlList.filter(
                            item => {
                                return item.bidNetPrice == '' || Number(item.bidNetPrice) == 0
                            }
                        )
                        if (arr.length > 0) {
                            return this.$message.error(
                                '商品【' + arr[0].productName + '】未填写网价'
                            )
                        }
                    }
                    // maxPrice 为0或者为空提示确认
                    let arr = this.inventoryBidForm.synthesizeTemporaryDtlList.filter(
                        item => {
                            return item.maxPrice == '' || Number(item.maxPrice) == 0
                        }
                    )
                    if (arr.length > 0) {
                        return this.$confirm(
                            '商品：【' +
                arr[0].productName +
                '】未设置最高单价，将不会进行限价，是否确认提交？',
                            '提示',
                            {
                                confirmButtonText: '确定',
                                cancelButtonText: '取消',
                                type: 'warning',
                            }
                        )
                            .then(() => {
                                createInventoryBidding(this.inventoryBidForm).then(res => {
                                    if (res.code === 200) {
                                        this.$message.success('创建成功！')
                                        this.showInventoryList = false
                                        this.inventoryBidFormVisible = false
                                        this.getTableData()
                                        this.inventoryBidForm.synthesizeTemporaryDtlList = []
                                        this.inventoryBidForm = {
                                            productType: 2,
                                            biddingSourceType: 3,
                                            billType: 1,
                                            title: null,
                                            type: 1,
                                            endTime: null,
                                            linkName: null,
                                            linkPhone: null,
                                            biddingExplain: null,
                                            synthesizeTemporaryDtlList: [],
                                            suppliers: [],
                                        }
                                    }
                                })
                            })
                            .catch(() => {
                                this.$message({
                                    type: 'info',
                                    message: '已取消',
                                })
                            })
                    } else {
                        createInventoryBidding(this.inventoryBidForm).then(res => {
                            if (res.code === 200) {
                                this.$message.success('创建成功！')
                                this.showInventoryList = false
                                this.inventoryBidFormVisible = false
                                this.getTableData()
                                this.inventoryBidForm.synthesizeTemporaryDtlList = []
                                this.inventoryBidForm = {
                                    productType: 2,
                                    biddingSourceType: 3,
                                    billType: 1,
                                    title: null,
                                    type: 1,
                                    endTime: null,
                                    linkName: null,
                                    linkPhone: null,
                                    biddingExplain: null,
                                    synthesizeTemporaryDtlList: [],
                                    suppliers: [],
                                }
                            }
                        })
                    }
                } else {
                    this.$message.error('请检查必填项')
                    return
                }
            })
        },
        deleteInventoryRow (row) {
            this.inventoryBidForm.synthesizeTemporaryDtlList =
        this.inventoryBidForm.synthesizeTemporaryDtlList.filter(t => {
            if (t.synthesizeTemporaryDtlId == row.synthesizeTemporaryDtlId) {
                return false
            } else {
                return true
            }
        })
        },
        // 确认选定商品
        selectInventoryProductAffirm () {
            this.inventoryBidForm.synthesizeTemporaryDtlList =
        this.inventorySelectedProductList
            this.showInventoryList = false
            // 清空
            this.inventoryList = []
            this.inventoryProductList = []
            this.inventorySelectedProductList = []
        },
        removeSelectProductList (row) {
            this.inventorySelectedProductList =
        this.inventorySelectedProductList.filter(
            t => t.synthesizeTemporaryDtlId != row.synthesizeTemporaryDtlId
        )
        },
        handleCurrentInventoryProductClick (row) {
            if (row.isBidding === 1) {
                return this.$message.warning('该商品已生成竞价')
            }
            for (let i = 0; i < this.inventorySelectedProductList.length; i++) {
                let t = this.inventorySelectedProductList[i]
                if (t.synthesizeTemporaryDtlId == row.synthesizeTemporaryDtlId) {
                    return this.$message.warning('该商品已选择！')
                }
            }
            this.inventorySelectedProductList.push(row)
        },
        async inventoryListClick (row) {
            //  根据清单编号查询：synthesizeTemporarySn
            this.inventoryProductList = []
            this.inventorySelectedProductList = []
            showLoading()
            this.inventoryBidForm.billType = row.billType
            this.currentBillType = row.billType
            let res = await suppliersSynthesizeTemporaryGetBySn({
                sn: row.synthesizeTemporarySn,
            })
            this.inventoryProductList = res.dtls || []
            hideLoading()
        },
        // 获取清单列表数据
        async getInventoryList () {
            let params = {
                pageNum: this.inventoryListPagination.currentPage,
                pageSize: this.inventoryListPagination.pageSize,
            }
            if (this.inventoryKey != null && this.inventoryKey != '') {
                params.synthesizeTemporarySn = this.inventoryKey.trim()
            }
            // params.bidStatus = 0
            params.billType = this.inventoryBidForm.billType
            params.states = [0, 1, 5, 11]
            showLoading()
            let res = await listByEntitySynthesizeTemporary(params)
            this.inventoryListPagination.total = res.totalCount
            this.inventoryListPagination.pageSize = res.pageSize
            this.inventoryListPagination.currentPage = res.currPage
            this.inventoryList = res.list || []
            hideLoading()
        },
        selectInventoryProduct () {
            this.inventoryList = []
            this.inventoryProductList = []
            this.inventorySelectedProductList = []
            this.showInventoryList = true
            this.getInventoryList()
        },
        createInventoryBiding () {
            this.inventoryBidFormVisible = true
            this.inventorySelectedProductList = []
            this.inventoryList = []
            this.inventoryProductList = []
        },
        formatStr () {
            let year = this.bidingForm.endTime.substring(0, 4)
            let month = this.bidingForm.endTime.substring(5, 7)
            let day = this.bidingForm.endTime.substring(8, 10)
            let minutes = this.bidingForm.endTime.substring(11, 16)
            /*            this.bidingForm.biddingNotice = `说明：
1、本表中各材料数量、金额为暂估数量，结算以实际供应并经双方签认的数量和金额为准；
2、“到场含税单价”包含但不限于材料费、运杂费、装车、捆扎、利润、管理费、安全费、税费、开卷校平等一切费用；税率13%，若因国家税率发生变化，按国家最新税率执行，发票开具均采用一票制；
3、材料执行标准：满足国家、行业相关标准文件规定以及项目施工设计文件要求
4、结算与支付方式：
5、竞价函报送要求：请将完成填报的竞价函PDF彩色扫描件上传至物资采购平台竞价专区，报价截止时间为${year}年${month}月${day}日${minutes}分，逾期送达的视为报价无效。
6、竞价函启封比价：竞价发起单位将于报价截止时间后组织比价，最终根据比价情况，由竞价发起单位采购领导小组选择确定供应商。'
7、报价方申明（若有必要）： `*/
            this.bidingForm.biddingNotice = `说明：
   1、本表中各材料数量、金额为暂估数量，结算以实际供应并经双方签认的数量和金额为准。
    2、“到场含税单价”为材料到达供货地点的含税单价，包含但不限于材料费、运杂费、装车、捆扎、利润、管理费、安全费、税费等一切费用（卸车由买方负责并承担相关费用）；
        3、材料执行标准：满足国家、行业相关标准文件规定以及项目施工设计文件要求
        4、发票开具均采用一票制。
        5、结算与支付方式：先货后款，卖方全部货物到场后需持双方收料签字确认的对账单与买方3日内完成对账（有特殊情况需双方协商），甲方在收到乙方提供的符合甲方财务要求的发票和对账单后5天内完成上账，60天内以银行转账的方式支付当期货款。
        6、竞价函报送要求：请将线上完成填报的竞价函导出打印，签字盖章后以PDF彩色扫描件上传至竞价专区，标题以“公司名称+**材料竞价”命名，报价截止时间为${year}年${month}月${day}日${minutes}分，逾期送达的视为报价无效。
        7、竞价函比价：竞价发起单位将于报价截止时间后组织比价，最终根据比价情况，由竞价发起单位采购领导小组选择确定供应商。
        8、其他说明：`
        },
        formatInventoryStr () {
            let year = this.inventoryBidForm.endTime.substring(0, 4)
            let month = this.inventoryBidForm.endTime.substring(5, 7)
            let day = this.inventoryBidForm.endTime.substring(8, 10)
            let minutes = this.inventoryBidForm.endTime.substring(11, 16)
            // 固定模板
            if (this.inventoryBidForm.billType === 2) {
                this.inventoryBidForm.biddingNotice = `说明：
备注：
1、本表中各材料数量、金额为暂估数量，结算以实际供应并经双方签认的数量和金额为准。
2、“到场含税单价”为材料到达供货地点的含税单价，包含但不限于材料费、运杂费、装车、捆扎、利润、管理费、安全费、税费等一切费用（卸车由买方负责并承担相关费用）；
3、材料执行标准：满足国家、行业相关标准文件规定以及项目施工设计文件要求；
4、发票开具均采用一票制。
5、结算与支付方式：先货后款，卖方全部货物到厂后需持双方收料签字确认的对账单与买方3日内完成对账（有特殊情况需双方协商），对账完成后乙方开具有效增值税专用发票，甲方收到增值税专用发票60个工作日内（有特殊情况需双方协商）向乙方付清已开票金额。
6、竞价函报送要求：请将完成填报的竞价函PDF彩色扫描件加密后（文档密码由报价方妥善保管）发送至四川路桥建设集团股份有限公司物资分公司企业邮箱**************，邮件标题以“公司名称+电话号码”命名，报价截止时间为${year}年${month}月${day}日${minutes}分，电子邮件逾期送达的视为报价无效。
7、竞价函启封比价：竞价发起单位将于报价截止时间后组织启封比价。届时，竞价发起单位将根据邮件标题预留电话，联系报价单位询问文件密码启封竞价函，请报价方保持电话畅通；最终根据比价情况，由竞价发起单位采购领导小组选择确定供应商。
8、报价方申明（若有必要）：`
            } else {
                this.inventoryBidForm.biddingNotice = `说明：
备注：
1、本表中各材料数量、金额为暂估数量，结算以实际供应并经双方签认的数量和金额为准。
2、网价参照《我的钢铁网》“成都市场”2023年12月20日流体管对应规格型号公布的最低价格报价（如无该规格型号报价，则选取相应报价），结算以到货当日对应规格型号网价+固定费，若到货当日无网价，按照到货当日接近日期最低网价执行；“到场含税单价”为材料到达供货地点的含税单价，包含但不限于材料费、运杂费、装车、捆扎、利润、管理费、安全费、税费等一切费用（卸车由买方负责并承担相关费用）；
3、材料执行标准：满足国家、行业相关标准文件规定以及项目施工设计文件要求
4、发票开具均采用一票制。
5、结算与支付方式：先货后款，卖方全部货物到厂后需持双方收料签字确认的对账单与买方3日内完成对账（有特殊情况需双方协商），对账完成后乙方开具有效增值税专用发票，甲方收到增值税专用发票60个工作日内（有特殊情况需双方协商）向乙方付清已开票金额。
6、竞价函报送要求：请将完成填报的竞价函PDF彩色扫描件加密后（文档密码由报价方妥善保管）发送至四川路桥建设集团股份有限公司物资分公司企业邮箱**************，邮件标题以“公司名称+电话号码”命名，报价截止时间为${year}年${month}月${day}日${minutes}分，电子邮件逾期送达的视为报价无效。
7、竞价函启封比价：竞价发起单位将于报价截止时间后组织启封比价。届时，竞价发起单位将根据邮件标题预留电话，联系报价单位询问文件密码启封竞价函，请报价方保持电话畅通；最终根据比价情况，由竞价发起单位采购领导小组选择确定供应商。
8、报价方申明（若有必要）：`
            }
        },
        agreeChange (row) {
            this.orderProductType = row
            // this.bidingFormOrderItems = []
            // if (this.bidingForm.productType == 1) {
            //     this.orderProductType = 13
            // }
            // if (this.bidingForm.productType == 0) {
            //     this.orderProductType = 10
            // }
            // if (row == 3) {
            //     this.bidingForm.productType = 3
            // }
        },
        // 分类点击
        // eslint-disable-next-line no-unused-vars
        classNodeClick (data, nodePath) {
            this.materialClassId = data.classId
            this.getMaterialTableList()
        },
        selectOrderItemAffirm () {
            if (
                this.yesSelectOrderItem == null ||
        this.yesSelectOrderItem.length == 0
            ) {
                return this.$message.error('未选择订单商品！')
            }
            if (this.bidingFormOrderItems.length == 0) {
                this.bidingFormOrderItems = this.yesSelectOrderItem
                this.yesSelectOrderItem = []
                this.showSelectOrderItem = false
            } else {
                for (let i = 0; i < this.yesSelectOrderItem.length; i++) {
                    let t = this.yesSelectOrderItem[i]
                    let flag = false
                    for (let j = 0; j < this.bidingFormOrderItems.length; j++) {
                        let t2 = this.bidingFormOrderItems[j]
                        if (t.orderItemId == t2.orderItemId) {
                            flag = true
                        }
                    }
                    if (!flag) {
                        this.bidingFormOrderItems.push(t)
                    }
                }
                this.yesSelectOrderItem = []
                this.showSelectOrderItem = false
            }
        },
        // selectMaterialClick () {
        //     if(this.materialSelectRow == null || this.materialSelectRow.length == 0) {
        //         return this.$message.error('未选择物资！')
        //     }
        //     let str = this.materialSelectRow[0].classNamePath.split('/')[0]
        //     let arr = []
        //     for (let i = 0; i < this.materialSelectRow.length; i++) {
        //         let t = this.materialSelectRow[i]
        //         // if(!(t.classNamePath.indexOf('低值易耗品') != -1 || t.classNamePath.indexOf('主要材料') != -1)) {
        //         //     return this.$message.error('只能选择低值易耗品或主要材料！')
        //         // }
        //         // if(str != t.classNamePath.split('/')[0]) {
        //         //     return this.$message.error('只能选择低值易耗品或主要材料其中一种物资')
        //         // }
        //         for (let i = 0; i < this.bidingFormOrderItems2.length; i++) {
        //             let t = this.bidingFormOrderItems2[i]
        //             if(t.classNamePath.indexOf(str) == -1) {
        //                 return this.$message.error('只能选择低值易耗品或主要材料其中一种物资')
        //             }
        //         }
        //         arr.push({
        //             classNamePath: t.classNamePath,
        //             productName: t.materialName,
        //             spec: t.spec,
        //             unit: t.unit,
        //             productSn: t.billNo,
        //             num: 1,
        //             productTexture: null,
        //             classId: t.classId,
        //             classPathName: t.classNamePath,
        //         })
        //     }
        //
        //     this.bidingFormOrderItems2.push(...arr)
        //     this.showDialog = false
        // },
        getMaterialTableList () {
            let params = {
                pageIndex: this.materialPaginationInfo.currentPage,
                pageSize: this.materialPaginationInfo.pageSize,
                isActive: 1,
            }
            if (this.materialFilterData.materialName != null) {
                params.materialName = this.materialFilterData.materialName
            }
            if (this.materialKeywords != null) {
                params.keyWord = this.materialKeywords.trim()
            }
            if (this.materialFilterData.spec != null) {
                params.spec = this.materialFilterData.spec
            }
            if (this.materialFilterData.unit != null) {
                params.unit = this.materialFilterData.unit
            }
            if (this.materialFilterData.materialNo != null) {
                params.materialNo = this.materialFilterData.materialNo
            }
            if (this.materialClassId != null) {
                params.classId = this.materialClassId
            }
            // if (this.stateOptionTitle != null) {
            //     params.isActive = this.stateOptionTitle
            // }
            this.materialTableLoading = true
            queryPageMaterialDtl(params)
                .then(res => {
                    this.materialTableData = res.list
                    this.materialPaginationInfo.total = res.totalCount
                    this.materialPaginationInfo.pageSize = res.pageSize
                    this.materialPaginationInfo.currentPage = res.currPage
                })
                .finally(() => (this.materialTableLoading = false))
        },
        addMaterialButClick () {
            this.showDialog = true
            this.getMaterialTableList()
        },
        createBidingM2 () {},
        handleSelectionChangeShop (list) {
            this.selectedSupplierRow = list || []
        },
        // 双击选择
        handleCurrentInventoryClickShop (row) {
            // 根据id排除已有的供应商
            for (let i = 0; i < this.selectedSupplierList.length; i++) {
                let t = this.selectedSupplierList[i]
                if (t.enterpriseId == row.enterpriseId) {
                    return this.$message.warning('该供应商已选择！')
                }
            }
            this.selectedSupplierList.push(row)
            this.$message.success('选择成功！')
        },
        deleteRow (row) {
            this.bidingFormOrderItems = this.bidingFormOrderItems.filter(t => {
                if (t.orderItemId == row.orderItemId) {
                    return false
                } else {
                    return true
                }
            })
        },
        // 取消选择
        handleRemoveCurrentClickShop (row) {
            // 从已选择的供应商数组中移除该供应商
            this.selectedSupplierList = this.selectedSupplierList.filter(
                t => t.enterpriseId != row.enterpriseId
            )
        },
        closeSupplierDialog () {
            this.showSupplierList = false
            this.selectedSupplierList = []
        },
        confirmSupplierDialog () {
            // console.log(this.selectedSupplierRow)
            // this.bidingForm.supplierId =  this.selectedSupplierRow.map(item=>{
            //     console.log(item)
            //     return  item.enterpriseId
            // })
            // 循环已选择的供应商列表
            this.selectedSupplierRow = this.selectedSupplierList || []
            this.selectedSupplierRowName = this.selectedSupplierRow.map(item => {
                return {
                    enterpriseId: item.enterpriseId,
                    enterpriseName: item.enterpriseName,
                    disabled: true,
                }
            })
            this.bidingForm.suppliers =
        this.selectedSupplierRow.map(item => {
            return {
                supplierId: item.enterpriseId,
                supplierName: item.enterpriseName,
                disabled: true,
            }
        }) || []
            if (this.inventoryBidFormVisible) {
                this.inventoryBidForm.suppliers =
          this.selectedSupplierRow.map(item => {
              return {
                  supplierId: item.enterpriseId,
                  supplierName: item.enterpriseName,
                  disabled: true,
              }
          }) || []
            }
            this.infoStr = '已选' + this.selectedSupplierRow.length + '个供应商'
            this.showSupplierList = false
        },
        sizeChange () {
            this.getshopLists()
        },
        currentChange () {
            this.getshopLists()
        },
        getShopList,
        async showSupplierDialog () {
            await this.getshopLists()
            this.showSupplierList = true
        },
        getshopLists () {
            let params = {
                page: this.shopList.paginationInfo.currentPage,
                limit: this.shopList.paginationInfo.pageSize,
                orderBy: this.filterData.orderBy,
            }
            if (this.shopList.keywords != null) {
                params.keywords = this.shopList.keywords
            }
            this.inventoryTableLoading = true
            getSupplierShopsList(params).then(res => {
                this.shopList.tableData = res.list || []
                this.shopList.paginationInfo.currentPage = res.currPage
                this.shopList.paginationInfo.pageSize = res.pageSize
                this.shopList.paginationInfo.total = res.totalCount
            })
            this.inventoryTableLoading = false
        },
        tableSelectM (value) {
            this.tableSelectRow = value
        },
        handleCurrentInventoryOrderItemClick (row) {
            for (let i = 0; i < this.yesSelectOrderItem.length; i++) {
                let t = this.yesSelectOrderItem[i]
                if (t.orderItemId == row.orderItemId) {
                    return this.$message.warning('该订单商品已选择！')
                }
            }
            this.yesSelectOrderItem.push(row)
        },
        removeSelectOrderItemList (row) {
            this.yesSelectOrderItem = this.yesSelectOrderItem.filter(
                t => t.orderItemId != row.orderItemId
            )
        },
        // 行点击
        handleCurrentInventoryClick (row) {
            // row.flag = !row.flag
            // this.$refs.eltableCurrentRow.toggleRowSelection(row, row.flag)
            for (let i = 0; i < this.materialSelectRow.length; i++) {
                let t = this.materialSelectRow[i]
                if (t.productSn == row.billNo) {
                    return this.$message.warning('该物资已选择！')
                }
            }
            this.materialSelectRow.push({
                classNamePath: row.classNamePath,
                productName: row.materialName,
                spec: row.spec,
                unit: row.unit,
                productSn: row.billNo,
                num: 1,
                productTexture: null,
                classId: row.classId,
                classPathName: row.classNamePath,
            })
        },
        // 选择当前行
        selectMaterialRow (value) {
            this.materialSelectRow = value
        },
        tableRowClickM (row) {
            row.flag = !row.flag
            this.$refs.tableRef.toggleRowSelection(row, row.flag)
        },
        createBidingM () {
            this.$refs.bidingFormRef.validate(valid => {
                if (valid) {
                    if (this.bidingFormOrderItems.length == 0) {
                        return this.$message.error('未选择订单商品！')
                    }
                    this.bidingForm.orderItems = this.bidingFormOrderItems
                    // 无需校验
                    // for (let i = 0; i < this.bidingForm.orderItems.length; i++) {
                    //     let t = this.bidingForm.orderItems[i]
                    //     if(t.maxPrice == null || t.maxPrice == 0 || t.maxPrice == '') {
                    //         return this.$message.error('商品：【' + t.productName + '】未设置最高单价！')
                    //     }
                    // }
                    // if (this.bidingForm.productType === 1) {
                    //     if(this.bidingForm.billType == null || this.bidingForm.billType == '') {
                    //         return this.$message.error('请选择价格类型！')
                    //     }
                    // }
                    // 邀请竞价必须选择供应商
                    if (this.bidingForm.type === 2) {
                        if (
                            this.bidingForm.suppliers == null ||
              this.bidingForm.suppliers.length == 0
                        ) {
                            return this.$message.error('请选择供应商！')
                        }
                    }
                    // maxPrice 为0或者为空提示确认
                    let arr = this.bidingForm.orderItems.filter(item => {
                        return item.maxPrice == '' || Number(item.maxPrice) == 0
                    })
                    console.log(arr)
                    if (arr.length > 0) {
                        this.$confirm(
                            '商品：【' +
                arr[0].productName +
                '】未设置最高单价，将不会进行限价，是否确认提交？',
                            '提示',
                            {
                                confirmButtonText: '确定',
                                cancelButtonText: '取消',
                                type: 'warning',
                            }
                        )
                            .then(() => {
                                this.clientPop('info', '您确定要生成竞价吗！', async () => {
                                    this.bidingFormLoading = true
                                    createBidingByOrder(this.bidingForm)
                                        .then(res => {
                                            if (res.code != null && res.code == 200) {
                                                this.$message.success('生成成功')
                                                this.getTableData()
                                                this.showBidingForm = false
                                                this.bidingForm = {
                                                    productType: 0,
                                                    biddingSourceType: 1,
                                                    title: null,
                                                    type: 1,
                                                    endTime: null,
                                                    linkName: null,
                                                    linkPhone: null,
                                                    biddingExplain: null,
                                                    orderItems: [],
                                                    suppliers: [],
                                                }
                                                this.selectedSupplierList = []
                                                this.bidingFormOrderItems = []
                                                this.orderProductType = 10
                                            }
                                        })
                                        .finally(() => {
                                            this.bidingFormLoading = false
                                        })
                                })
                            })
                            .catch(() => {
                                this.$message({
                                    type: 'info',
                                    message: '已取消提交',
                                })
                            })
                    } else {
                        this.clientPop('info', '您确定要生成竞价吗！', async () => {
                            this.bidingFormLoading = true
                            createBidingByOrder(this.bidingForm)
                                .then(res => {
                                    if (res.code != null && res.code == 200) {
                                        this.$message.success('生成成功')
                                        this.getTableData()
                                        this.showBidingForm = false
                                        this.bidingForm = {
                                            productType: 0,
                                            biddingSourceType: 1,
                                            title: null,
                                            type: 1,
                                            endTime: null,
                                            linkName: null,
                                            linkPhone: null,
                                            biddingExplain: null,
                                            orderItems: [],
                                            suppliers: [],
                                        }
                                        this.selectedSupplierList = []
                                        this.bidingFormOrderItems = []
                                        this.orderProductType = 10
                                    }
                                })
                                .finally(() => {
                                    this.bidingFormLoading = false
                                })
                        })
                    }
                } else {
                    this.$message({
                        message: '请检查非空输入框',
                        type: 'error',
                    })
                }
            })
        },
        // 关闭窗口
        closeDialog () {},
        // bidingOrderItemSelectM (value) {
        //     this.bidingOrderItemSelectRow = value
        // },
        // bidingOrderItemRowClickM (row) {
        //     row.flag = !row.flag
        //     this.$refs.bidingOrderItemRef.toggleRowSelection(row, row.flag)
        // },
        selectProductDia () {
            this.yesSelectOrderItem = []
            this.selectOrderItemTable = []
            this.showSelectOrderItem = true
            this.listBidingOrderListIdsM()
        },
        listBidingOrderItemsListM () {
            let params = {
                page: this.paginationInfo2.currentPage,
                limit: this.paginationInfo2.pageSize,
                productType: this.orderProductType,
            }
            if (this.keywords2 != null) {
                params.keywords = this.keywords2
            }
            if (this.bidingOrderItemState != null) {
                params.state = this.bidingOrderItemState
            }
            this.biddingOrderItemLoading = true
            listBidingOrderItemsList(params)
                .then(res => {
                    this.paginationInfo2.total = res.totalCount
                    this.paginationInfo2.pageSize = res.pageSize
                    this.paginationInfo2.currentPage = res.currPage
                    this.bidingFormOrderItems = res.list
                })
                .finally(() => {
                    this.biddingOrderItemLoading = false
                })
        },
        listBiddingOrderItemByOrderIdM () {
            let params = {
                page: this.selectOrderItemPage.currentPage,
                limit: this.selectOrderItemPage.pageSize,
                productType: this.orderProductType,
            }
            if (this.orderProductType == 13) {
                params.billType = this.bidingForm.billType
            }
            if (this.selectOrderItemKeywords != null) {
                params.keywords = this.selectOrderItemKeywords
            }
            if (this.orderId != null) {
                params.orderId = this.orderId
            }
            this.biddingorderItemSelectLoading = true
            listBidingOrderItemsList(params)
                .then(res => {
                    this.selectOrderItemPage.total = res.totalCount
                    this.selectOrderItemPage.pageSize = res.pageSize
                    this.selectOrderItemPage.currentPage = res.currPage
                    this.selectOrderItemTable = res.list
                })
                .finally(() => {
                    this.biddingorderItemSelectLoading = false
                })
        },
        biddingOrderListClick (row) {
            this.orderId = row.orderId
            this.listBiddingOrderItemByOrderIdM()
        },
        listBidingOrderListIdsM () {
            let params = {
                page: this.paginationOrderList.currentPage,
                limit: this.paginationOrderList.pageSize,
                productType: this.orderProductType,
            }
            if (this.orderSn != null) {
                params.orderSn = this.orderSn
            }
            if (this.orderProductType == 13) {
                params.billType = this.bidingForm.billType
            }
            this.biddingOrderListLoading = true
            listBidingOrderListIds(params)
                .then(res => {
                    this.paginationOrderList.total = res.totalCount
                    this.paginationOrderList.pageSize = res.pageSize
                    this.paginationOrderList.currentPage = res.currPage
                    this.bidingOrderList = res.list
                })
                .finally(() => {
                    this.biddingOrderListLoading = false
                })
        },
        createBidingClickM () {
            // 调用查询获取生成竞价
            this.showBidingForm = true
        },
        createBidingClickM2 () {
            // 调用查询获取生成竞价
            this.showBidingForm2 = true
        },
        submitBidingAudit () {
            // console.log('\\\\s')
            // console.log('111', this.showFlow)
            // if(this.showFlow) {
            //     // 构建变量
            //     let { token, userId, localOrgId } = this.userInfo
            //     if (!token || !userId || !localOrgId) return
            //     const proDefKey = 'Process_1709088862631'
            //     const initiator = userId
            //     let params = {
            //         proDefKey: proDefKey,
            //         initiator: initiator,
            //         businessKey: {
            //             userId: initiator,
            //             orgId: localOrgId,
            //             businessType: 'BidReleaseReview',
            //             businessId: this.tableSelectRow[0].biddingSn,
            //             processKey: proDefKey
            //         },
            //
            //     }
            //     startProcessByKey(params)
            // }
            if (this.tableSelectRow.length == 0) {
                return this.$message.error('未选择数据！')
            }
            let ids = this.tableSelectRow
                .filter(t => {
                    if (t.state == 0 || t.state == 2) {
                        return true
                    } else {
                        return false
                    }
                })
                .map(t => t.biddingId)
            if (ids.length == 0) {
                return this.$message.error('请选择待提交或审核失败数据！')
            }
            this.clientPop('info', '您确定要提交吗！', async () => {
                this.tableLoading = true
                submitBidingByIds(ids)
                    .then(res => {
                        if (res.code != null && res.code == 200) {
                            this.$message.success('提交成功')
                            this.getTableData()
                            this.tableSelectRow = []
                        }
                    })
                    .finally(() => {
                        this.tableLoading = false
                    })
            })
            // 提交工作流
        },
        // 详情
        handleView (row) {
            this.$router.push({
                path: '/supplierSys/bidManage/bidingListDetail',
                name: 'bidingListDetail',
                query: {
                    biddingSn: row.biddingSn,
                },
            })
        },
        resetSearchConditions () {
            this.filterData.orderSn = null
            this.filterData.title = null
            this.filterData.biddingSn = null
            this.filterData.createDate = []
            this.filterData.endDate = []
            this.filterData.state = null
            this.filterData.biddingState = null
            this.filterData.productType = null
            this.filterData.publicityState = null
        },
        resetSearchConditions2 () {
            this.materialFilterData.spec = null
            this.materialFilterData.unit = null
            this.materialFilterData.materialName = null
            this.materialFilterData.materialNo = null
        },
        // 高级搜索
        confirmSearch () {
            this.keywords = null
            this.getTableData()
            this.queryVisible = false
        },
        confirmSearch2 () {
            this.materialKeywords = null
            this.getMaterialTableList()
            this.materialQueryVisible = false
        },
        // 获取表格数据
        getTableData () {
            let params = {
                page: this.paginationInfo.currentPage,
                limit: this.paginationInfo.pageSize,
            }
            // 路由参数作为关键词
            if (this.$route.query.keywords != null) {
                this.keywords = this.$route.query.keywords
            }
            if (this.keywords != null && this.keywords != '') {
                params.keywords = this.keywords
            }
            if (this.filterData.orderSn != null) {
                params.orderSn = this.filterData.orderSn
            }
            if (this.filterData.title != null) {
                params.title = this.filterData.title
            }
            if (this.filterData.biddingSn != null) {
                params.biddingSn = this.filterData.biddingSn
            }
            if (this.filterData.createDate != null) {
                params.createStartDate = this.filterData.createDate[0]
                params.createEndDate = this.filterData.createDate[1]
            }
            if (this.filterData.endDate != null) {
                params.startDate = this.filterData.endDate[0]
                params.endDate = this.filterData.endDate[1]
            }
            if (this.filterData.state != null) {
                params.state = this.filterData.state
            }
            if (this.filterData.biddingState != null) {
                params.biddingState = this.filterData.biddingState
            }
            if (this.filterData.productType != null) {
                params.productType = this.filterData.productType
            }
            if (this.filterData.publicityState != null) {
                params.publicityState = this.filterData.publicityState
            }
            if (this.filterData.orderBy != null) {
                params.orderBy = this.filterData.orderBy
            }
            if (this.filterData.type != null) {
                params.type = this.filterData.type
            }
            let { mallRoles, shopName } = this.userInfo
            if (shopName === '四川路桥自营店' && this.showDevFunc) {
                let lg = mallRoles.some(item => item.name === '大宗临购管理人员')
                if (lg) params.productType = 1
                let lx = mallRoles.some(item => item.name === '零星采购管理人员')
                if (lx) params.productType = 0
                if (lg && lx) params.productType = null
            }
            this.tableLoading = true
            listMyCreateBiding(params)
                .then(res => {
                    this.paginationInfo.total = res.totalCount
                    this.paginationInfo.pageSize = res.pageSize
                    this.paginationInfo.currentPage = res.currPage
                    this.tableData = res.list
                    this.tableLoading = false
                })
                .catch(() => {
                    this.tableLoading = false
                })
        },
        // 查询
        handleInputSearch () {
            this.resetSearchConditions()
            this.getTableData()
        },
        handleInputSearch2 () {
            this.resetSearchConditions2()
            this.getMaterialTableList()
        },
        getScreenInfo () {
            this.screenWidth =
        document.documentElement.clientWidth || document.body.clientWidth
            this.screenHeight =
        document.documentElement.clientHeight || document.body.clientHeight
        },
    },
}
</script>

<style lang="scss" scoped>
/deep/ input::-webkit-outer-spin-button,
/deep/ input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
}

/deep/ input[type='number'] {
  -moz-appearance: textfield !important;
}

.base-page .left {
  min-width: 200px;
  padding: 0;
}

.base-page {
  width: 100%;
  height: 100%;
}

/deep/ .el-dialog {
  .el-dialog__body {
    //height: 380px;
    margin-top: 0px;
  }
}

.e-table {
  min-height: auto;
}

.action {
  margin-right: 10px;
  color: #2e61d7;
  cursor: pointer;
}

/deep/ .el-input--suffix .el-input__inner {
  padding-right: 5px;
}

/deep/ .el-dialog__body {
  margin-top: 0;
}

/deep/ #bidingDialog {
  .el-dialog__body {
    height: 680px;
    margin-top: 0px;
  }
}
/deep/ #inventorybidingDialog {
  .el-dialog__body {
    height: 680px;
    margin-top: 0px;
  }
}
/deep/ .el-dialog.dlg {
  height: 600px;

  .el-dialog__header {
    margin-bottom: 0;
  }

  .el-dialog__body {
    height: 474px;
    margin: 10px;
    display: flex;

    & > div {
      .e-pagination {
        background-color: unset;
      }

      //height: 670px;
      .title {
        height: 22px;
        margin-bottom: 10px;
        padding-left: 26px;
        text-align: left;
        line-height: 22px;
        color: #2e61d7;
        font-weight: bold;
        position: relative;
        display: flex;

        &::before {
          content: '';
          display: block;
          width: 10px;
          height: inherit;
          border-radius: 5px;
          background-color: blue;
          position: absolute;
          left: 10px;
          top: 0;
        }
      }
    }

    .el-input__inner {
      border: 1px solid blue;
      border-radius: 6px;
    }

    .el-input__suffix {
      width: 20px;
    }

    .e-table {
      flex-grow: 1;

      .table {
        height: 100%;
      }
    }

    .box-left {
      width: 260px;
      display: flex;
      flex-direction: column;

      .search {
        padding: 0 10px;
      }
    }

    .box-right {
      flex-grow: 1;
      display: flex;
      flex-direction: column;

      & > div {
        display: flex;
        flex-direction: column;
      }

      .top {
        justify-content: left;
        height: 374px;
        margin: 0;
        border-radius: 0;
        box-shadow: unset;
      }

      .bottom {
        flex-grow: 1;
      }
    }
  }

  .el-dialog__footer {
    background-color: #eff2f6;
  }
}

.search_box1 {
  display: flex;
  justify-content: flex-end;
  width: 100%;
  align-items: center;
  gap: 10px;
  background: #fff;
  height: 55px;
  margin: 0;
  border-radius: 0;
  box-shadow: none;
}
.action {
  margin-right: 10px;
  color: #2e61d7;
  cursor: pointer;
}
/deep/ input[type='number'] {
  -moz-appearance: textfield !important;
}

/deep/ input::-webkit-outer-spin-button,
/deep/ input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
}
/deep/ #supplierDialog2 {
  .el-dialog__body {
    height: 600px;
    margin-top: 0px;
  }
}
/deep/ .el-button {
  padding: 0 0px;
}
</style>
