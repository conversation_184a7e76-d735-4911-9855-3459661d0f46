<template>
    <div class="base-page">
        <div class="e-form">
            <!--            浮动-->
            <BillTop @cancel="handleClose"></BillTop>
            <div class="tabs warningTabs" style="padding-top: 70px;" v-loading="formLoading">
                <el-tabs tab-position="left" v-model="tabsName" @tab-click="onChangeTab">
                    <el-tab-pane label="对账单详情" name="baseInfo" :disabled="clickTabFlag">
                    </el-tab-pane>
                    <el-tab-pane label="对账单明细" name="reconciliationDtl" :disabled="clickTabFlag">
                    </el-tab-pane>
                    <el-tab-pane label="审核历史" name="auditRecords" :disabled="clickTabFlag"/>
                    <div id="tabs-content">
                        <!-- 基本信息 -->
                        <div id="baseInfo" class="con">
                            <div class="tabs-title" id="baseInfo">对账单详情</div>
                            <el-form :model="reconciliationForm" label-width="200px" :rules="reconciliationFormRules"
                                     ref="reconciliationFormRef" :disabled="false" class="demo-ruleForm">
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="对账单编号：" prop="reconciliationNo">
                                            <span>{{ reconciliationForm.billNo }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="创建时间：" prop="gmtCreate">
                                            <span>{{ reconciliationForm.gmtCreate }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="对账时间：">
                                            <el-date-picker
                                                disabled
                                                value-format="yyyy-MM-dd HH:mm:ss"
                                                v-model="reconciliationForm.startEndTme"
                                                type="daterange"
                                                range-separator="至"
                                                start-placeholder="开始日期"
                                                end-placeholder="结束日期">
                                            </el-date-picker>
                                        </el-form-item>
                                    </el-col>

                                    <el-col :span="12">
                                        <el-form-item label="新增来源：" prop="type">
                                            <el-tag v-if="reconciliationForm.createType == 1">自营店新增</el-tag>
                                            <el-tag v-if="reconciliationForm.createType == 2">二级供货商新增</el-tag>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="自营店是否确认：" prop="gmtCreate">
                                            <span v-show="reconciliationForm.supplierIsAffirm===0">未确认</span>
                                            <span v-show="reconciliationForm.supplierIsAffirm===1">确认</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="二级供应商是否确认：" prop="gmtCreate">
                                            <span v-show="reconciliationForm.twoSupplierIsAffirm===0">未确认</span>
                                            <span v-show="reconciliationForm.twoSupplierIsAffirm===1">确认</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="单位名称：" prop="supplierName">
                                            <span>{{ reconciliationForm.supplierName }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="二级供应商：" prop="purchasingOrgName">
                                            <span>{{ reconciliationForm.twoSupplierName }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="含税总金额：" prop="reconciliationAmount">
                                            <span>{{ reconciliationForm.rateAmount }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="不含税总金额：" prop="reconciliationNoRateAmount">
                                            <span>{{ reconciliationForm.noRateAmount }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <!--<el-col :span="12">-->
                                    <!--    <el-form-item label="对账时间：">-->
                                    <!--        <el-date-picker-->
                                    <!--            disabled-->
                                    <!--            value-format="yyyy-MM-dd HH:mm:ss"-->
                                    <!--            v-model="reconciliationForm.startEndTme"-->
                                    <!--            type="daterange"-->
                                    <!--            range-separator="至"-->
                                    <!--            start-placeholder="开始日期"-->
                                    <!--            end-placeholder="结束日期">-->
                                    <!--        </el-date-picker>-->
                                    <!--    </el-form-item>-->
                                    <!--</el-col>-->
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="税率：" prop="taxRate">
                                            <el-input-number :precision="2"  :min=0 :max=100   v-if="reconciliationForm.state!=3&&reconciliationForm.state!=4" type="number" style="min-width: 100px;width: 120px" v-model="reconciliationForm.taxRate"></el-input-number>
                                            <span v-else>{{reconciliationForm.taxRate}}</span>
                                            %
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="状态：" prop="state">
                                            <el-tag type="info" v-if="reconciliationForm.state == 5">草稿</el-tag>
                                            <el-tag v-if="reconciliationForm.state == 1">已提交</el-tag>
                                            <el-tag v-if="reconciliationForm.state == 2">待审核</el-tag>
                                            <el-tag type="success" v-if="reconciliationForm.state == 3">审核通过
                                            </el-tag>
                                            <el-tag type="danger" v-if="reconciliationForm.state == 4">审核不通过</el-tag>
                                            <el-tag type="danger" v-if="reconciliationForm.state == 7">已作废</el-tag>
                                        </el-form-item>
                                    </el-col>

                                </el-row>
                              <el-row>
                                <el-col :span="12">
                                  <el-form-item label="税额：" prop="taxAmount">
                                    <span>{{reconciliationForm.taxAmount}}</span>
                                  </el-form-item>
                                </el-col>
                              </el-row>
                                <el-row>
                                    <el-col :span="24">
                                        <el-form-item label="备注：" prop="remarks">
                                            <el-input style="width: 1000px;" type="textarea" :auto-resize="false"
                                                      v-model="reconciliationForm.remarks"
                                                      placeholder="请输入备注" maxlength="1000"
                                                      show-word-limit></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                            </el-form>
                        </div>
                        <!--计划清单-->
                        <div id="reconciliationDtl" class="con">
                            <div class="tabs-title" id="reconciliationDtl">对账单明细</div>
                            <div class="e-table" style="background-color: #fff">
                                <el-table
                                    ref="tableRef"
                                    border
                                    style="width: 100%"
                                    :data="tableData"
                                    :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
                                    :row-style="{ fontSize: '14px', height: '48px' }"
                                >
                                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                                    <el-table-column label="单据日期" prop="receivingDate" width="160">
                                        <template v-slot="scope">
                                            <span>{{scope.row.receivingDate.split(' ')[0]}}</span>
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="类型" width="120" fixed="left">
                                        <template v-slot="scope">
                                            <el-tag type="danger" v-show="scope.row.reconciliationType===2">退货对账</el-tag>
                                            <el-tag type="success" v-show="scope.row.reconciliationType===1">发货对账</el-tag>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="orderSn" label="订单号" width="220"/>
                                    <el-table-column prop="materialName" label="物资名称" width="200"/>
                                    <el-table-column prop="productName" label="商品名称" width="200"/>
                                    <el-table-column prop="spec" label="规格型号" width=""/>
                                    <el-table-column prop="unit" label="单位" width=""/>

                                    <el-table-column prop="quantity" label="已选数量" width="100">
                                    </el-table-column>
                                    <el-table-column
                                        v-if="reconciliationForm.type === 1" prop="netPrice" label="网价" width="100px"
                                    >
                                        <template v-slot="scope">
                                            <el-input  v-if="reconciliationForm.state === 5||reconciliationForm.state==0"
                                                type="number"
                                                v-model="scope.row.netPrice"
                                                @change="netPriceChangeM(scope.row)">
                                            </el-input>
                                            <span v-else>{{scope.row.netPrice}}</span>
                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                        v-if="reconciliationForm.type === 1" prop="fixationPrice" label="固定费"
                                        width="100px"
                                    >
                                        <template v-slot="scope">
                                            <el-input  v-if="reconciliationForm.state === 5||reconciliationForm.state==0"
                                                type="number"
                                                v-model="scope.row.fixationPrice"
                                                @change="fixationPriceChangeM(scope.row)">
                                            </el-input>
                                            <span v-else>{{scope.row.fixationPrice}}</span>
                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                        v-if="reconciliationForm.type === 2" prop="outFactoryPrice" label="出厂价"
                                        width="100px"
                                    >
                                        <template v-slot="scope">
                                            <el-input  v-if="reconciliationForm.state === 5||reconciliationForm.state==0"
                                                type="number"
                                                v-model="scope.row.outFactoryPrice"
                                                @change="outFactoryPriceChangeM(scope.row)">
                                            </el-input>
                                            <span v-else>{{scope.row.outFactoryPrice}}</span>
                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                        v-if="reconciliationForm.type === 2" prop="transportPrice" label="运杂费"
                                        width="100px"
                                    >
                                        <template v-slot="scope">
                                            <el-input  v-if="reconciliationForm.state === 5||reconciliationForm.state==0"
                                                type="number"
                                                v-model="scope.row.transportPrice"
                                                @change="transportPriceChangeM(scope.row)">
                                            </el-input>
                                            <span v-else>{{scope.row.transportPrice}}</span>
                                        </template>
                                    </el-table-column>

                                    <el-table-column prop="price" label="含税单价" width="100px"/>
                                    <el-table-column prop="noRatePrice" label="不含税单价" width="130px"/>
                                    <el-table-column prop="totalAmount" label="含税金额" width="100px"/>
                                    <el-table-column prop="noRateAmount" label="不含税金额" width="130px"/>
                                  <el-table-column prop="taxAmount" label="税额" width="120px"/>
                                    <!--                                    <el-table-column prop="settledAmount" label="已结算金额" width=""/>-->
                                    <el-table-column prop="remarks" label="备注" width="">
                                        <template v-slot="scope">
                                            <el-input  v-model="scope.row.remarks"></el-input>
                                        </template>
                                    </el-table-column>
                                </el-table>
                            </div>
                        </div>
                        <div id="auditRecords" class="con">
                            <div class="tabs-title" id="auditRecords">审核历史</div>
                            <div class="e-table" style="background-color: #fff">
                                <el-table
                                    border
                                    style="width: 100%"
                                    :data="reconciliationForm.auditRecords"
                                    :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
                                    :row-style="{ fontSize: '14px', height: '48px' }"
                                >
                                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                                    <el-table-column prop="auditType" label="审核类型" width="160">
                                        <template slot-scope="scope">
                                            <el-tag v-if="scope.row.auditType == 1">提交审核</el-tag>
                                            <el-tag v-if="scope.row.auditType == 2">变更审核</el-tag>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="founderName" label="审核人" width="200">
                                    </el-table-column>
                                    <el-table-column prop="gmtCreate" label="审核时间" width="160">
                                    </el-table-column>
                                    <el-table-column prop="auditResult" label="审核意见" width="">
                                    </el-table-column>
                                </el-table>
                            </div>
                        </div>
                    </div>
                </el-tabs>
            </div>
            <div class="buttons">
                <el-button type="primary"
                           v-if="reconciliationForm.state >0&& reconciliationForm.state <7"
                           @click="outPutExcelM">导出
                </el-button>
                <el-button type="primary"
                           v-if="reconciliationForm.state === 5  || reconciliationForm.state === 4"
                           @click="saveTwoSheetM">保存
                </el-button>
                <el-button type="primary"
                           class="btn-greenYellow"
                           v-if="reconciliationForm.state === 5"
                           @click="submintFromM()">保存并提交
                </el-button>
                <el-button
                    type="primary" class="btn-greenYellow" :disabled="disabledElement"
                    v-if="reconciliationForm.state === 3&&showDevFunc&&(reconciliationForm.invoiceState===0||reconciliationForm.invoiceState===3)"
                    @click="createInvoice"
                >开票</el-button>
                <el-button type="primary"
                           v-if="reconciliationForm.state === 3 && reconciliationForm.twoSupplierIsAffirm === 0"
                           @click="affirmTwoM()"
                >确认
                </el-button>

                <el-button type="primary" class="btn-delete"
                           v-if="reconciliationForm.state === 0 || reconciliationForm.state === 5||reconciliationForm.state===4"
                           @click="deleteOneM"
                >删除
                </el-button>
                <el-button @click="handleClose">返回</el-button>
            </div>
        </div>
    </div>
</template>
<script>
import $ from 'jquery'
import { toFixed } from '@/utils/common'
import '@/utils/jquery.scrollTo.min'
// eslint-disable-next-line no-unused-vars
import { getUuid, throttle, calculateYesTarRateAmount, calculateNotTarRateAmount } from '@/utils/common'
import {
    findByNo,
    update,
    saveAndUpdate,
    supplierReconciliationSupplierAffirm,
    deleteById, outputExcel, updateState, twoSupplierReconciliationSupplierAffirm
} from '@/api/reconciliation/twoReconciliation'

import { mapState } from 'vuex'
import { capitalsAndNum, taxRateItemAmount } from '@/utils/material_reconciliationUtils/compute'
import { twoReconciliationAmountM, twoReconciliationFloatAmountM } from '@/utils/material_reconciliationUtils/twoCompute'

export default {
    components: {},
    data () {
        return {
            reconciliationFormRules: {
                startEndTme: [
                    { required: true, message: '请选择对账时间', trigger: 'blur' },
                ],
                reconciliationProductType: [
                    { required: true, message: '请选择业务类型', trigger: 'blur' },
                ],
                taxRate: [
                    { required: true, message: '请输入公司税率', trigger: 'blur' },
                    { validator: this.validateNumber, trigger: 'blur' }
                ],
            },
            formLoading: false,
            yyyymmdd: '',
            productType: null,
            selectContractOrPlanLoading: false,
            showSelectContractOrPlan: false,
            contractOrPlanTableDate: [],
            keywords: null,
            paginationInfo: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            reconciliationForm: {
                billId: null,
                billNo: null,
                type: null,
                orderId: null,
                orderSn: null,
                businessType: null,
                reconciliationProductType: null,
                sourceBillId: null,
                sourceBillNo: null,
                supplierOrgId: null,
                twoSupplierOrgId: null,
                supplierEnterpriseId: null,
                supplierName: null,

                purchasingOrgName: null,
                acceptanceName: null,
                reconciliationAmount: null,
                taxAmount: null,
                reconciliationNoRateAmount: null,
                settleAmount: null,
                startTime: null,
                endTime: null,
                startEndTme: [],
                createType: null,
                // TODO 待完善

            },
            showReconciliationForm: false,
            reconciliationFormLoading: false,
            maxNum: *********,
            tableData: [],
            tabsName: 'baseInfo',
            screenWidth: 0,
            screenHeight: 0,
            lastConHeight: 0,
            clickTabFlag: false, // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            winEvent: {},
            topHeight: 120,
            tableLoading: false,
            changedRow: [],
        }
    },
    created () {
        this.supplierReconciliationGetBySnM()
    },
    computed: {
        ...mapState(['userInfo']),
        // tab内容高度
        tabsContentHeight () {
            return this.screenHeight - 140 + 'px'
        },
        // 填补底部空白，以使高度够滚动
        seatHeight () {
            return this.screenHeight - 72 - this.lastConHeight
        },
    },
    watch: {
        screenHeight: {
            handler (newVal) {
                $('#tabs-content').height(newVal - 71)
            }
        },
        'reconciliationForm.taxRate': {
            handler (value) {
                let taxAmount = 0
                let rateAmount = 0
                let noRateAmount = 0
                this.tableData.forEach(item => {
                    if (this.reconciliationForm.type == 1) {
                        item.price = this.fixed2(Number(item.fixationPrice) + Number(item.netPrice))
                    }else {
                        item.price = this.fixed2(Number(item.outFactoryPrice) + Number(item.transportPrice))
                    }

                    let prams = taxRateItemAmount(item.price, item.quantity, value)
                    item.noRatePrice = prams.noRatePrice
                    item.taxAmount = prams.taxAmount
                    item.totalAmount = prams.acceptanceAmount
                    item.noRateAmount =  prams.acceptanceNoRateAmount
                    taxAmount = this.fixed2(Number(taxAmount) + Number(item.taxAmount))
                    rateAmount = this.fixed2(Number(rateAmount) + Number(item.totalAmount))
                    noRateAmount = this.fixed2(Number(noRateAmount) + Number(item.noRateAmount))

                })
                this.reconciliationForm.rateAmount = rateAmount
                this.reconciliationForm.taxAmount = taxAmount
                // noRateAmount = calculateNotTarRateAmount(rateAmount,  value)
                this.reconciliationForm.noRateAmount = capitalsAndNum(rateAmount, noRateAmount,  value)
            },
        }
    },
    methods: {
        createInvoice () {
            let billIds = []
            billIds.push(this.reconciliationForm.billId)
            this.$router.push({
                path: '/supplierSys/twoSupplierApply/twoSupplierApply',
                //name后面跟跳转的路由名字（必须有亲测，不使用命名路由会传参失败）
                name: 'twoSupplierApply',
                params: {
                    row: {
                        billIds: billIds,
                    }
                }
            })
        },
        netPriceChangeM (row) {

            this.disposeFreightPriceM(row)
            // 处理固定费用
            this.disposeFixationPriceM(row)
            row.price = this.fixed2(Number(row.netPrice) + Number(row.fixationPrice))
            this.countAmountFM()
        },
        disposeFreightPriceM (row) {
            if (row.netPrice <= 0 || row.netPrice >= this.maxNum) {
                row.netPrice = this.fixed2(0)
            } else {
                row.netPrice = this.fixed2(row.netPrice)
            }
        },
        disposeFixationPriceM (row) {
            if (row.fixationPrice <= 0 || row.fixationPrice >= this.maxNum) {
                row.fixationPrice = this.fixed2(0)
            } else {
                row.fixationPrice = this.fixed2(row.fixationPrice)
            }
        },
        fixationPriceChangeM (row) {
            this.disposeFreightPriceM(row)
            // 处理固定费用
            this.disposeFixationPriceM(row)
            row.price = this.fixed2(Number(row.netPrice) + Number(row.fixationPrice))
            this.countAmountFM()
        },
        countAmountFM () {
            let prams = twoReconciliationAmountM(this.tableData, this.reconciliationForm.taxRate)
            this.tableData = prams.tableData
            this.reconciliationForm.taxAmount = prams.taxAmount
            this.reconciliationForm.rateAmount = prams.reconciliationAmount
            this.reconciliationForm.noRateAmount = prams.reconciliationNoRateAmount
        },
        transportPriceChangeM (row) {
            this.distransportPriceChangeM(row)

            this.disoutFactoryPriceM(row)
            this.countAmountGM(row)
        },
        countAmountGM () {
            let prams = twoReconciliationFloatAmountM(this.tableData, this.reconciliationForm.taxRate)
            this.tableData = prams.tableData
            this.reconciliationForm.taxAmount = prams.taxAmount
            this.reconciliationForm.rateAmount = prams.reconciliationAmount
            this.reconciliationForm.noRateAmount = prams.reconciliationNoRateAmount
        },
        outFactoryPriceChangeM (row) {
            this.disoutFactoryPriceM(row)
            this.distransportPriceChangeM(row)
            // 处理固定费用

            this.countAmountGM(row)
        },

        distransportPriceChangeM (row) {
            if (row.transportPrice <= 0 || row.transportPrice >= this.maxNum) {
                row.transportPrice = this.fixed2(0)
            } else {
                row.transportPrice = this.fixed2(row.transportPrice)
            }
        },
        // 处理固定费用
        disoutFactoryPriceM (row) {
            if (row.outFactoryPrice <= 0 || row.outFactoryPrice >= this.maxNum) {
                row.outFactoryPrice = this.fixed2(0)
            } else {
                row.outFactoryPrice = this.fixed2(row.outFactoryPrice)
            }
        },
        affirmTwoM () {
            this.clientPop('info', '您确定要确认数据吗？', async () => {
                this.formLoading = true
                twoSupplierReconciliationSupplierAffirm({ billId: this.reconciliationForm.billId }).then(res => {
                    if (res.code != null && res.code == 200) {
                        this.$message.success('操作成功')
                        this.$router.go(-1)
                    }
                }).finally(() => {
                    this.formLoading = false
                })
            })
        },
        submintFromM () {
            if (this.tableData.length === 0) {
                return this.$message.error('对账明细不能为空！')
            }
            if (this.reconciliationForm.type == 1) {
                for (let i = 0; i < this.tableData.length; i++) {
                    let t = this.tableData[i]
                    if (t.netPrice == 0.00 || t.netPrice == null) {
                        return this.$message.error('网价不能为0,请填写网网价！')
                    }
                }
            }else {
                for (let i = 0; i < this.tableData.length; i++) {
                    let t = this.tableData[i]
                    if (t.outFactoryPrice == 0.00 || t.outFactoryPrice == null) {
                        return this.$message.error('出厂价不能为0,请填写出厂价！')
                    }
                }
            }

            this.clientPop('info', '您确定要保存提交数据吗？', async () => {
                this.tableLoading = true
                this.reconciliationForm.state = 1
                this.$refs.reconciliationFormRef.validate(valid => {
                    if (valid) {
                        saveAndUpdate(this.reconciliationForm).then(res => {
                            if (res.code != null && res.code == 200) {
                                this.$message.success('操作成功')
                                this.supplierReconciliationGetBySnM()
                            }
                        }).finally(() => {
                            this.tableLoading = false
                        })
                    }})

            })
        },
        validateNumber (rule, value, callback) {
            if (value < 0 || value >= 100) {
                callback(new Error('供应商税率不能超过100'))
            } else {
                callback()
            }
        },
        saveAndUpdateM () {
            this.clientPop('info', '您确定要提交数据吗？', async () => {
                this.tableLoading = true
                this.reconciliationForm.state = 2
                this.$refs.reconciliationFormRef.validate(valid => {
                    if (valid) {
                        saveAndUpdate( this.reconciliationForm).then(res => {
                            if (res.code != null && res.code == 200) {
                                this.$message.success('操作成功')
                                this.supplierReconciliationGetBySnM()
                            }
                        }).finally(() => {
                            this.tableLoading = false
                        })
                    }})

            })
        },
        updateStateM (state) {
            this.clientPop('info', '您确定要提交审核数据吗？', async () => {
                this.tableLoading = true
                updateState({ billId: this.reconciliationForm.billId, state: state }).then(res => {
                    if (res.code != null && res.code == 200) {
                        this.$message.success('操作成功')
                        this.supplierReconciliationGetBySnM()
                        this.$router.go(-1)
                    }
                }).finally(() => {
                    this.tableLoading = false
                })
            })
        },
        outPutExcelM () {
            this.formLoading = true
            const now = this.yyyymmdd
            const date = now.getDate()  // 获取月份，返回值范围是 0-11，因此需要加 1
            const month = now.getMonth() + 1 // 获取月份，返回值范围是 0-11，因此需要加 1
            const year = now.getFullYear()  // 获取月份，返回值范围是 0-11，因此需要加 1
            outputExcel({ billId: this.reconciliationForm.billId }).then(res => {
                const blob = new Blob([res], { type: 'application/vnd.ms-excel' })
                const url = window.URL.createObjectURL(blob)
                const a = document.createElement('a')
                a.href = url
                a.download = year + '年' + month + '月' + date + '日' + this.reconciliationForm.twoSupplierName + '物资供货对账单.xlsx'
                a.click()
                window.URL.revokeObjectURL(url)
                this.$message.success('操作成功')
            }).finally(() => {
                this.formLoading = false
            })
        },
        affirmM () {
            this.clientPop('info', '您确定要确认数据吗？', async () => {
                this.formLoading = true
                supplierReconciliationSupplierAffirm({ billId: this.reconciliationForm.billId }).then(res => {
                    if (res.code != null && res.code == 200) {
                        this.$message.success('操作成功')
                        this.$router.go(-1)
                    }
                }).finally(() => {
                    this.formLoading = false
                })
            })
        },
        deleteOneM () {
            this.clientPop('info', '您确定要删除数据吗？', async () => {
                this.formLoading = true
                deleteById({ id: this.reconciliationForm.billId }).then(res => {
                    if (res.code != null && res.code == 200) {
                        this.$message.success('删除成功')
                        this.$router.go(-1)
                    }
                }).finally(() => {
                    this.formLoading = false
                })
            })
        },
        supplierReconciliationGetBySnM () {
            this.formLoading = true
            findByNo({ billNo: this.$route.query.billNo }).then(res => {
                if (res != null) {
                    this.tableData = res.dtl
                    this.reconciliationForm = res
                    let timeArr = this.tableData.map(item => {
                        return new Date(item.receivingDate)
                    })
                    this.yyyymmdd = new Date(Math.max.apply(null, timeArr))
                    this.reconciliationForm.startEndTme = [res.startTime, res.endTime]
                }
            }).finally(() => {
                this.formLoading = false
            })
        },
        saveTwoSheetM () {
            if (this.tableData.length === 0) {
                return this.$message.error('对账明细不能为空！')
            }
            if (this.reconciliationForm.type == 1) {
                for (let i = 0; i < this.tableData.length; i++) {
                    let t = this.tableData[i]
                    if (t.netPrice == 0.00 || t.netPrice == null) {
                        return this.$message.error('网价不能为0,请填写网网价！')
                    }
                }
            }else {
                for (let i = 0; i < this.tableData.length; i++) {
                    let t = this.tableData[i]
                    if (t.outFactoryPrice == 0.00 || t.outFactoryPrice == null) {
                        return this.$message.error('出厂价不能为0,请填写出厂价！')
                    }
                }
            }
            this.reconciliationForm.dtl = this.tableData
            this.$refs.reconciliationFormRef.validate(valid => {
                if (valid) {
                    this.clientPop('info', '您确定要保存吗！', async () => {
                        this.formLoading = true
                        // 处理日期
                        this.reconciliationForm.startTime = this.reconciliationForm.startEndTme[0]
                        this.reconciliationForm.endTime = this.reconciliationForm.startEndTme[1]
                        this.reconciliationForm.state = 5
                        update(this.reconciliationForm).then(res => {
                            if (res.code != null && res.code == 200) {
                                this.$message.success('保存成功')
                                this.supplierReconciliationGetBySnM()
                            }
                        }).finally(() => {
                            this.formLoading = false
                        })
                    })
                }
            })
        },
        fixed4 (num) {
            return toFixed(num, 4)
        },
        fixed2 (num) {
            return toFixed(num, 2)
        },

        // 获取最后一个内容区域的高度，计算底部空白
        getLastConHeight () {
            let si = setInterval(() => {
                // 因为dom异步加载，通过轮询找到渲染后的dom，获取高度
                if (document.getElementById('terminationLog')) {
                    this.lastConHeight = document.getElementById('terminationLog').offsetHeight
                    clearInterval(si)
                    si = null
                }
            }, 100)
        },
        // 返回
        handleClose () {
            this.$router.go(-1)
        },
        onChangeTab (e) {
            const height = $('#' + e.name).offset().top
            try {
                $('#tabs-content').scrollTo(height - this.topHeight, 500)
            } catch (err) {
                console.log(err)
            }
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            this.clickTabFlag = true
            // 动画结束后，恢复状态
            setTimeout(() => {
                this.clickTabFlag = false
            }, 600)
        },
        beforeDestroy () {
            window.removeEventListener('scroll', this.winEvent.onScroll)
            window.removeEventListener('resize', this.winEvent.onResize)
        },
    },
    mounted () {
        // 获取数据
        // 获取最后一个内容区域的高度，计算底部空白
        this.getLastConHeight()
        // 保存所有tabName
        const arr = ['baseInfo', 'reconciliationDtl', 'auditRecords']
        this.tabArr = arr
        let $idsTop = []
        this.winEvent.onScroll = () => {
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            if (this.clickTabFlag) {
                return
            }
            if (!$idsTop[$idsTop.length - 1]) {
                $idsTop = arr.map(item => {
                    const $item = document.getElementById(item)
                    let itemTop = null
                    if ($item) {
                        itemTop = $item.offsetTop
                    }
                    return itemTop
                })
            }
            const scrollTop = $('#tabs-content')[0].scrollTop
            // 倒序查找
            let curLocal = 0
            for (let i = $idsTop.length - 1; i >= 0; i--) {
                let item = $idsTop[i]
                if (scrollTop + 1 >= item) {
                    curLocal = i
                    break
                }
            }
            // 设置对应tabName
            this.tabsName = arr[curLocal]
        }
        $('#tabs-content')[0].addEventListener('scroll', this.winEvent.onScroll)
        this.screenWidth = document.documentElement.clientWidth - this.topHeight
        this.screenHeight = document.documentElement.clientHeight - this.topHeight
        const onResize = () => {
            this.screenWidth = document.documentElement.clientWidth - this.topHeight
            this.screenHeight = document.documentElement.clientHeight - this.topHeight
            $idsTop = arr.map(item => {
                return document.getElementById(item).offsetTop
            })
        }
        this.winEvent.onResize = throttle(onResize, 500)
        window.addEventListener('resize', this.winEvent.onResize)
        if (this.$route.query.name) {
            setTimeout(() => {
                this.onChangeTab({ name: this.$route.query.name })
                this.tabsName = this.$route.query.name
            }, 200)
        }
    },
}
</script>

<style lang='scss' scoped>
.base-page {
    flex-grow: 1;
}
.e-table {
    min-height: auto;
    background: #fff;
}

/deep/ .e-form .el-form-item .el-form-item__content {
    height: unset !important;
    display: flex;
    align-items: center;
}

.action span {
    color: #216EC6;
    cursor: pointer;
    user-select: none;

    &:not(:last-of-type) {
        margin-right: 15px;
    }
}

::v-deep input::-webkit-outer-spin-button,
::v-deep input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}

::v-deep input[type=‘number’] {
    -moz-appearance: textfield !important;
}

/deep/ .el-dialog {
    .el-dialog__body {
        height: 500px;
        margin-top: 0px;
    }
}

</style>