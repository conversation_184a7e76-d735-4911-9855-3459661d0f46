<template>
  <div class="e-form">
    <BillTop @cancel="handleClose"/>
    <div v-loading='formLoading' class="tabs warningTabs" style="padding-top: 70px;">
      <el-tabs v-model="tabsName" tab-position="left" @tab-click="onChangeTab">
        <el-tab-pane :disabled="clickTabFlag" label="出库单详情" name="baseInfo">
        </el-tab-pane>
        <el-tab-pane :disabled="clickTabFlag" label="出库结算单明细" name="baseInfos">
        </el-tab-pane>
        <el-tab-pane :disabled="clickTabFlag" v-if="viewType != 'add'" label="审核记录" name="auditRecords">
        </el-tab-pane>
        <div id="tabs-content">
          <div id="baseInfCon" class="con">
            <div id="baseInfo" class="tabs-title">出库结算单详情</div>
            <!--新增-->
            <div v-if="showForm" class="form">
              <el-form
                  ref="formEdit" :model="addForm.formData" :rules="formRules"
                  class="demo-ruleForm" label-width="200px"
              >
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="业务类型：" prop="supplierType">
                      <el-radio-group v-model="addForm.formData.supplierType" style="width: 100%">
                        <el-col :span="8" v-for="item in supplierTypeList" :key="item.value">
                          <el-radio :label="item.value" :value="item.value" :key="item.value">{{
                              item.label
                            }}
                          </el-radio>
                        </el-col>
                      </el-radio-group>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="出库时间：" prop="outWarehouseTime">
                      <el-date-picker
                          v-model="addForm.formData.outWarehouseTime"
                          type="datetime"
                          placeholder="选择日期时间"
                          default-time="12:00:00">
                      </el-date-picker>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="收货单位：" prop="purchasingOrgName">
                      <el-input placeholder="请选择收货单位" disabled
                                v-model="addForm.formData.purchasingOrgName"/>
                      <el-button
                          :disabled="addForm.formData.supplierType == null"
                          size="mini" type="primary" @click="selectedEnterprise">选择收货单位
                      </el-button>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="选择物资：" prop="productName">

                      <el-input
                          disabled
                          v-model="addForm.formData.productName"
                          placeholder="请选择物资"
                      ></el-input>
                      <el-button
                          size="mini" type="primary"
                          @click="importDeviceSelect"
                      >选择
                      </el-button>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="库房选择：" prop="warehouseId">
                      <el-select
                          v-model="addForm.formData.warehouseId" filterable placeholder="请选择库房"
                      >
                        <el-option
                            v-for="item in warehouseList" :key="item.value"
                            :label="item.label" :value="item.value"
                        >
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="合同编号：" prop="projectAddress">
                      <el-input
                          disabled
                          v-model="addForm.formData.contractNo"
                      ></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="销售含税总价：">
                      <el-input
                          v-model="addForm.formData.xsRateAmount" clearable
                          oninput="if(value.length > 16)value = value.slice(0, 16)"
                          type="number"
                      ></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="销售不含税总价：">
                      <el-input
                          v-model="addForm.formData.xsNoRateAmount" clearable
                          oninput="if(value.length > 16)value = value.slice(0, 16)"
                          type="number"
                      ></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="采购含税进价：">
                      <el-input
                          v-model="addForm.formData.cgRateAmount" clearable
                          oninput="if(value.length > 16)value = value.slice(0, 16)"
                          type="number"
                      ></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="采购不含税进价：">
                      <el-input
                          v-model="addForm.formData.cgNoRateAmount" clearable
                          oninput="if(value.length > 16)value = value.slice(0, 16)"
                          placeholder="请输入不含税总金额"
                          type="number"
                      ></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="项目名称：">
                      <el-input
                          disabled
                          v-model="addForm.formData.projectName"
                      ></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="项目地址：">
                      <el-input
                          disabled
                          v-model="addForm.formData.projectAddress"
                      ></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="数量：">
                      <el-input
                          disabled
                          v-model="addForm.formData.num"
                      ></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="期数：">
                      <el-input
                          disabled
                          v-model="addForm.formData.periodsNum"
                          placeholder="请输入期数"
                      ></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="供应商发票是否开具：">
                      <el-radio-group v-model="addForm.formData.invoiceIssueIs">
                        <el-radio :label="1">是</el-radio>
                        <el-radio :label="0">否</el-radio>
                      </el-radio-group>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="申请开票日期：">
                      <el-date-picker
                          v-model="addForm.formData.applyInvoicingTime"
                          type="datetime"
                          placeholder="选择日期时间"
                          default-time="12:00:00">
                      </el-date-picker>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="受票单位：">
                      <el-input
                          disabled
                          v-model="addForm.formData.ticketReceivingUnit"
                          placeholder="请输入受票单位"
                      ></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="受票单位地址：">
                      <el-input
                          disabled
                          v-model="addForm.formData.ticketReceivingUnitAddress"
                          placeholder="请输入受票单位地址"
                      ></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="受票单位税号：">
                      <el-input
                          disabled
                          v-model="addForm.formData.ticketReceivingUnitTaxNo"
                          placeholder="请输入受票单位税号"
                      ></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="受票单位电话：">
                      <el-input
                          disabled
                          v-model="addForm.formData.ticketReceivingUnitPhone"
                          placeholder="请输入受票单位电话"
                      ></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="受票单位开户行：">
                      <el-input
                          disabled
                          v-model="addForm.formData.ticketReceivingUnitBank"
                          placeholder="请输入受票单位开户行"
                      ></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="受票单位账号：">
                      <el-input
                          disabled
                          v-model="addForm.formData.ticketReceivingUnitAccount"
                          placeholder="请输入受票单位账号"
                      ></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </div>
            <div id="baseInfos" class="tabs-title">出库结算单明细</div>
            <div class="e-table">
              <el-table
                  @row-click="handleCurrentInventoryClick" ref="tableRef" class="table"
                  :height="rightTableHeight" :data="tableData" border highlight-current-row
                  v-loading="tableLoading"
                  @current-change="handleCurrentChange" @selection-change="handleSelectionChange"
              >
                <el-table-column label="序号" type="index" width="60"></el-table-column>
                <el-table-column label="物资名称" type="index" width="60" prop="productName"></el-table-column>
                <el-table-column label="规格型号" prop="spec"/>
                <el-table-column label="计量单位" prop="unit"/>
                <el-table-column label="数量" prop="num"/>
                <el-table-column label="销售含税单价" prop="price"/>
                <el-table-column label="销售含税金额" prop="acceptanceAmount"/>
                <el-table-column label="销售不含税金额" prop="acceptanceNoRateAmount"/>
                <el-table-column label="客户" prop="operationTime"/>
                <el-table-column label="采购含税单价" prop="price"/>
                <el-table-column label="采购含税金额" prop="noRatePrice"/>
                <el-table-column label="采购不含税金额" prop="operationType"/>
                <el-table-column label="供应商" prop="supplierName"/>
              </el-table>
              <!-- 分页器 -->
              <Pagination
                  :total="pages.totalCount" :pageSize.sync="pages.pageSize"
                  :currentPage.sync="pages.currPage"
                  @currentChange="currentChange" @sizeChange="sizeChange"/>
            </div>
            <div id="auditRecords" class="tabs-title" v-if="viewType != 'add'">审核记录</div>
            <div class="e-table" style="background-color: #fff" v-if="viewType != 'add'">
              <el-table
                  border
                  style="width: 100%"
                  :data="auditRecordList"
                  :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
                  :row-style="{ fontSize: '14px', height: '48px' }"
              >
                <el-table-column label="序号" type="index" width="60"></el-table-column>
                <el-table-column prop="auditType" label="审核类型" width="220">
                </el-table-column>
                <el-table-column prop="auditPerson" label="审核人" width="220">
                </el-table-column>
                <el-table-column prop="userType" label="人员类型" width="220">
                </el-table-column>
                <el-table-column prop="auditTime" label="审核时间" width="230">
                </el-table-column>
                <el-table-column prop="founderName" label="审核意见" width="227">
                </el-table-column>
              </el-table>
            </div>
          </div>
        </div>
      </el-tabs>
      <div class="buttons">
        <el-button type="info" @click="submit">保存</el-button>
        <el-button type="info" @click="saveAndSubmit">保存并提交</el-button>
        <el-button @click="handleClose">返回</el-button>
      </div>
    </div>
    <!--选择物资库-->
    <el-dialog
        v-dialogDrag custom-class="dlg" width="90%" title="物资选择"
        :visible.sync="selectMaterialDialogShow"
    >
      <div class="box-left">
        <div class="e-table">
          <div class="top">
            <div style="width: 200px">
              <el-input type="text" @blur="getContractOrPlanListM" placeholder="输入搜索关键字" v-model="keywords">
                <img :src="require('@/assets/search.png')" slot="suffix" @click="getContractOrPlanListM"/>
              </el-input>
            </div>
          </div>
          <el-table ref="selectContractOrPlanR"
                    id="myTable"
                    border
                    :select-all="false"
                    max-height="340px"
                    @selection-change="selectContractOrPlanSelectM"
                    @row-click="selectContractOrPlanRowClickM"
                    :data="contractOrPlanTableDate"
                    v-loading="selectContractOrPlanLoading"
                    class="table"
          >
            <el-table-column type="selection" width="40"></el-table-column>
            <el-table-column label="序号" type="index" width="60"></el-table-column>
            <el-table-column prop="billNo" label="计划编号" width=""></el-table-column>
            <el-table-column prop="orderSn" label="订单编号" width=""></el-table-column>
            <el-table-column prop="orgName" label="收货单位名称" width=""></el-table-column>
            <el-table-column prop="gmtCreate" label="创建日期" width="">
              <template v-slot="scope">
                {{ scope.row.gmtCreate | dateStr }}
              </template>
            </el-table-column>
          </el-table>
          <Pagination
              v-if="contractOrPlanTableDate != null && contractOrPlanTableDate.length > 0"
              :total="paginationInfo.total"
              :pageSize.sync="paginationInfo.pageSize"
              :currentPage.sync="paginationInfo.currentPage"
              @currentChange="currentChangeUser"
              @sizeChange="sizeChangeUser"
          />
        </div>
      </div>
      <div class="box-right">
        <div class="e-table">
          <div class="top">
            <div style="width: 200px">
              <el-input type="text" @blur="getSiteReceivingTableDateM" placeholder="输入搜索关键字" v-model="keywords2">
                <img :src="require('@/assets/search.png')" slot="suffix" @click="getSiteReceivingTableDateM"/>
              </el-input>
            </div>
          </div>
          <el-table ref="siteReceivingTableRef"
                    v-loading="siteReceivingLoading"
                    border
                    max-height="390px"
                    @selection-change="siteReceivingTableSelectM"
                    @row-click="siteReceivingTableRowClickM"
                    :data="siteReceivingTableDate"
                    class="table"
          >
            <el-table-column type="selection" width="40"></el-table-column>
            <el-table-column label="序号" type="index" width="60"></el-table-column>
            <el-table-column prop="orderSn" label="订单编号" width=""></el-table-column>
            <el-table-column prop="materialName" label="物资名称" width=""></el-table-column>
            <el-table-column prop="spec" label="规格型号" width=""></el-table-column>
            <el-table-column prop="texture" label="材质" width=""></el-table-column>
            <el-table-column prop="unit" label="计量单位" width=""></el-table-column>
            <el-table-column prop="maxQuantity" label="剩余可对账数量" width=""></el-table-column>
            <!--                            <el-table-column prop="totalQuantity" label="总数量" width=""></el-table-column>-->
          </el-table>
          <Pagination
              v-show="siteReceivingTableDate != null && siteReceivingTableDate.length > 0"
              :total="paginationInfo2.total"
              :pageSize.sync="paginationInfo2.pageSize"
              :currentPage.sync="paginationInfo2.currentPage"
              @currentChange="currentChangeUser2"
              @sizeChange="sizeChangeUser2"
          />
        </div>
      </div>
      <span slot="footer">
                <el-button type="primary" @click="siteReceivingTableDateSelectAffirmClick">确认选择</el-button>
                <el-button @click="selectMaterialDialogShow = false">取消</el-button>
            </span>
    </el-dialog>
    <el-dialog
        v-dialogDrag :title="selectPurchasingOrgNameTitle" :visible.sync="showPurchasingOrgNameView" width="80%"
        style="margin-left: 10%;" :close-on-click-modal="false"
    >
      <div class="e-table" v-loading="selectPurchasingOrgNameLoading">
        <div class="top dfa" style="height: 50px; padding-left: 10px">
          <el-input
              style="width: 200px; " type="text" @blur="getOrderPlanByOrgNameListM"
              placeholder="输入搜索关键字" v-model="supplierKeywords"
          >
            <img :src="require('@/assets/search.png')" slot="suffix" @click="getOrderPlanByOrgNameListM"/>
          </el-input>
        </div>
        <el-table
            ref="selectTwoSupplierR"
            border
            :data="purchasingOrgNameTable"
            class="table"
            :max-height="$store.state.tableHeight"
        >
          <!--<el-table-column type="selection" width="40"></el-table-column>-->
          <el-table-column label="序号" type="index" width="60"/>
          <el-table-column label="操作">
            <template v-slot="scope">
              <div
                  class="pointer" style="color: rgba(33, 110, 198, 1);"
                  @click="submitOrgName(scope.row)"
              >选择收货单位
              </div>
            </template>
          </el-table-column>
          <el-table-column label="收货单位名称" prop="orgName"/>
        </el-table>
      </div>
      <!--分页-->
      <span slot="footer">
                    <Pagination
                        :total="paginationInfo1.total"
                        :pageSize.sync="paginationInfo1.pageSize"
                        :currentPage.sync="paginationInfo1.currentPage"
                        @currentChange="currentChangeUser"
                        @sizeChange="sizeChangeUser"
                    />
                </span>
    </el-dialog>

  </div>

</template>

<script>
import { regionData } from 'element-china-area-data'
import '@/utils/jquery.scrollTo.min'
import Pagination from '@/components/pagination/pagination'
import { mapState } from 'vuex'
import $ from 'jquery'
import { addImgUrl, getUuid, spliceImgUrl, throttle, } from '@/utils/common'
import { saveAndSubmitOutBound, saveOutBound, updateOutBound } from '@/api/supplierSys/stock/inBoundManage'
import { getEnterpriseInfoTaxRate } from '@/api/platform/shop/shopManager'
import { getList } from '@/api/platform/system/systemParam'
import { getContactSupplierPageList } from '@/api/reconciliation/reconciliation'
import {
    supplierGetEnterprisePageList
} from '@/api/reconciliation/reconciliation'
import { getReconcilableMaterialList, getReconciliablePlansBySupplier } from '@/api/orderShipDtl/orderShipDtl'
import { getInvoiceRecordInfo } from '@/api/supplierSys/invoice/invoiceRecord'

export default {
    data () {
        return {
            regionTableIndex: 1,
            addressData: regionData, // 地址数据
            economizeData: [], //只有省数据
            marketData: [], //省市数据
            zoneUpdateShow: false,
            zoneUpdateForm: {},
            zoneListLoading: false,
            zoneAbbr: {
                province: '',
                city: [],

            },
            selectMaterialDialogShow: false,
            cascaderOptions: {
                province: [],
                city: [],
                district: [],
            },
            auditRecordList: [],
            previewImg: '',
            uploadMax: 10,
            init: {
                inventory: {
                    state: 1,
                    productType: 0,
                },
            },
            pages: {
                totalCount: 0,
                currPage: 1,
                pageSize: 20,
            },
            uploadType: null,
            mainImg: '', // 主图
            smallImg: '', // 小图
            dialogImageUrl: '', // 主图
            // 数据加载
            formLoading: false,
            selectPurchasingOrgNameTitle: '',
            showPurchasingOrgNameView: false,
            selectPurchasingOrgNameLoading: false,
            supplierKeywords: '',
            keywords: '',
            keywords2: '',
            purchasingOrgNameTable: false,
            paginationInfo1: { // 分页
                total: 0,
                pageSize: 10,
                currentPage: 1,
            },
            reconciliationForm: {
                title: null,
                type: null,
                orderId: null,
                orderSn: null,
                businessType: null,
                reconciliationProductType: null,
                sourceBillId: null,
                sourceBillNo: null,
                supplierId: null,
                supplierEnterpriseId: null,
                supplierName: null,
                purchaserId: null,
                purchaserLocalId: null,
                purchaserName: null,
                purchasingOrgId: null,
                purchasingLocalOrgId: null,
                purchasingOrgName: null,
                acceptanceName: null,
                reconciliationAmount: null,
                taxAmount: null,
                reconciliationNoRateAmount: null,
                settleAmount: null,
                startTime: null,
                endTime: null,
                startEndTme: [],
                createType: 1,
                // 内外供应商信息
                creditCode: null,
                orgShort: null,
                supplierOrgId: null,
                taxRate: null,
                dtl: [],
            },
            brandTableLoading: false,
            inventoryTableLoading: false,
            formRules: {
                relevanceName: [
                    { required: true, message: '请选择名称', trigger: 'blur' },
                ],
                // brandName: [
                //     { required: true, message: '请选择品牌', trigger: 'blur' },
                // ],
                taxInPrice: [{ required: true, message: '请输入销售价格', trigger: 'change' }],
                classId: [
                    { required: true, message: '请选择分类', trigger: 'blur' },
                ],
                taxRate: [
                    { required: true, message: '请输入税率', trigger: 'blur' },
                ],
                productMaterial: [
                    { required: true, message: '请输入材质', trigger: 'blur' },
                ],
                annualizedRate: [
                    { required: true, message: '请输入年化率', trigger: 'blur' },
                ],
                // productMinPrice: [
                //     { required: true, message: '请输入最低价格', trigger: 'blur' },
                //     { pattern: /^([0-9]+[\d]*(.[0-9]{1,2})?)$/, message: '数据格式错误', trigger: 'blur' },
                // ],
                settlePrice: [
                    { required: true, message: '请输入结算价格', trigger: 'blur' },
                    { pattern: /^([0-9]+[\d]*(.[0-9]{1,2})?)$/, message: '数据格式错误', trigger: 'blur' },
                ],
                skuName: [
                    { required: true, message: '请输入规格', trigger: 'blur' },
                    { min: 1, max: 200, message: '超出范围', trigger: 'blur' }
                ],
                stock: [
                    { required: true, message: '请输入库存', trigger: 'blur' },
                    { pattern: /^([0-9]+[\d]*(.[0-9]{1,3})?)$/, message: '数据格式错误', trigger: 'blur' },
                ],
                costPrice: [
                    { required: true, message: '请输入成本价', trigger: 'blur' },
                    { pattern: /^([0-9]+[\d]*(.[0-9]{1,2})?)$/, message: '数据格式错误', trigger: 'blur' },
                ],
                unit: [
                    { required: true, message: '请选择计量单位', trigger: 'blur' },
                ],
                originalPrice: [
                    { required: true, message: '请输入原价', trigger: 'blur' },
                    { pattern: /^([0-9]+[\d]*(.[0-9]{1,2})?)$/, message: '数据格式错误', trigger: 'blur' },
                ],
                sellPrice: [
                    { required: true, message: '请输入销售价格', trigger: 'blur' },
                    { pattern: /^([0-9]+[\d]*(.[0-9]{1,2})?)$/, message: '数据格式错误', trigger: 'blur' },
                ],
                isZone: [
                    { required: true, message: '请输选择区域类型', trigger: 'blur' },
                ],
                // province: [
                //     { required: true, message: '请选择地址', trigger: 'blur' },
                // ],
                // detailedAddress: [
                //     { required: true, message: '请输入详细地址', trigger: 'blur' },
                //     { min: 1, max: 250, message: '超出范围', trigger: 'blur' }
                // ],
                adminFile: [
                    { required: true, message: '请上传文件', trigger: 'blur' },
                ],
                productFiles: [
                    { required: true, message: '请上传文件', trigger: 'blur' },
                ],
                minFile: [
                    { required: true, message: '请上传文件', trigger: 'blur' },
                ],
                productDescribe: [
                    { required: true, message: '请输入描述', trigger: 'blur' },
                ],
            },
            rowData: null, // 跳转过来的数据
            showForm: false,
            // 商品库
            inventory: {
                tableData: [],
                keyWord: null,
                classId: null,
                paginationInfo: { // 分页
                    total: 200,
                    pageSize: 20,
                    currentPage: 0,
                },
            },
            showDeviceDialog: false, // 商品库弹窗
            viewType: null,
            showBrandDialog: false, // 品牌弹窗
            uploadImgSize: 10, // 上传文件大小
            //表单数据
            formData: {},

            addForm: {
                formData: {
                    supplierType: null,
                    sourceBillId: null,
                    sourceBillNosourceBillNo: null,
                    outWarehouseTime: null,
                    supplierName: null,
                    purchasingOrgName: null,
                    contractNo: null,
                    supplierId: null,
                    warehouseId: 1,
                    cgNoRateAmount: null,
                    cgRateAmount: null,
                    xsNoRateAmount: null,
                    xsRateAmount: null,
                    projectName: null,
                    projectAddress: null,
                    periodsNum: null,
                    ticketReceivingUnit: null,
                    ticketReceivingUnitAddress: null,
                    ticketReceivingUnitAccount: null,
                    ticketReceivingUnitBank: null,
                    ticketReceivingUnitTaxNo: null,
                    ticketReceivingUnitPhone: null,
                    invoiceIssueIs: null,
                    applyInvoicingTime: null,
                }
            },
            siteReceivingTableSelectRowData: [],
            selectContractOrPlanSelectRowData: [],
            siteReceivingTableDate: [],
            paginationInfo2: {
                total: 0,
                pageSize: 20,
                currentPage: 1,
            },
            paginationInfo: {
                total: 0,
                pageSize: 20,
                currentPage: 1,
            },
            siteReceivingLoading: false,
            selectContractOrPlanLoading: false,
            contractOrPlanTableDate: [],
            warehouseList: [
                {
                    label: '库房一',
                    value: 1,
                },
            ],
            supplierTypeList: [
                {
                    label: '零星采购',
                    value: 1,
                },
                {
                    label: '大宗合同',
                    value: 2
                },
                {
                    label: '周转材料',
                    value: 3
                }
            ],
            tabsName: 'baseInfo',
            screenWidth: 0,
            screenHeight: 0,
            lastConHeight: 0,
            clickTabFlag: false, // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            winEvent: {},
            topHeight: 120,
            tableData: [],
            tableLoading: false,
        }
    },
    components: {
        Pagination,
    },
    created () {
        this.addForm.numUnitOptions = this.materialUnit
        this.rowData = this.$route.params.row
        if (this.rowData.viewType == 'add') {

            this.getEnterpriseInfoTaxRateM()
            this.viewType = 'add'
            if (this.rowData.classPath != null) {
                this.addForm.formData.classPath = this.rowData.classPath
                this.addForm.formData.classId = this.rowData.classPath[this.rowData.classPath.length - 1]
            }
            this.showForm = true
        } else {
            this.getMaterialInfo()
        }
        this.getEconomizeAndMarketList()
        console.log(this.userInfo.isBusiness)//是否自营店铺：1：是  0：否
    },
    mounted () {
    // 获取最后一个内容区域的高度，计算底部空白
        this.getLastConHeight()
        // 保存所有tabName
        const arr = ['baseInfo', 'baseInfos', 'auditRecords']
        this.tabArr = arr
        let $idsTop = []
        const onScroll = () => {
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            if (this.clickTabFlag) {
                return
            }
            if (!$idsTop[$idsTop.length - 1]) {
                $idsTop = arr.map(item => {
                    const $item = document.getElementById(item)
                    let itemTop = null
                    if ($item) {
                        itemTop = $item.offsetTop
                    }
                    return itemTop
                })
            }
            const scrollTop = $('#tabs-content')[0].scrollTop
            // 倒序查找
            let curLocal = 0
            for (let i = $idsTop.length - 1; i >= 0; i--) {
                let item = $idsTop[i]
                if (scrollTop + 1 >= item) {
                    curLocal = i
                    break
                }
            }
            // 设置对应tabName
            this.tabsName = arr[curLocal]
        }
        this.winEvent.onScroll = onScroll
        $('#tabs-content')[0].addEventListener('scroll', this.winEvent.onScroll)
        this.screenWidth = document.documentElement.clientWidth - this.topHeight
        this.screenHeight = document.documentElement.clientHeight - this.topHeight
        const onResize = () => {
            this.screenWidth = document.documentElement.clientWidth - this.topHeight
            this.screenHeight = document.documentElement.clientHeight - this.topHeight
            $idsTop = arr.map(item => {
                const itemTop = document.getElementById(item).offsetTop
                return itemTop
            })
        }
        this.winEvent.onResize = throttle(onResize, 500)
        window.addEventListener('resize', this.winEvent.onResize)
        if (this.$route.query.name) {
            setTimeout(() => {
                this.onChangeTab({
                    name: this.$route.query.name
                })
                this.tabsName = this.$route.query.name
            }, 200)
        }
    },
    beforeDestroy () {
        window.removeEventListener('scroll', this.winEvent.onScroll)
        window.removeEventListener('resize', this.winEvent.onResize)
    },
    computed: {
        ...mapState(['materialUnit']),
        ...mapState({
            options: state => state.contract.ctClassify,
            userInfo: state => state.userInfo,
        }),
        // tab内容高度
        tabsContentHeight () {
            return this.screenHeight - 140 + 'px'
        },
        // 填补底部空白，以使高度够滚动
        seatHeight () {
            return this.screenHeight - 72 - this.lastConHeight
        },
        ...mapState(['userInfo']),
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 291
        },
    },
    watch: {
        screenHeight: {
            handler (newVal) {
                $('#tabs-content').height(newVal - 71)
            }
        },
        'addForm.formData.isZone': {
            handler (val) {
                this.addForm.formData.isZone = parseInt(this.addForm.formData.isZone)
                if (val == 2) {
                    if (this.addForm.formData.zonePriceList == null) {
                        this.addForm.formData.zonePriceList = []
                    }

                }
            }
        }
    },
    methods: {
        getFixed () {
            getList({ keywords: '固定加成率' }).then(res => {
                this.addForm.formData.markUpNum = res.list[0].keyValue2
                this.showForm = true
            })
        },
        getEconomizeAndMarketList () {
            let economizeList = []
            let marketList = []
            this.addressData.forEach((e, i) => {
                economizeList.push({ label: e.label, value: e.value })
                marketList.push({ label: e.label, value: e.value, children: [] })
                e.children.forEach(s => {
                    marketList[i].children.push({ label: s.label, value: s.value })
                })
            })
            this.economizeData = economizeList
            this.marketData = marketList
        },
        removeOneParamsData (row) {
            if (row.index === 1) {
                let region = this.addForm.formData.regionTableData[0]
                region.taxInPrice = ''
                region.price = ''
                region.selectAddressOptionsAll = []
                region.detailAddress = []
            } else {
                this.addForm.formData.regionTableData = this.addForm.formData.regionTableData.filter(obj => obj.index !== row.index)
            }
        },
        //获取当前企业的企业税率
        getEnterpriseInfoTaxRateM () {
            getEnterpriseInfoTaxRate().then(res => {
                this.addForm.formData.taxRate = res
            })
        },
        // 获取供应商下的计划订单列表
        getContractOrPlanListM () {
            let params = {
                page: this.paginationInfo.currentPage,
                limit: this.paginationInfo.pageSize,
            }
            if (this.keywords != null) {
                params.keywords = this.keywords
            }
            if (this.addForm.formData.supplierType != null) {
                params.productType = this.addForm.formData.supplierType
            }
            if (this.addForm.formData.purchasingOrgName != null) {
                params.orgName = this.addForm.formData.purchasingOrgName
            }
            this.selectContractOrPlanLoading = true
            // supplierGetContactPlanPageList(params).then(res => {
            getReconciliablePlansBySupplier(params).then(res => {
                this.paginationInfo.total = res.totalCount
                this.paginationInfo.pageSize = res.pageSize
                this.paginationInfo.currentPage = res.currPage
                this.contractOrPlanTableDate = res.list
            }).finally(() => {
                this.selectContractOrPlanLoading = false
            })
        },
        getSiteReceivingTableDateM () {
            let params = {
                page: this.paginationInfo2.currentPage,
                limit: this.paginationInfo2.pageSize,
                orgId: this.addForm.formData.purchasingOrgId,
                businessType: this.addForm.formData.supplierType,
            }
            let orderIds = this.selectContractOrPlanSelectRowData.map(t => t.orderId)
            let orderSns = this.selectContractOrPlanSelectRowData.map(t => t.orderSn)
            if (orderIds.length == 0) {
                return this.$message.error('未选择订单')
            }
            params.orderIds = orderIds
            // 传递订单编号，用逗号分隔
            params.orderSn = orderSns.join(',')
            // 如果是内部店铺
            if (this.userInfo.isInterior == 1) {
                params.orgShort = this.userInfo.orgInfo.shortCode
            } else {
                params.creditCode = this.userInfo.socialCreditCode
            }
            if (this.keywords2 != null) {
                params.keyword = this.keywords2
            }
            if (this.addForm.formData.sourceBillId != null) {
                params.sourceId = this.addForm.formData.sourceBillId
            }
            params.productType = this.addForm.formData.supplierType
            this.siteReceivingLoading = true
            getReconcilableMaterialList(params).then(res => {
                this.paginationInfo2.total = res.totalCount
                this.paginationInfo2.pageSize = res.pageSize
                this.paginationInfo2.currentPage = res.currPage
                this.siteReceivingTableDate = res.list

                // 默认全部选中右侧列表数据
                this.$nextTick(() => {
                    if (res.list && res.list.length > 0) {
                        res.list.forEach(row => {
                            this.$refs.siteReceivingTableRef.toggleRowSelection(row, true)
                        })
                        // 更新选中的数据
                        this.siteReceivingTableSelectRowData = [...res.list]
                    }
                })
            }).finally(() => {
                this.siteReceivingLoading = false
            })
        },
        selectContractOrPlanSelectM (value) {
            this.selectContractOrPlanSelectRowData = value
            this.siteReceivingTableDate = []
            // 重置右边列表的分页参数
            this.paginationInfo2.currentPage = 1
            this.paginationInfo2.total = 0
            if (value.length > 0) {
                let num = this.addForm.formData.supplierType
                let row = this.selectContractOrPlanSelectRowData[0]
                // 计划
                if (num == 10) {
                    this.addForm.formData.businessType = 2
                    this.reconciliationForm.sourceBillId = row.billId
                    this.reconciliationForm.sourceBillNo = row.billNo
                }
                // 合同
                if (num == 12) {
                    this.reconciliationForm.businessType = 1
                    this.reconciliationForm.sourceBillId = row.contractId
                    this.reconciliationForm.sourceBillNo = row.contractNo
                }
                this.productTypeDis = true
                this.tableData = []
                this.reconciliationForm.creditCode = row.creditCode
                this.reconciliationForm.orgShort = row.shortCode
                this.reconciliationForm.supplierOrgId = row.storageOrgId
                // 只在收货单位信息为空时才从计划/合同数据中获取，避免覆盖用户手动选择的收货单位
                if (!this.reconciliationForm.purchasingOrgId) {
                    this.reconciliationForm.purchasingOrgId = row.orgId
                }
                if (!this.reconciliationForm.purchasingOrgName) {
                    this.reconciliationForm.purchasingOrgName = row.orgName
                }
                this.reconciliationForm.taxRate = row.taxRate
                this.originalTaxRate = row.taxRate // 保存原始税率
                let newArr = this.selectContractOrPlanSelectRowData.filter(t => {
                    if (row.billNo != t.billNo) {
                        this.$message.error('请选择同计划的订单数据！')
                        this.$refs.selectContractOrPlanR.toggleRowSelection(t, false)
                        return false
                    } else {
                        this.$refs.selectContractOrPlanR.toggleRowSelection(t, true)
                        return true
                    }
                })
                this.selectContractOrPlanSelectRowData = newArr
                this.getSiteReceivingTableDateM()
            }
        },
        selectContractOrPlanRowClickM (row) {
            row.flag = !row.flag
            this.$refs.selectContractOrPlanR.toggleRowSelection(row, row.flag)
        },
        siteReceivingTableSelectM (value) {
            this.siteReceivingTableSelectRowData = value
        },
        siteReceivingTableRowClickM (row) {
            row.flag = !row.flag
            this.$refs.siteReceivingTableRef.toggleRowSelection(row, row.flag)
        },
        // 选择数据确认
        siteReceivingTableDateSelectAffirmClick () {
            if (this.siteReceivingTableSelectRowData.length == 0) {
                return this.$message.error('未选择数据')
            }
            let newArr = []
            this.siteReceivingTableSelectRowData.forEach(row => {
                this.reconciliationForm.taxRate = row.taxRate
                this.originalTaxRate = row.taxRate // 保存原始税率
                let dtl = {
                    // 基础信息
                    orderId: row.orderId,
                    orderSn: row.orderSn,
                    tradeId: row.tradeId,
                    orderItemId: row.orderItemId,

                    // 物资信息
                    materialName: row.materialName,
                    materialClassId: row.materialClassId,
                    materialClassName: row.materialClassName,
                    spec: row.spec,
                    texture: row.texture,
                    unit: row.unit,

                    // 供应商信息
                    supplierId: row.supplierId,
                    supplierName: row.supplierName,

                    // 采购信息
                    purchaserId: row.purchaserId,
                    purchaserName: row.purchaserName,
                    purchasingOrgId: row.purchasingOrgId,
                    purchasingOrgName: row.purchasingOrgName,

                    // 数量和价格
                    maxQuantity: row.maxQuantity,
                    quantity: row.maxQuantity, // 默认使用最大可对账数量
                    ratePrice: row.ratePrice || 0, // 含税单价
                    noRatePrice: row.noRatePrice ? Number(row.noRatePrice).toFixed(2) : '0.00', // 不含税单价，截取2位小数
                    price: row.ratePrice || 0, // 含税单价

                    // 日期
                    receivingDate: row.receiveDate,

                    // 对账单类型
                    type: row.type,

                    // 备注
                    remarks: row.remarks,

                    // 其他字段（保持原有逻辑）
                    settledAmount: 0,
                    fixationPrice: 0,
                    freightPrice: 0,
                    outFactoryPrice: row.outFactoryPrice || Number(row.ratePrice) || 0, // 出厂价默认等于含税单价
                    transportPrice: row.transportPrice || 0, // 运杂费
                    acceptanceAmount: 0,
                    acceptanceNoRateAmount: 0,
                    uuid: getUuid()
                }
                if (dtl.maxQuantity == null || dtl.maxQuantity <= 0) {
                    return this.$message.error('剩余数量为0不能选择！')
                }
                newArr.push(dtl)
            })
        },
        resetRelevanceAndBrand () {
            let arr = ['relevanceName', 'brandName', 'brandId', 'relevanceId']
            arr.forEach(item => this.addForm.formData[item] = '')
        },
        // 获取物资详情
        getMaterialInfo () {
            this.formLoading = true
        },
        // 物资库点击
        handleCurrentInventoryClick (row) {
            console.log('111122', row)
            this.addForm.formData.productName = row.materialName
            this.addForm.formData.productId = row.billId
            this.addForm.formData.serialNum = row.billNo
            this.showDeviceDialog = false
        },
        // 获取物资库
        getDeviceInventory () {
            let params = {
                page: this.paginationInfo.currentPage,
                limit: this.paginationInfo.pageSize,
            }
            if (this.keywords != null) {
                params.keywords = this.keywords
            }
            if (this.reconciliationForm.reconciliationProductType != null) {
                params.productType = this.reconciliationForm.reconciliationProductType
            }
            if (this.reconciliationForm.purchasingOrgName != null) {
                params.orgName = this.reconciliationForm.purchasingOrgName
            }
            this.selectContractOrPlanLoading = true
            getReconciliablePlansBySupplier(params).then(res => {
                this.paginationInfo.total = res.totalCount
                this.paginationInfo.pageSize = res.pageSize
                this.paginationInfo.currentPage = res.currPage
                this.contractOrPlanTableDate = res.list
            }).finally(() => {
                this.selectContractOrPlanLoading = false
            })
        },
        //根据发货单位进行分组查询企业名称
        getOrderPlanByOrgNameListM () {
            let params = {
                page: this.paginationInfo1.currentPage,
                limit: this.paginationInfo1.pageSize,
            }
            if (this.supplierKeywords != null) {
                params.keywords = this.supplierKeywords
            }
            if (this.addForm.formData.supplierType != null) {
                params.productType = this.addForm.formData.supplierType
            }
            this.selectPurchasingOrgNameLoading = true
            supplierGetEnterprisePageList(params).then(res => {
                this.paginationInfo1.total = res.totalCount
                this.paginationInfo1.pageSize = res.pageSize
                this.paginationInfo1.currentPage = res.currPage
                this.purchasingOrgNameTable = res.list
                console.log('项目部列表数据:', res.list)
            }).finally(() => {
                this.selectPurchasingOrgNameLoading = false
            })
        },

        // 选择物资名称
        importDeviceSelect () {
            if (this.addForm.formData == null || this.addForm.formData.supplierName == '') {
                this.$message.warning('请先选择收货单位')
                return
            }
            this.siteReceivingTableDate = []
            // 重置右边列表的分页参数
            this.paginationInfo2.currentPage = 1
            this.paginationInfo2.total = 0
            this.getContractOrPlanListM()
            this.selectContractOrPlanSelectRowData = []
            this.selectMaterialDialogShow = true
        },
        selectedEnterprise () {
            this.supplierKeywords = null
            this.selectPurchasingOrgNameTitle = '选择项目部'
            this.getOrderPlanByOrgNameListM()
            this.showPurchasingOrgNameView = true
        },
        // 提交
        submit () {
            console.log(this.addForm.formData)
            this.$refs.formEdit.validate(valid => {
                if (!valid) return this.$message.error('请检查非空输入框')
                spliceImgUrl(this.addForm.formData, this.imgUrlPrefixDelete)
                if (this.viewType === 'add') {
                    this.formLoading = true
                    saveOutBound(this.addForm.formData).then(res => {
                        if (res.code != null && res.code != 200) {
                            addImgUrl(this.addForm.formData, this.imgUrlPrefixAdd)
                            return this.formLoading = false
                        }
                        this.message(res)
                    }).finally(() => {
                        this.formLoading = false
                    })
                } else {
                    this.formLoading = true
                    updateOutBound(this.addForm.formData).then(res => {
                        this.getMaterialInfo()
                        this.message(res)
                    }).catch(() => {
                        this.getMaterialInfo()
                    }).finally(() => this.formLoading = false)
                }
            })
        },
        saveAndSubmit () {
            console.log(this.addForm.formData)
            this.$refs.formEdit.validate(valid => {
                if (!valid) return this.$message.error('请检查非空输入框')
                spliceImgUrl(this.addForm.formData, this.imgUrlPrefixDelete)
                if (this.viewType === 'add') {
                    this.formLoading = true
                    saveAndSubmitOutBound(this.addForm.formData).then(res => {
                        if (res.code != null && res.code != 200) {
                            addImgUrl(this.addForm.formData, this.imgUrlPrefixAdd)
                            return this.formLoading = false
                        }
                        this.message(res)
                    }).finally(() => {
                        this.formLoading = false
                    })
                } else {
                    this.formLoading = true
                    updateOutBound(this.addForm.formData).then(res => {
                        this.getMaterialInfo()
                        this.message(res)
                    }).catch(() => {
                        this.getMaterialInfo()
                    }).finally(() => this.formLoading = false)
                }
            })
        },
        //取消
        handleClose () {
            this.$router.replace('/supplierSys/analysis/onBoundManagement')
        },
        onChangeTab (e) {
            const height = $('#' + e.name).offset().top
            try {
                $('#tabs-content').scrollTo(height - this.topHeight, 500)
            } catch (err) {
                console.log(err)
            }
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            this.clickTabFlag = true
            // 动画结束后，恢复状态
            setTimeout(() => {
                this.clickTabFlag = false
            }, 600)
        },
        // 获取最后一个内容区域的高度，计算底部空白
        getLastConHeight () {
            let si = setInterval(() => {
                // 因为dom异步加载，通过轮询找到渲染后的dom，获取高度
                if (document.getElementById('terminationLog')) {
                    this.lastConHeight = document.getElementById('terminationLog').offsetHeight
                    clearInterval(si)
                    si = null
                }
            }, 100)
        },
        getOrderPlanListM () {
            let params = {
                page: this.paginationInfo1.currentPage,
                limit: this.paginationInfo1.pageSize,
            }
            if (this.supplierKeywords != null) {
                params.keywords = this.supplierKeywords
            }
            if (this.addForm.formData.supplierType != null) {
                params.productType = this.addForm.formData.supplierType
            }
            this.selectPurchasingOrgNameLoading = true
            getContactSupplierPageList(params).then(res => {
                this.paginationInfo1.total = res.totalCount
                this.paginationInfo1.pageSize = res.pageSize
                this.paginationInfo1.currentPage = res.currPage
                this.purchasingOrgNameTable = res.list
            }).finally(() => {
                this.selectPurchasingOrgNameLoading = false
            })
        },
        currentChangeUser (currPage) {
            this.paginationInfo1.currentPage = currPage
            this.getOrderPlanListM()
        },
        sizeChangeUser (pageSize) {
            this.paginationInfo1.pageSize = pageSize
            this.getOrderPlanListM()
        },
        currentChangeUser2 (currPage) {
            this.paginationInfo2.currentPage = currPage
            this.getOrderPlanListM()
        },
        sizeChangeUser2 (pageSize) {
            this.paginationInfo2.pageSize = pageSize
            this.getOrderPlanListM()
        },
        //选择发货单位
        submitOrgName (row) {
            this.addForm.formData.purchasingOrgName = row.orgName
            this.addForm.formData.purchasingOrgId = row.orgId
            this.addForm.formData.supplierName = row.storageName
            this.addForm.formData.contractNo = row.contractNo
            getInvoiceRecordInfo(row.orgId).then(res=>{
                this.addForm.formData.ticketReceivingUnit = res.company
                this.addForm.formData.ticketReceivingUnitAccount = res.bank
                this.addForm.formData.ticketReceivingUnitAddress = res.userAddress
                this.addForm.formData.ticketReceivingUnitBank = res.bank
                this.addForm.formData.ticketReceivingUnitPhone = res.userPhone
                this.addForm.formData.ticketReceivingUnitTaxNo = res.dutyParagraph
            })
            this.showPurchasingOrgNameView = false
        },
        // 重置数据
        resetFormData () {
            this.addForm.formData = {
                supplierType: null,
                outWarehouseTime: null,
                supplierName: null,
                supplierId: null,
                warehouseId: 1,
                projectAddress: null,
                noRateAmount: null,
                rateAmount: null,
                projectName: null,
                periodsNum: null,
                ticketReceivingUnit: null,
                ticketReceivingUnitAddress: null,
                ticketReceivingUnitAccount: null,
                ticketReceivingUnitBank: null,
                ticketReceivingUnitTaxNo: null,
                ticketReceivingUnitPhone: null,
                invoiceIssueIs: null,
                applyInvoicingTime: null,
            }
        },
        // 消息提示
        message (res) {
            this.$message({
                message: res.message,
                type: res.code === 200 ? 'success' : 'error'
            })
        },
        currentChange () {

        },
        sizeChange () {

        },
        handleCurrentChange () {

        },
        handleSelectionChange () {

        },
    }
}
</script>

<style lang='scss' scoped>
/deep/ input::-webkit-outer-spin-button,
/deep/ input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
}

/deep/ input[type='number'] {
  -moz-appearance: textfield !important;
}

.e-table {
  min-height: auto;
  background: #fff;
}

#tabs-content {
  padding-bottom: 70px !important;
}

/deep/ .el-dialog .el-dialog__body {
  height: unset !important;
  margin: 0 20px;
}

/deep/ .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  width: 148px;
  height: 148px;
  cursor: pointer;
  position: relative;
  overflow: hidden;

  .el-icon-delete {
    margin-left: 10px;
    line-height: unset;
    color: #e9513e;
  }

  .cover {
    width: 100%;
    height: 100%;
    position: relative;
    left: 0;
    top: 0;
    background-color: rgba(0, 0, 0, 0.7);
    display: none;
  }

  &:hover {
    border-color: #409EFF;

    .cover {
      display: block;
    }
  }

  img, & > i {
    width: 148px;
    height: 148px;
  }

  i {
    color: #8c939d;
    line-height: 148px;
    text-align: center;
  }

  img {
    object-fit: cover;
    display: block;
  }
}

/deep/ .el-input--suffix .el-input__inner {
  padding-right: 5px;
}

// 上传成功后隐藏上传按钮
/deep/ .hide_box_admin .el-upload--picture-card {
  display: none;
}

/deep/ .hide_box_min .el-upload--picture-card {
  display: none;
}

.action {
  margin-right: 10px;
  color: #2e61d7;
  cursor: pointer;
}

//弹窗
/deep/ .el-dialog {
  .el-dialog__body {
    height: 300px;
    margin-top: 0;
  }
}
/deep/ .el-dialog.dlg {
  height: 600px;

  .el-dialog__header {
    margin-bottom: 0;
  }

  .el-dialog__body {
    height: 1474px;
    margin: 10px;
    display: flex;

    & > div {
      .e-pagination {
        background-color: unset;
      }

      //height: 670px;
      .title {
        height: 22px;
        margin-bottom: 10px;
        padding-left: 26px;
        text-align: left;
        line-height: 22px;
        color: #2e61d7;
        font-weight: bold;
        position: relative;
        display: flex;

        &::before {
          content: '';
          display: block;
          width: 10px;
          height: inherit;
          border-radius: 5px;
          background-color: blue;
          position: absolute;
          left: 10px;
          top: 0;
        }
      }
    }

    .el-input__inner {
      border: 1px solid blue;
      border-radius: 6px;
    }

    .el-input__suffix {
      width: 20px;
    }

    .e-table {
      flex-grow: 1;

      .table {
        height: 100%;
      }
    }
    .el-table__empty-block {
      min-height: 500px;
    }

    .box-left {
      width: 660px;
      display: flex;
      flex-direction: column;
      .top {
        box-shadow: unset;
      }
    }

    .box-right {
      flex-grow: 1;
      display: flex;
      flex-direction: column;

      & > div {
        display: flex;
        flex-direction: column;
      }

      .top {
        justify-content: left;
        border-radius: 0;
        box-shadow: unset;
      }

      .bottom {
        flex-grow: 1;
      }
    }
  }

  .el-dialog__footer {
    background-color: #eff2f6;
  }
}

</style>
