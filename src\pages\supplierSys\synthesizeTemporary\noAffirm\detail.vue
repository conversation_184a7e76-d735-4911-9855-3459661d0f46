<template>
    <div class="e-form">
        <BillTop @cancel="handleClose"/>
        <div class="tabs warningTabs" style="padding-top: 70px;" v-loading="formLoading">
            <el-tabs tab-position="left" v-model="tabsName" @tab-click="onChangeTab">
                <el-tab-pane label="清单信息" name="baseInfo" :disabled="clickTabFlag">
                </el-tab-pane>
                <el-tab-pane label="清单明细" name="productInfo" :disabled="clickTabFlag">
                </el-tab-pane>
                <el-tab-pane label="审核历史" name="auditRecords" :disabled="clickTabFlag"/>
                <div id="tabs-content" style="padding-bottom: 70px;">
                    <!-- 基本信息 -->
                    <div id="baseInfCon" class="con">
                        <div class="tabs-title" id="baseInfo">清单信息</div>
                        <div class="form">
                            <el-form :model="formData" :rules="formRoles" label-width="200px" ref="rulesBase" class="demo-ruleForm">
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="编号：">
                                            <span>{{ formData.synthesizeTemporarySn }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="状态：">
                                            <el-tag type="info" v-if="formData.state == 0">草稿</el-tag>
                                            <el-tag v-if="formData.state == 1">待确认</el-tag>
                                            <el-tag v-if="formData.state == 2">待审核</el-tag>
                                            <el-tag type="success" v-if="formData.state == 3">审核通过</el-tag>
                                            <el-tag type="danger" v-if="formData.state == 4">审核未通过</el-tag>
                                            <el-tag type="danger" v-if="formData.state == 5">收货单位拒绝</el-tag>
                                            <el-tag type="danger" v-if="formData.state == 11">审核不通过</el-tag>
                                            <el-tag type="success" v-if="formData.state == 6">已推送大宗临购计划</el-tag>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="清单类型：">
                                            <el-tag v-if="formData.billType == 1">浮动价格</el-tag>
                                            <el-tag v-if="formData.billType == 2">固定价格</el-tag>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="省：">
                                            <span>{{ formData.province }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="市：">
                                            <span>{{ formData.city }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="县、区：">
                                            <span>{{ formData.county }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="项目收货地址：">
                                            <span>{{ formData.receiverAddress }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="含税总金额（元）：">
                                            <span>{{ formData.synthesizeSumAmount }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12" >
                                        <el-form-item label="超期垫资利息（%）：" prop="outPhaseInterest">
                                            <el-input
                                                v-if="formData.state == 1 || formData.state == 4 || formData.state == 5"
                                                type="number"
                                                v-model="formData.outPhaseInterest"
                                                @change="outPhaseInterestChangeM(formData)">
                                            </el-input>
                                            <span v-else>{{formData.outPhaseInterest}}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12" >
                                        <el-form-item label="货款支付周期（单位月）：" prop="paymentWeek">
                                            {{formData.paymentWeek}}月
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="提交时间：">
                                            <span>{{ formData.submitTime }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="创建时间：">
                                            <span>{{ formData.gmtCreate }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="采购单位名称：">
                                            <span>{{ formData.orgName }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="供货单位名称：">
                                            <span>{{ formData.supplierOrgName }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row v-if="formData.state === 5">
                                    <el-col :span="24">
                                        <el-form-item label="收货单位拒绝原因：">
                                            <span style="color: red">{{formData.refuseRes}}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="24">
                                        <el-form-item label="备注信息：" >
                                          <div class="remark-content">  {{formData.remarks}}</div>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                            </el-form>
                        </div>
                    </div>
                    <!-- 订单商品-->
                    <div id="productInfo" class="con" v-loading="tableLoading">
                        <div class="tabs-title" id="productInfo">清单明细
                        </div>
                        <div class="top" style="height: 50px;padding-bottom: 1px;margin-bottom: 5px">
                        <div class="search_box" style="margin-left: 20px">
                            <el-button type="primary" v-if="formData.state === 1 || formData.state === 0 || formData.state === 4 || formData.state === 5 || formData.state === 11"  @click="createSynthesizeTemporaryBid">生成竞价单</el-button>
<!--                            <el-button type="primary" v-if="inventoryBid && formData.state === 1"  @click="createSynthesizeTemporaryBid">生成竞价单</el-button>-->
                        </div>
                        </div>
                        <div class="e-table">
                            <el-table
                                  border
                                  :data="formData.dtls"
                                  class="table"
                                  @selection-change="handleSelectDtl"
                                  :max-height="$store.state.tableHeight"
                            >
                                <el-table-column type="selection"  :selectable="handleSelectedAble" header-align="center" align="center" width="50"></el-table-column>
                                <el-table-column label="序号" type="index" width="60"/>
                                <el-table-column prop="productSn" label="商品编号" width="200"/>
                                <el-table-column prop="isBidding" label="竞价状态" width="120">
                                    <template v-slot="scope">
                                        <span v-if="scope.row.isBidding == 1" class="pointer action" @click="gotoBidding(scope.row)">已生成竞价</span>
                                        <span v-if="scope.row.isBidding == 0">未生成竞价</span>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="productName" label="商品名称" width=""/>
                                <el-table-column prop="materialName" label="物资名称" width=""/>
                                <el-table-column prop="spec" label="规格型号"/>
                                <el-table-column prop="classNamePath" label="分类路径"/>
                                <el-table-column prop="texture" label="材质"/>
                                <el-table-column prop="brandName" label="品牌名称"/>
                                <el-table-column v-if="formData.billType === 1" prop="netPrice" label="网价" width="100">
                                    <template v-slot="scope">
                                        <el-input
                                            v-if="formData.state == 1 || formData.state == 4 || formData.state == 5"
                                            type="number"
                                            v-model="scope.row.netPrice"
                                            @change="netPriceChangeM(scope.row)">
                                        </el-input>
                                        <span v-else>{{scope.row.netPrice}}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column v-if="formData.billType === 1" prop="fixationPrice" label="固定费" width="100">
                                    <template v-slot="scope">
                                        <el-input
                                            type="number"
                                            v-if="formData.state == 1 || formData.state == 4 || formData.state == 5"
                                            v-model="scope.row.fixationPrice"
                                            @change="fixationPriceChangeM(scope.row)">
                                        </el-input>
                                        <span v-else>{{scope.row.fixationPrice}}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column v-if="formData.billType === 2" prop="outFactoryPrice" label="出厂价" width="100">
                                    <template v-slot="scope">
                                        <el-input
                                            v-if="formData.state == 1 || formData.state == 4 || formData.state == 5"
                                            type="number"
                                            v-model="scope.row.outFactoryPrice"
                                            @change="outFactoryPriceChangeM(scope.row)">
                                        </el-input>
                                        <span v-else>{{scope.row.outFactoryPrice}}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column v-if="formData.billType === 2" prop="transportPrice" label="运杂费" width="100">
                                    <template v-slot="scope">
                                        <el-input
                                            v-if="formData.state == 1 || formData.state == 4 || formData.state == 5"
                                            type="number"
                                            v-model="scope.row.transportPrice"
                                            @change="transportPriceChangeM(scope.row)">
                                        </el-input>
                                        <span v-else>{{scope.row.transportPrice}}</span>
                                    </template>
                                </el-table-column>
<!--                                <el-table-column prop="isTwoUnit" label="是否有副级单位" width="">-->
<!--                                    <template v-slot="scope">-->
<!--                                        <el-tag type="success" v-if="scope.row.isTwoUnit === 1">是</el-tag>-->
<!--                                        <el-tag type="info" v-if="scope.row.isTwoUnit === 0">否</el-tag>-->
<!--                                    </template>-->
<!--                                </el-table-column>-->
                                <el-table-column prop="qty" label="数量" width=""/>
                                <el-table-column prop="unit" label="计量单位" width=""/>
<!--                                <el-table-column prop="twoUnitNum" label="副级数量" width="">-->
<!--                                    <template v-slot="scope">-->
<!--                                        <span v-if="scope.row.isTwoUnit === 1">{{scope.row.twoUnitNum}}</span>-->
<!--                                        <span v-else>/</span>-->
<!--                                    </template>-->
<!--                                </el-table-column>-->
<!--                                <el-table-column prop="twoUnit" label="副级单位" width="">-->
<!--                                    <template v-slot="scope">-->
<!--                                        <span v-if="scope.row.isTwoUnit === 1">{{scope.row.twoUnit}}</span>-->
<!--                                        <span v-else>/</span>-->
<!--                                    </template>-->
<!--                                </el-table-column>-->

                                <el-table-column prop="synthesizePrice" label="含税单价"/>
                                <el-table-column prop="synthesizeAmount" label="含税金额"/>
                            </el-table>
                        </div>
                    </div>
                    <div id="auditRecords" class="con">
                        <div class="tabs-title" id="auditRecords">审核历史</div>
                        <div class="e-table"  style="background-color: #fff">
                            <el-table
                                border
                                style="width: 100%"
                                :data="formData.auditRecords"
                                :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
                                :row-style="{ fontSize: '14px', height: '48px' }"
                            >
                                <el-table-column label="序号" type="index" width="60"></el-table-column>
                                <el-table-column prop="auditType" label="审核类型" width="160">
                                    <template slot-scope="scope">
                                        <el-tag v-if="scope.row.auditType == 1">提交审核</el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="founderName" label="审核人" width="200">
                                </el-table-column>
                                <el-table-column prop="gmtCreate" label="审核时间" width="160">
                                </el-table-column>
                                <el-table-column prop="auditResult" label="审核意见" width="">
                                </el-table-column>
                            </el-table>
                        </div>
                    </div>
                </div>
            </el-tabs>
        </div>
        <div class="buttons">
            <el-button
                type="primary" class="btn-greenYellow"
                v-if="formData.state === 2 && userInfo.roles.includes('物资大宗临购清单审核权限')"
                @click="auditPlanM(1, '通过')"
                v-has-permi="{platform:'oneselfShopManage',auth:'SynthesizeTemporaryAuditCode'}"
            >通过
            </el-button>
            <el-button
                type="primary" class="btn-delete"
                v-if="formData.state === 2  && userInfo.roles.includes('物资大宗临购清单审核权限')"
                v-has-permi="{platform:'oneselfShopManage',auth:'SynthesizeTemporaryAuditCode'}"
                @click="auditPlanM(0, '未通过')"
            >未通过
            </el-button>
            <el-button type="primary" v-if="formData.orgIsDelete === 1" class="btn-delete" @click="deleteBilM">删除</el-button>
            <el-button type="primary" v-if="formData.state === 1 || formData.state === 4" class="btn-delete" @click="auditRefuseM">拒绝</el-button>
            <el-button type="primary" v-if="formData.state === 1 || formData.state === 4 || formData.state === 5" class="btn-greenYellow" @click="batchUpdate(0)">保存</el-button>
            <el-button type="primary" v-if="(formData.state === 1 || formData.state === 4 || formData.state === 5) && userInfo.roles.includes('物资大宗临购清单提交权限')" class="btn-greenYellow"
                       v-has-permi="{platform:'oneselfShopManage',auth:'SynthesizeTemporarySubmitCode'}"
                       @click="batchUpdate(1)">保存并提交</el-button>
            <el-button @click="handleClose">返回</el-button>
        </div>
        <el-dialog v-loading="bidingFormLoading" v-dialogDrag id="supplierDialog"  title="生成竞价" :visible.sync="showBidingForm"  width="80%" style="margin-left: 20%;" :close-on-click-modal="false">
            <div class="tabs-title" id="contractList">竞价信息</div>
            <el-form :model="bidingForm" :rules="bidingFormRules" label-width="200px" ref="bidingFormRef" :disabled="false" class="demo-ruleForm">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="标题：" prop="title">
                            <el-input maxlength="200" placeholder="请输入标题" clearable  v-model="bidingForm.title"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="截止时间：" prop="endTime">
                            <el-date-picker
                                v-model="bidingForm.endTime"
                                type="datetime"
                                placeholder="选择日期时间"
                                value-format="yyyy-MM-dd HH:mm:ss"
                                :picker-options="pickerOptions">
                            </el-date-picker>
<!--                            <el-date-picker-->
<!--                                value-format="yyyy-MM-dd HH:mm:ss"-->
<!--                                v-model="bidingForm.endTime"-->
<!--                                align="right"-->
<!--                                type="date"-->
<!--                                placeholder="选择日期"-->
<!--                                :picker-options="pickerOptions">-->
<!--                            </el-date-picker>-->
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="联系人：" prop="linkName">
                            <el-input maxlength="10" placeholder="请输入联系人" clearable  v-model="bidingForm.linkName"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="联系电话：" prop="linkPhone">
                            <el-input type="number" clearable  v-model="bidingForm.linkPhone" placeholder="请输入11位手机号码"/>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="竞价类型：" prop="type">
                            <el-radio-group v-model="bidingForm.type">
                                <el-radio :label="1">公开竞价</el-radio>
                                <span> <el-radio :label="2" >邀请竞价</el-radio></span>
                            </el-radio-group>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" v-if="bidingForm.type===2">
                        <el-form-item label="选择供应商：" prop="">
                            <el-button type="primary"  @click="showSupplierDialog">选择供应商</el-button>
                            <!--                            <el-select style="width: 80%" name="name" no-data-text="请选择供应商" v-model="bidingForm.suppliers"  multiple collapse-tags  placeholder="请选择">-->
                            <!--                                <el-option-->
                            <!--                                    v-for="item in bidingForm.suppliers"-->
                            <!--                                    :key="item.enterpriseId"-->
                            <!--                                    :label="item.enterpriseName"-->
                            <!--                                    :value="item.enterpriseId"-->
                            <!--                                    :disabled="item.disabled">-->
                            <!--                                </el-option>-->
                            <!--                            </el-select>-->
                        </el-form-item>

                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="说明：" prop="biddingExplain">
                            <editor v-model="bidingForm.biddingExplain"></editor>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row style="margin-top: 10px">
                    <el-col :span="24">
                        <el-form-item label="竞价函说明：" prop="biddingNotice">
                            <el-input type="textarea"
                                      :rows="6" v-model="bidingForm.biddingNotice"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <div class="tabs-title" id="contractList">清单商品</div>
            <div class="e-table"  style="background-color: #fff">
                <el-table ref="tableRef"
                          border
                          style="width: 100%"
                          :data="bidingForm.synthesizeTemporaryDtlList"
                          class="table"
                          :max-height="$store.state.tableHeight"
                >
                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                    <el-table-column prop="productName" label="商品名称" width="200px"></el-table-column>
<!--                    <el-table-column prop="productImg" label="商品图片" width="130">-->
<!--                        <template v-slot="scope">-->
<!--                            <el-image style="width: 90px; height: 60px" :src="imgUrlPrefixAdd + scope.row.productImg"></el-image>-->
<!--                        </template>-->
<!--                    </el-table-column>-->
                    <el-table-column prop="qty" label="数量" width=""></el-table-column>
                    <!--                    <el-table-column prop="maxPrice" label="最高单价" width="">-->
                    <!--                        <template v-slot="scope">-->
                    <!--                            <el-input-number-->
                    <!--                                :controls="false"-->
                    <!--                                :precision="2"-->
                    <!--                                v-model="scope.row.maxPrice"-->
                    <!--                                ></el-input-number>-->
                    <!--                        </template>-->
                    <!--                    </el-table-column>-->
                    <el-table-column  v-if="formData.billType == 1" prop="bidNetPrice" label="网价" width="180">
                        <template v-slot="scope">
                            <el-input-number
                                :controls="false"
                                :precision="2"
                                v-model="scope.row.bidNetPrice"
                            ></el-input-number>
                        </template>
                    </el-table-column>
                    <el-table-column prop="spec" label="规格" width="200px"></el-table-column>
                    <el-table-column prop="texture" label="材质" width=""></el-table-column>
                    <el-table-column prop="unit" label="计量单位" width=""></el-table-column>
                    <el-table-column prop="gmtCreate" label="创建时间" width="160"></el-table-column>
                </el-table>
            </div>
            <div class="buttons">
                <el-button class="btn-blue" @click="createBidingM">生成竞价</el-button>
                <el-button @click="showBidingForm = false">返回</el-button>
            </div>
        </el-dialog>
        <el-dialog v-dialogDrag title="选择供应商" id="supplierDialog2"  v-loading="showTableLoading" heigh :visible.sync="showSupplierList" width="80%" >
            <div style="display: flex;flex-direction: row;justify-content: space-between">
                <div class="box-left" style="max-width: 100%">
                    <div class="e-table" style="background-color: #fff">
                        <div class="top" style="height: 50px">
                            <div class="left-btn1">
                            </div>
                            <div class="search_box">
                                <el-input style="" clearable type="text" @keyup.enter.native="getshopLists" placeholder="输入搜索关键字" v-model="shopList.keywords">
                                    <img src="@/assets/search.png" slot="suffix" @click="getshopLists" alt=""/>
                                </el-input>
                            </div>
                            <span style="color: #f1083b">双击选择供应商！</span>
                        </div>
                        <el-table
                            ref="tableShop1"
                            highlight-current-row border
                            :data="shopList.tableData"
                            class="table"
                            :height="rightTableHeight"
                            @row-dblclick="handleCurrentInventoryClickShop"
                        >
                            <el-table-column label="序号" type="index" width="60"/>
                            <el-table-column prop="enterpriseName" width="" label="企业名称"/>

                        </el-table>
                        <Pagination
                            v-show="shopList.tableData || shopList.tableData.length > 0"
                            :total="shopList.paginationInfo.total"
                            :pageSize.sync="shopList.paginationInfo.pageSize"
                            :currentPage.sync="shopList.paginationInfo.currentPage"
                            @currentChange="currentChange"
                            @sizeChange="sizeChange"
                        />
                    </div>
                </div>
                <div class="box-right" style="width: 100%">
                    <div class="e-table">
                        <div class="top" style="height: 50px">
                            <span style="color: #ea083a">双击移除供应商！</span>
                        </div>
                        <!--                                            @selection-change="handleSelectionChangeShop"
                                                @row-click="handleCurrentInventoryClickShop"-->
                        <el-table
                            ref="tableShop2"
                            highlight-current-row border
                            :data="selectedSupplierList"
                            class="table"
                            :height="rightTableHeight"
                            @row-dblclick="handleRemoveCurrentClickShop"
                        >
                            <!--                            <el-table-column type="selection" header-align="center" align="center" width="50"/>-->
                            <el-table-column label="序号" type="index" width="60"/>
                            <el-table-column prop="enterpriseName" width="" label="企业名称"/>

                        </el-table>
                    </div>
                </div>
            </div>
            <span slot="footer">
                <el-button type="primary" style="margin-top: 20px" @click="confirmSupplierDialog">确定</el-button>
                <el-button style="margin-top: 20px" @click="closeSupplierDialog">取消</el-button>
            </span>
        </el-dialog>
        <el-dialog title="已中标明细" id="hitBidDialog" :visible.sync="showHitBidForm" width="80%" v-dialogDrag >
            <div class="e-table">
            <el-table
                border
                style="width: 100%"
                :data="hitBidData.hitBidList"
                class="table"
                :max-height="$store.state.tableHeight"
            >
                <el-table-column label="序号" type="index" width="60"></el-table-column>
                <el-table-column prop="productName" label="商品名称"></el-table-column>
                <el-table-column prop="spec" label="规格型号"></el-table-column>
                <el-table-column prop="productTexture" label="商品材质"></el-table-column>
                <el-table-column prop="unit" label="计量单位"></el-table-column>
                <el-table-column prop="num" label="数量"></el-table-column>
                <el-table-column v-if="formData.billType === 1" label="网价" prop="netPrice" width="100"/>
                <el-table-column v-if="formData.billType === 1"  label="固定费" prop="fixationPrice" width="100"/>
                <el-table-column v-if="formData.billType === 2"  label="出厂价" prop="outFactoryPrice" width="100"/>
                <el-table-column v-if="formData.billType === 2"  label="运杂费" prop="transportPrice" width="100"/>
                <el-table-column prop="bidRatePrice" label="含税到场单价"></el-table-column>
                <el-table-column prop="bidPrice" label="不含税到场单价"></el-table-column>
                <el-table-column prop="bidRateAmount" label="含税总金额"></el-table-column>
                <el-table-column prop="bidAmount" label="不含税总金额"></el-table-column>
                <el-table-column prop="taxRate" label="税率"></el-table-column>
                <el-table-column prop="remarks" label="报价方备注" width="160"></el-table-column>
            </el-table>
            <div class="buttons">
                <el-button @click="goToBidDetail(hitBidData.biddingSn)" type="primary">查看竞价详情</el-button>
                <el-button @click="showHitBidForm = !showHitBidForm">关闭</el-button>
            </div>
            </div>
        </el-dialog>
    </div>
</template>

<script>
// eslint-disable-next-line no-unused-vars
import { toFixed } from '@/utils/common'
import '@/utils/jquery.scrollTo.min'
import $ from 'jquery'
import { throttle } from '@/utils/common'
import {
    auditBusinessST,
    batchUpdateItems,
    auditRefuseOrg,
    supplierDeleteInfo,
    suppliersSynthesizeTemporaryGetBySn
} from '@/api/frontStage/userCenter'
import { mapState } from 'vuex'
import editor from '@/components/quillEditor.vue'
import { getSupplierShopsList } from '@/api/platform/supplier/supplierAudit'
import {  createBidingByTemporary } from '@/api/platform/order/orders'
import Pagination from '@/components/pagination/pagination.vue'

export default {
    components: {
        editor,
        Pagination, },

    data () {
        return {
            formRoles: {
                outPhaseInterest: [
                    { required: true, message: '请输入超期垫资利息（%）', trigger: 'blur' },
                ],
            },
            formLoading: false,
            keywords: null,
            //基本信息表单数据
            formData: {},
            // 表格数据
            tableData: [],
            paginationInfo: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            tabsName: 'baseInfo',
            screenWidth: 0,
            screenHeight: 0,
            lastConHeight: 0,
            clickTabFlag: false, // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            winEvent: {},
            topHeight: 120,
            changedRow: [],
            changedRowNum: [],
            tabArr: [],
            tableLoading: false,
            pickerOptions: {
                disabledDate (time) {
                    return time.getTime() < Date.now()
                },
            },
            synthesizeTemporaryDtl: [],
            bidingForm: {
                biddingSourceType: 3,
                title: null,
                type: 1,
                endTime: null,
                linkName: null,
                linkPhone: null,
                biddingExplain: null,
                synthesizeTemporaryDtlList: [],
                productType: 2,
                suppliers: [],
                biddingNotice: `1.价格与数量：1）表中“到场含税单价”为材料送达供货地点的含税单价，其包含但不限于材料费、加工费、运杂费、装车、捆扎、包装、利润、管理费、安全费、税费等一切费用（不含卸车费）。2）表中数量、金额均为暂估，结算以实际供应并经双方签认的数量和金额为准。3）发票开具均采用一票制，税率为13%。
2.质量技术要求：1）卖方所供物资必须满足国家、行业相关标准文件规定以及项目部施工设计文件要求，若经抽检不合格的，卖方须按2万元/次向买方支付违约金并承担因此给买方造成的一切损失，以上款项买方有权在结算款和履约保证金中直接扣除，若不足以弥补损失的，卖方须予以补足，同时买方有权将违约供应商列入四川路桥供应商黑名单；2）其他要求：。
3.结算与支付：先货后款，卖方全部货物到场后3日内，需持双方收料签字确认的对账单同买方完成对账（具体双方协商），买方在收到卖方提供的合法合规且符合买方财务要求的发票和对账单后，5日内完成上账，90日内以银行转账的方式支付当期货款。
4.竞价单报送要求：请报价单位将线上完成填报的竞价单导出打印，盖章后以PDF彩色扫描件上传至竞价专区，标题以“报价单位名称+左上角竞价单编号末尾5位数字”命名，报价截止时间为'{year}'年'{month}'月'{day}'日'{minutes}'分。
5.报价的否决：报价单位发生以下情形之一，其报价将被否决：1）未在规定时间内按竞价要求报价的；2）未上传清晰的竞价单盖章扫描件的，或扫描件内容与平台线上填报的内容存在实质性差异的；3）报价明显不合理的，低于成本的；4）报价单位因近期在合同履约、材料质量、供应服务等方面出现问题，已被买方取消或暂停竞价参与资格的；5）其他未能实质性响应本次竞价或违反买方有关规定的情形。
6.竞价规则：1）报价单位在报价截止时间前，按竞价要求一次报出不可更改的价格；2）买方将于报价截止时间后，通过平台开启竞价，报价符合竞价要求且价格最低的供应商拟推荐为成交候选人，最终由买方采购领导小组审批确定。
7.其他说明与附件：1）本竞价单未尽事宜，按照双方签订的商城准入协议以及平台相关规定执行；2）本次竞价附件（若有）：（示例：XX项目XX材料设计图纸文件）；3）其他说明（若有）：`
            },
            bidingFormRules: {
                title: [
                    { required: true, message: '请输入标题', trigger: 'blur' },
                    { min: 1, max: 200, message: '超过限制', trigger: 'blur' }
                ],
                endTime: [
                    { required: true, message: '请选择截止时间', trigger: 'blur' },
                ],
                linkPhone: [
                    { required: false, message: '请输入11位手机号', trigger: 'blur' },
                    { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' },
                ],
            },
            bidingFormLoading: false,
            showBidingForm: false,
            showTableLoading: false,
            showSupplierList: false,
            shopList: {
                tableData: [],
                keywords: null,
                paginationInfo: { // 分页
                    total: 200,
                    pageSize: 20,
                    currentPage: 1,
                },
            },
            // 邀请竞价
            // 第二个选择框的供应商
            selectedSupplierList: [],
            materialSelectRow: [],
            // 选中供应商
            selectedSupplierRow: [],
            // 选中供应商名称
            selectedSupplierRowName: [],
            // 中标数据
            hitBidData: {
                biddingSn: null,
                hitBidList: [],
            },
            showHitBidForm: false
        }
    },
    created () {
        this.findByDataSnM()
    },
    mounted () {
        // 保存所有tabName
        const arr = ['baseInfo', 'productInfo']
        this.tabArr = arr
        let $idsTop = []
        const onScroll = () => {
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            if (this.clickTabFlag) {
                return
            }
            if (!$idsTop[$idsTop.length - 1]) {
                $idsTop = arr.map(item => {
                    const $item = document.getElementById(item)
                    let itemTop = null
                    if ($item) {
                        itemTop = $item.offsetTop
                    }
                    return itemTop
                })
            }
            const scrollTop = $('#tabs-content')[0].scrollTop
            // 倒序查找
            let curLocal = 0
            for (let i = $idsTop.length - 1; i >= 0; i--) {
                let item = $idsTop[i]
                if (scrollTop + 1 >= item) {
                    curLocal = i
                    break
                }
            }
            // 设置对应tabName
            this.tabsName = arr[curLocal]
        }
        this.winEvent.onScroll = onScroll
        $('#tabs-content')[0].addEventListener('scroll', this.winEvent.onScroll)
        this.screenWidth = document.documentElement.clientWidth - this.topHeight
        this.screenHeight = document.documentElement.clientHeight - this.topHeight
        const onResize = () => {
            this.screenWidth = document.documentElement.clientWidth - this.topHeight
            this.screenHeight = document.documentElement.clientHeight - this.topHeight
            $idsTop = arr.map(item => {
                const itemTop = document.getElementById(item).offsetTop
                return itemTop
            })
        }
        this.winEvent.onResize = throttle(onResize, 500)
        window.addEventListener('resize', this.winEvent.onResize)
        if (this.$route.query.name) {
            setTimeout(() => {
                this.onChangeTab({
                    name: this.$route.query.name
                })
                this.tabsName = this.$route.query.name
            }, 200)
        }
    },
    beforeDestroy () {
        window.removeEventListener('scroll', this.winEvent.onScroll)
        window.removeEventListener('resize', this.winEvent.onResize)
    },
    computed: {
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 292
        },
        ...mapState({
            options: state => state.contract.ctClassify,
            userInfo: state => state.userInfo,
        }),
        // tab内容高度
        tabsContentHeight () {
            return this.screenHeight - 140 + 'px'
        },
    },
    watch: {
        'bidingForm.endTime': {
            handler () {
                this.formatStr()
                this.$forceUpdate()
            }
        },
        screenHeight: {
            handler (newVal) {
                $('#tabs-content').height(newVal - 71)
            }
        },
    },
    methods: {
        gotoBidding (row) {
            if (row.biddingProduct != null && row.bidRecordItem != null) {
                this.hitBidData.hitBidList = []
                // 说明有中标数据
                let allData = {
                    ...row.biddingProduct,
                    ...row.bidRecordItem
                }
                console.log('allData', allData)
                this.hitBidData.biddingSn = row.biddingSn
                this.hitBidData.hitBidList.push(allData)
                this.showHitBidForm = true
                // console.log('allDataList', this.hitBidData.hitBidList)
                return
            }
            this.goToBidDetail(row.biddingSn)
        },
        goToBidDetail (biddingSn) {
            this.$router.push({
                path: '/supplierSys/bidManage/bidingListDetail',
                name: 'bidingListDetail',
                query: {
                    biddingSn: biddingSn
                } })
        },
        formatStr () {
            let year = this.bidingForm.endTime.substring(0, 4)
            let month = this.bidingForm.endTime.substring(5, 7)
            let day = this.bidingForm.endTime.substring(8, 10)
            let minutes = this.bidingForm.endTime.substring(11, 16)
            if (this.bidingForm.billType === 2) {

                this.bidingForm.biddingNotice =                     `1.价格与数量：1）表中“到场含税单价”为材料送达供货地点的含税单价，其包含但不限于材料费、加工费、运杂费、装车、捆扎、包装、利润、管理费、安全费、税费等一切费用（不含卸车费）。2）表中数量、金额均为暂估，结算以实际供应并经双方签认的数量和金额为准。3）发票开具均采用一票制，税率为13%。
2.质量技术要求：1）卖方所供物资必须满足国家、行业相关标准文件规定以及项目部施工设计文件要求，若经抽检不合格的，卖方须按2万元/次向买方支付违约金并承担因此给买方造成的一切损失，以上款项买方有权在结算款和履约保证金中直接扣除，若不足以弥补损失的，卖方须予以补足，同时买方有权将违约供应商列入四川路桥供应商黑名单；2）其他要求：。
3.结算与支付：先货后款，卖方全部货物到场后3日内，需持双方收料签字确认的对账单同买方完成对账（具体双方协商），买方在收到卖方提供的合法合规且符合买方财务要求的发票和对账单后，5日内完成上账，90日内以银行转账的方式支付当期货款。
4.竞价单报送要求：请报价单位将线上完成填报的竞价单导出打印，盖章后以PDF彩色扫描件上传至竞价专区，标题以“报价单位名称+左上角竞价单编号末尾5位数字”命名，报价截止时间为${year}年${month}月${day}日${minutes}分。
5.报价的否决：报价单位发生以下情形之一，其报价将被否决：1）未在规定时间内按竞价要求报价的；2）未上传清晰的竞价单盖章扫描件的，或扫描件内容与平台线上填报的内容存在实质性差异的；3）报价明显不合理的，低于成本的；4）报价单位因近期在合同履约、材料质量、供应服务等方面出现问题，已被买方取消或暂停竞价参与资格的；5）其他未能实质性响应本次竞价或违反买方有关规定的情形。
6.竞价规则：1）报价单位在报价截止时间前，按竞价要求一次报出不可更改的价格；2）买方将于报价截止时间后，通过平台开启竞价，报价符合竞价要求且价格最低的供应商拟推荐为成交候选人，最终由买方采购领导小组审批确定。
7.其他说明与附件：1）本竞价单未尽事宜，按照双方签订的商城准入协议以及平台相关规定执行；2）本次竞价附件 3）其他说明（若有）：`

                //                 `说明：
                // 1、本表中各材料数量、金额为暂估数量，结算以实际供应并经双方签认的数量和金额为准。
                // 2、“到场含税单价”为材料到达供货地点的含税单价，包含但不限于材料费、运杂费、装车、捆扎、利润、管理费、安全费、税费等一切费用（卸车由买方负责并承担相关费用）；
                //     3、材料执行标准：满足国家、行业相关标准文件规定以及项目施工设计文件要求
                //     4、发票开具均采用一票制。
                //     5、结算与支付方式：先货后款，卖方全部货物到场后需持双方收料签字确认的对账单与买方3日内完成对账（有特殊情况需双方协商），甲方在收到乙方提供的符合甲方财务要求的发票和对账单后5天内完成上账，60天内以银行转账的方式支付当期货款。
                //     6、竞价函报送要求：请将线上完成填报的竞价函导出打印，签字盖章后以PDF彩色扫描件上传至竞价专区，标题以“公司名称+**材料竞价”命名，报价截止时间为${year}年${month}月${day}日${minutes}分，逾期送达的视为报价无效。
                //     7、竞价函比价：竞价发起单位将于报价截止时间后组织比价，最终根据比价情况，由竞价发起单位采购领导小组选择确定供应商。
                //     8、其他说明：`
            }else {
                //                 this.bidingForm.biddingNotice = `说明：
                // 备注：
                // 1、本表中各材料数量、金额为暂估数量，结算以实际供应并经双方签认的数量和金额为准。
                // // 2、网价参照《我的钢铁网》“成都市场”2023年12月20日流体管对应规格型号公布的最低价格报价（如无该规格型号报价，则选取相应报价），结算以到货当日对应规格型号网价+固定费，若到货当日无网价，按照到货当日接近日期最低网价执行；“到场含税单价”为材料到达供货地点的含税单价，包含但不限于材料费、运杂费、装车、捆扎、利润、管理费、安全费、税费等一切费用（卸车由买方负责并承担相关费用）；
                // 3、材料执行标准：满足国家、行业相关标准文件规定以及项目施工设计文件要求按照国家最新的标准验收并符合招标人设计图纸要求，供方提供加盖印章的材质证明。
                // 4、发票开具均采用一票制。
                // 5、结算与支付方式：先货后款，卖方全部货物到厂后需持双方收料签字确认的对账单与买方3日内完成对账（有特殊情况需双方协商），对账完成后乙方开具有效增值税专用发票，甲方收到增值税专用发票60个工作日内（有特殊情况需双方协商）向乙方付清已开票金额。
                // 6、竞价函报送要求：请将完成填报的竞价函PDF彩色扫描件加密后（文档密码由报价方妥善保管）发送至四川路桥建设集团股份有限公司物资分公司企业邮箱**************，邮件标题以“公司名称+电话号码”命名，报价截止时间为${year}年${month}月${day}日${minutes}分，电子邮件逾期送达的视为报价无效。
                // 7、竞价函启封比价：竞价发起单位将于报价截止时间后组织启封比价。届时，竞价发起单位将根据邮件标题预留电话，联系报价单位询问文件密码启封竞价函，请报价方保持电话畅通；最终根据比价情况，由竞价发起单位采购领导小组选择确定供应商。
                // 8、报价方申明（若有必要）：`
                this.bidingForm.biddingNotice = `说明：
        1、本表中各材料数量、金额为暂估数量，结算以实际供应并经双方签认的数量和金额为准。
        2、“到场含税单价”为材料到达供货地点的含税单价，包含但不限于材料费、运杂费、装车、捆扎、利润、管理费、安全费、税费等一切费用（卸车由买方负责并承担相关费用）；
        3、材料执行标准：满足国家、行业相关标准文件规定以及项目施工设计文件要求
        4、发票开具均采用一票制。
        5、结算与支付方式：先货后款，卖方全部货物到场后需持双方收料签字确认的对账单与买方3日内完成对账（有特殊情况需双方协商），甲方在收到乙方提供的符合甲方财务要求的发票和对账单后5天内完成上账，60天内以银行转账的方式支付当期货款。
        6、竞价函报送要求：请将线上完成填报的竞价函导出打印，签字盖章后以PDF彩色扫描件上传至竞价专区，标题以“公司名称+**材料竞价”命名，报价截止时间为${year}年${month}月${day}日${minutes}，逾期送达的视为报价无效。
        7、竞价函比价：竞价发起单位将于报价截止时间后组织比价，最终根据比价情况，由竞价发起单位采购领导小组选择确定供应商。
        8、其他说明：`
            }

        },
        handleSelectedAble (row) {
            console.log(row)
            if (row.isBidding === 1) {
                return false
            } else {
                return true
            }
        },
        confirmSupplierDialog () {
            // 循环已选择的供应商列表
            this.selectedSupplierRow = this.selectedSupplierList || []
            this.selectedSupplierRowName = this.selectedSupplierRow.map(item=>{
                return   { enterpriseId: item.enterpriseId, enterpriseName: item.enterpriseName, disabled: true }
            })
            this.bidingForm.suppliers = this.selectedSupplierRow.map(item=>{
                return   { supplierId: item.enterpriseId, supplierName: item.enterpriseName, disabled: true }
            }) || []
            this.infoStr = '已选' + this.selectedSupplierRow.length + '个供应商'
            this.showSupplierList = false
        },
        closeSupplierDialog () {
            this.showSupplierList = false
            this.selectedSupplierList = []
        },
        // 取消选择
        handleRemoveCurrentClickShop (row) {
            // 从已选择的供应商数组中移除该供应商
            this.selectedSupplierList = this.selectedSupplierList.filter(t => t.enterpriseId != row.enterpriseId)
        },
        createBidingM () {
            this.$refs.bidingFormRef.validate(valid => {
                if (valid) {
                    // 邀请竞价必须选择供应商
                    if (this.bidingForm.type === 2) {
                        if (this.bidingForm.suppliers == null || this.bidingForm.suppliers.length == 0) {
                            return this.$message.error('请选择供应商！')
                        }
                    }
                    // 浮动价格必须先填写网价
                    if (this.bidingForm.billType === 1) {
                        let arr = this.bidingForm.synthesizeTemporaryDtlList.filter(item =>{
                            return item.bidNetPrice == '' || Number(item.bidNetPrice) == 0
                        })
                        if (arr.length > 0) {
                            return this.$message.error('商品【' + arr[0].productName + '】未填写网价')
                        }
                    }
                    this.clientPop('info', '您确定要生成竞价吗！', async () => {
                        this.bidingFormLoading = true
                        this.bidingForm.billType = this.formData.billType
                        createBidingByTemporary(this.bidingForm).then(res => {
                            if (res) {
                                this.$message.success('生成成功')
                                this.findByDataSnM()
                                this.showBidingForm = false
                                this.bidingForm = {
                                    biddingSourceType: 3,
                                    title: null,
                                    type: 1,
                                    endTime: null,
                                    linkName: null,
                                    linkPhone: null,
                                    biddingExplain: null,
                                    orderItems: [],
                                    productType: 2,
                                }
                            }
                        }).finally(() => {
                            this.bidingFormLoading = false
                            this.showBidingForm = false
                        })
                    })
                } else {
                    this.$message({
                        message: '请检查非空输入框',
                        type: 'error'
                    })
                }
            })
        },

        sizeChange () {
            this.getshopLists()
        },
        currentChange () {
            this.getshopLists()
        },
        // 双击选择
        handleCurrentInventoryClickShop (row) {
            // 根据id排除已有的供应商
            for (let i = 0; i < this.selectedSupplierList.length; i++) {
                let t = this.selectedSupplierList[i]
                if(t.enterpriseId == row.enterpriseId) {
                    return this.$message.warning('该供应商已选择！')
                }
            }
            this.selectedSupplierList.push(row)
            this.$message.success('选择成功！')

        },
        handleSelectionChangeShop (list) {
            this.selectedSupplierRow = list || []
        },
        getshopLists () {
            let params = {
                page: this.shopList.paginationInfo.currentPage,
                limit: this.shopList.paginationInfo.pageSize,
                orderBy: 1,
            }
            if (this.shopList.keywords != null) {
                params.keywords = this.shopList.keywords
            }
            this.inventoryTableLoading = true
            getSupplierShopsList(params).then(res => {
                this.shopList.tableData = res.list || []
                this.shopList.paginationInfo.currentPage = res.currPage
                this.shopList.paginationInfo.pageSize = res.pageSize
                this.shopList.paginationInfo.total = res.totalCount
            })
            this.inventoryTableLoading = false
        },
        async showSupplierDialog () {
            await this.getshopLists ()
            this.showSupplierList = true
        },
        createSynthesizeTemporaryBid () {
            console.log(this.bidingForm.billType, 'this.bidingForm.billType')
            if (this.synthesizeTemporaryDtl.length === 0) {
                this.$message.error('请选择要生成竞价单的商品')
                return
            }
            if (this.bidingForm.billType === 2) {
                //                 this.bidingForm.biddingNotice = `说明：
                // 备注：
                // 1、本表中各材料数量、金额为暂估数量，结算以实际供应并经双方签认的数量和金额为准。
                // 2、“到场含税单价”为材料到达供货地点的含税单价，包含但不限于材料费、运杂费、装车、捆扎、利润、管理费、安全费、税费等一切费用（卸车由买方负责并承担相关费用）；
                // 3、材料执行标准：满足国家、行业相关标准文件规定以及项目施工设计文件要求按照国家最新的标准验收并符合招标人设计图纸要求，供方提供加盖印章的材质证明。
                // 4、发票开具均采用一票制。
                // 5、结算与支付方式：先货后款，卖方全部货物到厂后需持双方收料签字确认的对账单与买方3日内完成对账（有特殊情况需双方协商），对账完成后乙方开具有效增值税专用发票，甲方收到增值税专用发票60个工作日内（有特殊情况需双方协商）向乙方付清已开票金额。
                // 6、竞价函报送要求：请将完成填报的竞价函PDF彩色扫描件加密后（文档密码由报价方妥善保管）发送至四川路桥建设集团股份有限公司物资分公司企业邮箱**************，邮件标题以“公司名称+电话号码”命名，报价截止时间为xxxx年xx月xx日xx:xx分，电子邮件逾期送达的视为报价无效。
                // 7、竞价函启封比价：竞价发起单位将于报价截止时间后组织启封比价。届时，竞价发起单位将根据邮件标题预留电话，联系报价单位询问文件密码启封竞价函，请报价方保持电话畅通；最终根据比价情况，由竞价发起单位采购领导小组选择确定供应商。
                // 8、报价方申明（若有必要）：`
                this.bidingForm.biddingNotice = `1.价格与数量：1）表中“到场含税单价”为材料送达供货地点的含税单价，其包含但不限于材料费、加工费、运杂费、装车、捆扎、包装、利润、管理费、安全费、税费等一切费用（不含卸车费）。2）表中数量、金额均为暂估，结算以实际供应并经双方签认的数量和金额为准。3）发票开具均采用一票制，税率为13%。
2.质量技术要求：1）卖方所供物资必须满足国家、行业相关标准文件规定以及项目部施工设计文件要求，若经抽检不合格的，卖方须按2万元/次向买方支付违约金并承担因此给买方造成的一切损失，以上款项买方有权在结算款和履约保证金中直接扣除，若不足以弥补损失的，卖方须予以补足，同时买方有权将违约供应商列入四川路桥供应商黑名单；2）其他要求：（示例：卖方供应物资必须满足以下具体技术指标：抗拉强度≥80KN、断裂（屈服）延伸率≤5%，或符合附件图纸要求）。
3.结算与支付：先货后款，卖方全部货物到场后3日内，需持双方收料签字确认的对账单同买方完成对账（具体双方协商），买方在收到卖方提供的合法合规且符合买方财务要求的发票和对账单后，5日内完成上账，90日内以银行转账的方式支付当期货款。
4.竞价单报送要求：请报价单位将线上完成填报的竞价单导出打印，盖章后以PDF彩色扫描件上传至竞价专区，标题以“报价单位名称+左上角竞价单编号末尾5位数字”命名，报价截止时间为'{year}'年'{month}'月'{day}'日'{minutes}'分。
5.报价的否决：报价单位发生以下情形之一，其报价将被否决：1）未在规定时间内按竞价要求报价的；2）未上传清晰的竞价单盖章扫描件的，或扫描件内容与平台线上填报的内容存在实质性差异的；3）报价明显不合理的，低于成本的；4）报价单位因近期在合同履约、材料质量、供应服务等方面出现问题，已被买方取消或暂停竞价参与资格的；5）其他未能实质性响应本次竞价或违反买方有关规定的情形。
6.竞价规则：1）报价单位在报价截止时间前，按竞价要求一次报出不可更改的价格；2）买方将于报价截止时间后，通过平台开启竞价，报价符合竞价要求且价格最低的供应商拟推荐为成交候选人，最终由买方采购领导小组审批确定。
7.其他说明与附件：1）本竞价单未尽事宜，按照双方签订的商城准入协议以及平台相关规定执行；2）本次竞价附件 3）其他说明（若有）： `

            }else {
                //                 this.bidingForm.biddingNotice = `说明：
                // 备注：
                // 1、本表中各材料数量、金额为暂估数量，结算以实际供应并经双方签认的数量和金额为准。
                // 2、网价参照《我的钢铁网》“成都市场”2023年12月20日流体管对应规格型号公布的最低价格报价（如无该规格型号报价，则选取相应报价），结算以到货当日对应规格型号网价+固定费，若到货当日无网价，按照到货当日接近日期最低网价执行；“到场含税单价”为材料到达供货地点的含税单价，包含但不限于材料费、运杂费、装车、捆扎、利润、管理费、安全费、税费等一切费用（卸车由买方负责并承担相关费用）；
                // 3、材料执行标准：满足国家、行业相关标准文件规定以及项目施工设计文件要求按照国家最新的标准验收并符合招标人设计图纸要求，供方提供加盖印章的材质证明。
                // 4、发票开具均采用一票制。
                // 5、结算与支付方式：先货后款，卖方全部货物到厂后需持双方收料签字确认的对账单与买方3日内完成对账（有特殊情况需双方协商），对账完成后乙方开具有效增值税专用发票，甲方收到增值税专用发票60个工作日内（有特殊情况需双方协商）向乙方付清已开票金额。
                // 6、竞价函报送要求：请将完成填报的竞价函PDF彩色扫描件加密后（文档密码由报价方妥善保管）发送至四川路桥建设集团股份有限公司物资分公司企业邮箱**************，邮件标题以“公司名称+电话号码”命名，报价截止时间为xxxx年xx月xx日xx:xx分，电子邮件逾期送达的视为报价无效。
                // 7、竞价函启封比价：竞价发起单位将于报价截止时间后组织启封比价。届时，竞价发起单位将根据邮件标题预留电话，联系报价单位询问文件密码启封竞价函，请报价方保持电话畅通；最终根据比价情况，由竞价发起单位采购领导小组选择确定供应商。
                // 8、报价方申明（若有必要）：`
                this.bidingForm.biddingNotice =  `说明：
        1、本表中各材料数量、金额为暂估数量，结算以实际供应并经双方签认的数量和金额为准。
        2、“到场含税单价”为材料到达供货地点的含税单价，包含但不限于材料费、运杂费、装车、捆扎、利润、管理费、安全费、税费等一切费用（卸车由买方负责并承担相关费用）；
        3、材料执行标准：满足国家、行业相关标准文件规定以及项目施工设计文件要求
        4、发票开具均采用一票制。
        5、结算与支付方式：先货后款，卖方全部货物到场后需持双方收料签字确认的对账单与买方3日内完成对账（有特殊情况需双方协商），甲方在收到乙方提供的符合甲方财务要求的发票和对账单后5天内完成上账，60天内以银行转账的方式支付当期货款。
        6、竞价函报送要求：请将线上完成填报的竞价函导出打印，签字盖章后以PDF彩色扫描件上传至竞价专区，标题以“公司名称+**材料竞价”命名，报价截止时间为xxxx年xx月xx日xx:xx分，逾期送达的视为报价无效。
        7、竞价函比价：竞价发起单位将于报价截止时间后组织比价，最终根据比价情况，由竞价发起单位采购领导小组选择确定供应商。
        8、其他说明：`
            }
            //  提交生成草稿竞价单
            this.showBidingForm = true
            this.bidingForm.billType = this.formData.billType
            this.bidingForm.synthesizeTemporaryDtlList = this.synthesizeTemporaryDtl
        },
        handleSelectDtl (val) {
            if (Array.isArray(val)) {
                // 移除isBidding为1的数据
                val = val.filter(t => t.isBidding != 1)
            }
            this.synthesizeTemporaryDtl = val
        },
        auditPlanM (state, title) {
            this.clientPop('info', '您确定进行【' + title + '】操作吗？', async () => {
                if (state == 0) {
                    this.$prompt('未通过原因', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'error',
                        inputType: 'textarea',
                        inputPlaceholder: '请输入不通过原因',
                        inputPattern: /^.+$/,
                        inputErrorMessage: '请输入不通过原因'
                    }).then(({ value }) => {
                        let params = {
                            id: this.formData.synthesizeTemporaryId,
                            isOpen: 0,
                            auditResult: value
                        }
                        this.formLoading = true
                        auditBusinessST(params).then(res => {
                            if (res.code != null && res.code == 200) {
                                this.$message.success('操作成功')
                                this.findByDataSnM()
                            }
                        }).finally(() => {
                            this.formLoading = false
                        })
                    }).catch(() => {
                    })
                } else {
                    let params = {
                        id: this.formData.synthesizeTemporaryId,
                        isOpen: 1
                    }
                    this.formLoading = true
                    auditBusinessST(params).then(res => {
                        if (res.code != null && res.code == 200) {
                            this.$message.success('操作成功')
                            this.findByDataSnM()
                        }
                    }).finally(() => {
                        this.formLoading = false
                    })
                }
            })
        },
        auditRefuseM () {
            this.clientPop('info', '您确定是否拒绝该清单？', async () => {
                this.$prompt('拒绝原因', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'error',
                    inputType: 'textarea',
                    inputPlaceholder: '请输入拒绝原因',
                    inputPattern: /^.+$/,
                    inputErrorMessage: '请输入拒绝原因'
                }).then(({ value }) => {
                    let params = {
                        id: this.formData.synthesizeTemporaryId,
                        isOpen: 0,
                        auditResult: value
                    }
                    this.formLoading = true
                    auditRefuseOrg(params).then(res => {
                        if (res.code != null && res.code == 200) {
                            this.$message.success('操作成功')
                            this.findByDataSnM()
                        }
                    }).finally(() => {
                        this.formLoading = false
                    })
                })
            })
        },
        fixed4 (num) {
            return toFixed(num, 4)
        },
        fixed2 (num) {
            return toFixed(num, 2)
        },
        deleteBilM () {
            this.clientPop('info', '您确定要删除吗？', async () => {
                this.formLoading = true
                supplierDeleteInfo({ id: this.formData.synthesizeTemporaryId }).then(res => {
                    if(res.code == 200) {
                        this.$message.success('操作成功！')
                        this.$router.go(-1)
                    }
                }).finally(() => {
                    this.formLoading = false
                })
            })
        },
        batchUpdate (num) {
            for (let i = 0; i < this.formData.dtls.length; i++) {
                let t = this.formData.dtls[i]
                if(this.formData.billType == 1) {
                    if(t.netPrice == null) {
                        return this.$message.error('商品：【' + t.productName + '】未填入网价！')
                    }
                    if(t.fixationPrice == null || t.fixationPrice < 0) {
                        t.fixationPrice = 0
                    }
                }
                if(this.formData.billType == 2) {
                    if(t.outFactoryPrice == null) {
                        return this.$message.error('商品：【' + t.productName + '】未填入出厂价！')
                    }
                    if(t.transportPrice == null || t.transportPrice < 0) {
                        t.transportPrice = 0
                    }
                }
            }
            this.$refs.rulesBase.validate(valid => {
                if(valid) {
                    // 保存
                    this.clientPop('info', '您确认要该操作吗？', async () => {
                        this.tableLoading = true
                        this.formData.dtls[0].outPhaseInterest = this.formData.outPhaseInterest
                        this.formData.dtls[0].isAffirm = num
                        batchUpdateItems(this.formData.dtls).then(res => {
                            if(res.code == 200) {
                                this.$message.success('操作成功！')
                                this.findByDataSnM()
                            }
                        }).finally(() => {
                            this.tableLoading = false
                        })
                    })
                }else {
                    this.$message.error('请检查非空项！')
                }
            })
        },
        countAmountFM (row) {
            row.synthesizePrice = this.fixed2(Number(row.netPrice) + Number(row.fixationPrice))
            this.countTotalAmountM(row)
        },
        countTotalAmountM (row) {
            row.synthesizeAmount = this.fixed2(Number(row.qty) * Number(row.synthesizePrice))
            let amount = 0
            for (let i = 0; i < this.formData.dtls.length; i++) {
                let t = this.formData.dtls[i]
                amount = this.fixed2(Number(amount) +  Number(t.synthesizeAmount))
            }
            this.formData.synthesizeSumAmount = amount
        },
        countAmountGM (row) {
            row.synthesizePrice = this.fixed2(Number(row.outFactoryPrice) + Number(row.transportPrice))
            this.countTotalAmountM(row)
        },
        transportPriceChangeM (row) {
            if(row.transportPrice <= 0 || row.transportPrice == null) {
                return row.transportPrice = 0
            }
            if(row.transportPrice >= 9999999) {
                return row.transportPrice
            }
            row.transportPrice = this.fixed2(row.transportPrice)
            this.countAmountGM(row)
        },
        outFactoryPriceChangeM (row) {
            if(row.outFactoryPrice <= 0 || row.outFactoryPrice == null) {
                return row.outFactoryPrice = 0
            }
            if(row.outFactoryPrice >= 9999999) {
                return row.outFactoryPrice
            }
            row.outFactoryPrice = this.fixed2(row.outFactoryPrice)
            this.countAmountGM(row)
        },
        fixationPriceChangeM (row) {
            if(row.fixationPrice <= 0 || row.fixationPrice == null) {
                return row.fixationPrice = 0
            }
            if(row.fixationPrice >= 9999999) {
                return row.fixationPrice
            }
            row.fixationPrice = this.fixed2(row.fixationPrice)
            this.countAmountFM(row)
        },
        netPriceChangeM (row) {
            if(row.netPrice <= 0 || row.netPrice == null) {
                return row.netPrice = 0
            }
            if(row.netPrice >= 9999999) {
                return row.netPrice
            }
            row.netPrice = this.fixed2(row.netPrice)
            this.countAmountFM(row)
        },
        outPhaseInterestChangeM (row) {
            if(row.outPhaseInterest <= 0 || row.outPhaseInterest == null) {
                return row.outPhaseInterest = 0
            }
            if(row.outPhaseInterest >= 100) {
                return row.outPhaseInterest = 100
            }
            row.outPhaseInterest = parseFloat(row.outPhaseInterest)
        },
        findByDataSnM () {
            this.formLoading = true
            suppliersSynthesizeTemporaryGetBySn({ sn: this.$route.query.sn }).then(res => {
                this.formData = res
            }).finally(() => {
                this.formLoading = false
            })
        },
        //取消
        handleClose () {
            this.$router.go(-1)
        },
        onChangeTab (e) {
            const height = $('#' + e.name).offset().top
            try{
                $('#tabs-content').scrollTo(height - this.topHeight, 500)
            }catch (err) {
                console.log(err)
            }
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            this.clickTabFlag = true
            // 动画结束后，恢复状态
            setTimeout(() => {
                this.clickTabFlag = false
            }, 600)
        },
    }
}
</script>

<style lang='scss' scoped>
.mainTitle {
    box-sizing: border-box;
    width: 100%;
    height: 40px;
    line-height: 40px;
    background: rgb(246, 246, 246);
    border: 1px solid rgb(236, 236, 236);
    margin: auto;
    margin-bottom: 15px;
    padding-left: 10px;
}

.separ {
    width: 30px;
    height: 40px;
    line-height: 18px;
    text-align: center;
}

.e-table {
    min-height: auto;
    background: #fff;
}

.upload {
    margin: 20px auto;
    display: flex;
    justify-content: center;
    text-align: center;
}

.upload-demo {
    display: flex;
    justify-content: center;
    align-items: center;
}

/deep/.el-input--suffix .el-input__inner {
    padding-right: 5px;
}

/deep/ .el-tabs__content {
    // overflow: hidden;
    &::-webkit-scrollbar {width: 0;}
}
/deep/ .el-dialog {
    .el-dialog__body {
        height: 780px;
        margin-top: 0px;
    }
}

/deep/ #supplierDialog {
    .el-dialog__body {
        height: 580px;
        margin-top: 0px;
    }
}
.action {
    margin-right: 10px;
    color: #2e61d7;
    cursor: pointer;
}
::v-deep input::-webkit-outer-spin-button,
::v-deep input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}
::v-deep input[type=‘number’] {
    -moz-appearance: textfield !important;
}
/deep/ #hitBidDialog {
    .el-dialog__body {
        height: 380px;
        margin-top: 0;
    }
}
/deep/ #supplierDialog2 {
    .el-dialog__body {
        height: 600px;
        margin-top: 0px;
    }
}
.remark-content {
  /* 设置最大高度 */
  max-height: 100px;
  /* 设置超出内容滚动 */
  overflow-y: auto;
  /* 设置边框 */
  border: 1px solid #ebeef5;
  /* 设置内边距 */
  padding: 8px;
  /* 设置圆角 */
  border-radius: 4px;
}
</style>
