// import Vue from 'vue'
// import vuex from 'vuex'
import VuexPersist from 'vuex-persist'
import mutations from './mutations'
import actions from './actions'
import selectOptions from './modules/selectOptions'
// import biddingApplication from './modules/biddingApplication'
// import contract from './modules/contract'
// import equip from './modules/equip'
// import outsourcer from './modules/outsourcer'
// import turnover from './modules/turnover'
// import settlementManagement from './modules/settlementManagement'

const vuexLC = new VuexPersist({ storage: window.localStorage })

Vue.use(Vuex)
Vue.config.devtools = true

const store = new Vuex.Store({
    state: {
        // yezi
        materialCity: [], // 物资城市
        materialUnit: [], // 计量单位
        // 其他
        steps: [
            { description: '', },
            { description: '', }
        ],
        enterpriseInfo: {
            socialCreditCode: 'JH4354455652165112',
        },
        cToken: '',
        userInfo: {
            // orgInfo: {},
            // profile: {},
            // baseCyByOrgId: {}, // 本机构设置的本位币
            // baseCy: {}, //本位币信息
        },
        searchForm: {}, // 存储高级搜索条件
        popStatus: '', // 弹窗状态 prompt: 信息弹窗， success: 成功信息弹窗， error: 失败信息弹窗
        popConfirm: false, // 弹窗是否点击确定，如果为true，执行后续业务逻辑
        comPopConfirm: false, //组件弹窗状态
        selectedInfo: null, // 弹窗选中后返回数据
        auditState: {}, //审核相关按钮状态
        auditParams: {}, //审核参数
        historyTableData: [],
        evaluationBasis: {}, // 动态评价存的数据
        annualEvaluationData: {}, // 年度评价存的数据
        menuid: '', //菜单id
        noGetBaseCyByOrgListurl: [
            //合同类
            '/subcontractList',
            '/otherContractList',
            '/serviceProcurementList',
            '/ctRevolvMaterial',
            '/processingContract',
            '/ctMaterialPurchase',
            '/ctEquipmentLease',
            '/ctEquipmentPurchase',
            '/materialSalesContract',
            '/equimentLeaseIncomeContract',
            '/projectContractSearch',
            //基础管理
            '/baseCurrency',
            '/baseAuth',
            '/baseUser',
            '/materialBaseLibManage',
            '/materialPlanRange',
            '/materialUnitConvert',
            '/materialPartBPerson',
            '/materialWarehouse',
            '/reimbursementType',
            '/outsourcerType',
            '/qualificationLevel',
            '/businessScope',
            '/evaluationPeriod',
            '/creditEvaluationList',
            '/biddingMethod',
            '/biddingAnnouncement',
            '/bidWinningNotice',
            '/contractLimitSetting',
            '/paymentMethod',
            '/contractList',
            '/projectProperties',
            '/engineeringGrade',
            '/position',
            '/currency',
            '/timingUnit',
            '/measurementUnit',
            '/subgradeDetailed',
            '/bridgeStructureType',
            '/gasCondition',
            '/otherEngineering',
            '/constructionCategory',
            '/addCategory',
            '/equipDatabase',
            '/categoryManage',
            '/equipTemporaryList',
            '/shiftConversion',
            '/equipPrice',
            '/managementUnit',
            //新增公开招标报名（web端）
            '/openBiddingRegAdd',
            //弹框
            '/switchorg',
            '/exaDialog',
            '/nullifyDialog',
            '/switchorg',
            '/search',
            '/newSelectData',
            //首页
            '/',
            '/todoTop'
        ], //不需要获取本位币的路由
        dialogVisible: false, //是否显示弹框
        setDialogParams: {}, //弹窗参数
        getDialogData: [], //弹框确定按钮返回参数
    },
    actions: actions,
    mutations: mutations,
    modules: {
        selectOptions,
        // biddingApplication,
        // contract,
        // equip,
        // outsourcer,
        // turnover,
        // settlementManagement
    },
    plugins: [vuexLC.plugin],
    tableHeight: 0,
})

export default store
